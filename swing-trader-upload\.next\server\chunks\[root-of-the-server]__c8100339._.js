module.exports = [
"[project]/SwingTrader-AI-Package/.next-internal/server/app/api/paper-trading/execute/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/node:crypto [external] (node:crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}),
"[project]/SwingTrader-AI-Package/src/app/api/paper-trading/execute/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist-node/v4.js [app-route] (ecmascript) <export default as v4>");
;
;
async function POST(request) {
    try {
        const { scanResult, accountSize } = await request.json();
        if (!scanResult || !accountSize) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Scan result and account size are required'
            }, {
                status: 400
            });
        }
        // Determine which setup to use
        const setup = scanResult.overnightSetup || scanResult.breakoutSetup;
        if (!setup) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No trading setup found'
            }, {
                status: 400
            });
        }
        const strategy = scanResult.overnightSetup ? 'overnight_momentum' : 'technical_breakout';
        // Calculate position sizing with risk management
        const entryPrice = setup.entryPrice;
        const stopLoss = setup.stopLoss;
        const riskPerShare = Math.abs(entryPrice - stopLoss);
        // Use 1-2% risk per trade (configurable)
        const riskPercentage = 1.5 // 1.5% of account
        ;
        const riskAmount = accountSize * (riskPercentage / 100);
        // Calculate position size based on risk
        let positionSize = Math.floor(riskAmount / riskPerShare);
        // Ensure we don't exceed reasonable position limits
        const maxPositionValue = accountSize * 0.1 // Max 10% of account per position
        ;
        const maxShares = Math.floor(maxPositionValue / entryPrice);
        positionSize = Math.min(positionSize, maxShares);
        // Minimum position size check
        if (positionSize < 1) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Position size too small - increase account size or reduce risk per share'
            }, {
                status: 400
            });
        }
        // Create paper trade record
        const paperTrade = {
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
            symbol: scanResult.symbol,
            strategy,
            entryPrice,
            stopLoss,
            targets: setup.targets,
            positionSize,
            accountSize,
            riskAmount,
            riskPercentage,
            entryTime: new Date().toISOString(),
            status: 'open',
            currentPrice: scanResult.quote.price,
            unrealizedPnL: 0,
            notes: `${strategy} setup - Score: ${scanResult.overallScore}/100`
        };
        // Calculate potential profit/loss
        const potentialProfit = (setup.targets[0] - entryPrice) * positionSize;
        const potentialLoss = (entryPrice - stopLoss) * positionSize;
        const riskRewardRatio = potentialProfit / Math.abs(potentialLoss);
        const executionSummary = {
            paperTrade,
            executionDetails: {
                symbol: scanResult.symbol,
                strategy,
                entryPrice: entryPrice,
                stopLoss: stopLoss,
                firstTarget: setup.targets[0],
                positionSize,
                positionValue: entryPrice * positionSize,
                riskAmount,
                riskPercentage,
                potentialProfit,
                potentialLoss,
                riskRewardRatio: Math.round(riskRewardRatio * 100) / 100,
                executionTime: new Date().toISOString()
            }
        };
        // In a real application, you would save this to a database
        // For now, we'll return the trade details for client-side storage
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(executionSummary);
    } catch (error) {
        console.error('Paper trading execution error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to execute paper trade'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__c8100339._.js.map
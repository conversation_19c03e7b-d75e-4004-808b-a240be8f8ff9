#!/usr/bin/env node

console.log('🚀 SwingTrader AI - Quick Start');
console.log('===============================');
console.log('');

const os = require('os');
const { execSync } = require('child_process');

const platform = os.platform();

if (platform === 'win32') {
    console.log('Windows detected - running Windows installer...');
    execSync('install-windows.bat', { stdio: 'inherit' });
} else {
    console.log('Mac/Linux detected - running Unix installer...');
    execSync('chmod +x install-mac-linux.sh && ./install-mac-linux.sh', { stdio: 'inherit' });
}

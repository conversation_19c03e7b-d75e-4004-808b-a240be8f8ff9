{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(value)\n}\n\nexport function formatPercentage(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'percent',\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(value / 100)\n}\n\nexport function calculateRiskReward(\n  entryPrice: number,\n  stopLoss: number,\n  takeProfit: number\n): number {\n  const risk = Math.abs(entryPrice - stopLoss)\n  const reward = Math.abs(takeProfit - entryPrice)\n  return reward / risk\n}\n\nexport function calculatePositionSize(\n  accountSize: number,\n  riskPercentage: number,\n  entryPrice: number,\n  stopLoss: number\n): number {\n  const riskAmount = accountSize * (riskPercentage / 100)\n  const riskPerShare = Math.abs(entryPrice - stopLoss)\n  return Math.floor(riskAmount / riskPerShare)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yMAAO,EAAC,IAAA,gLAAI,EAAC;AACtB;AAEO,SAAS,eAAe,KAAa;IAC1C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,QAAQ;AACpB;AAEO,SAAS,oBACd,UAAkB,EAClB,QAAgB,EAChB,UAAkB;IAElB,MAAM,OAAO,KAAK,GAAG,CAAC,aAAa;IACnC,MAAM,SAAS,KAAK,GAAG,CAAC,aAAa;IACrC,OAAO,SAAS;AAClB;AAEO,SAAS,sBACd,WAAmB,EACnB,cAAsB,EACtB,UAAkB,EAClB,QAAgB;IAEhB,MAAM,aAAa,cAAc,CAAC,iBAAiB,GAAG;IACtD,MAAM,eAAe,KAAK,GAAG,CAAC,aAAa;IAC3C,OAAO,KAAK,KAAK,CAAC,aAAa;AACjC", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,0MAAG,EACxB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,2MAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,2MAAI,GAAG;IAC9B,qBACE,6NAAC;QACC,WAAW,IAAA,4JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,2MAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6NAAC;QACC,KAAK;QACL,WAAW,IAAA,4JAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,2MAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6NAAC;QACC,KAAK;QACL,WAAW,IAAA,4JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,2MAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6NAAC;QACC,KAAK;QACL,WAAW,IAAA,4JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,2MAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6NAAC;QACC,KAAK;QACL,WAAW,IAAA,4JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,2MAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6NAAC;QAAI,KAAK;QAAK,WAAW,IAAA,4JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,2MAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6NAAC;QACC,KAAK;QACL,WAAW,IAAA,4JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,IAAA,0MAAG,EACvB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6NAAC;QAAI,WAAW,IAAA,4JAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/SwingScanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, Search, TrendingUp, TrendingDown, Minus, Target, Shield, BarChart3 } from 'lucide-react'\nimport { ScanResult, ScanSummary } from '@/lib/swingScanner'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface ScannerProps {\n  autoScan?: boolean\n}\n\nexport function SwingScanner({ autoScan = false }: ScannerProps) {\n  const [isScanning, setIsScanning] = useState(false)\n  const [scanResults, setScanResults] = useState<ScanSummary | null>(null)\n  const [selectedScan, setSelectedScan] = useState<'quick' | 'full' | 'sector'>('quick')\n  const [selectedSector, setSelectedSector] = useState<string>('Technology')\n  const [error, setError] = useState<string | null>(null)\n\n  const sectors = [\n    'Technology', 'Financial Services', 'Healthcare', 'Industrial', \n    'Materials', 'Consumer', 'Communication Services', 'Energy'\n  ]\n\n  // Auto-scan on component mount if enabled\n  useEffect(() => {\n    if (autoScan) {\n      handleQuickScan()\n    }\n  }, [autoScan])\n\n  const handleQuickScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch('/api/scanner/quick?limit=15')\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      \n      // Convert to ScanSummary format\n      const summary: ScanSummary = {\n        totalScanned: data.totalScanned,\n        successfulScans: data.results.length,\n        failedScans: data.totalScanned - data.results.length,\n        topOpportunities: data.results,\n        sectorBreakdown: {},\n        scanDuration: 0\n      }\n      \n      setScanResults(summary)\n    } catch (err) {\n      setError('Failed to perform quick scan. Please try again.')\n      console.error('Quick scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const handleFullScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch('/api/scanner/full?limit=25&concurrent=3')\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform full scan. Please try again.')\n      console.error('Full scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const handleSectorScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch(`/api/scanner/sector/${encodeURIComponent(selectedSector)}?limit=15`)\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform sector scan. Please try again.')\n      console.error('Sector scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const getTrendIcon = (trend: string) => {\n    switch (trend) {\n      case 'BULLISH':\n        return <TrendingUp className=\"h-4 w-4 text-green-400\" />\n      case 'BEARISH':\n        return <TrendingDown className=\"h-4 w-4 text-red-400\" />\n      default:\n        return <Minus className=\"h-4 w-4 text-yellow-400\" />\n    }\n  }\n\n  const getRecommendationColor = (recommendation: string) => {\n    if (recommendation.includes('BUY')) return 'bg-green-500/20 text-green-400'\n    if (recommendation.includes('SELL')) return 'bg-red-500/20 text-red-400'\n    return 'bg-yellow-500/20 text-yellow-400'\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Scanner Controls */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center\">\n            <Search className=\"mr-2 h-5 w-5 text-blue-400\" />\n            Swing Trading Scanner\n          </CardTitle>\n          <CardDescription className=\"text-slate-300\">\n            Automatically scan stocks for the best swing trading opportunities\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex flex-wrap gap-4 mb-4\">\n            <Button\n              onClick={handleQuickScan}\n              disabled={isScanning}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              {isScanning && selectedScan === 'quick' ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : null}\n              Quick Scan (Top 16)\n            </Button>\n            \n            <Button\n              onClick={handleFullScan}\n              disabled={isScanning}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n            >\n              {isScanning && selectedScan === 'full' ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : null}\n              Full Scan (All 70+ Stocks)\n            </Button>\n\n            <div className=\"flex gap-2\">\n              <select\n                value={selectedSector}\n                onChange={(e) => setSelectedSector(e.target.value)}\n                className=\"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n                disabled={isScanning}\n              >\n                {sectors.map(sector => (\n                  <option key={sector} value={sector}>{sector}</option>\n                ))}\n              </select>\n              <Button\n                onClick={handleSectorScan}\n                disabled={isScanning}\n                variant=\"outline\"\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n              >\n                {isScanning && selectedScan === 'sector' ? (\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                ) : null}\n                Scan Sector\n              </Button>\n            </div>\n          </div>\n\n          {isScanning && (\n            <div className=\"text-center py-4\">\n              <Loader2 className=\"mx-auto h-8 w-8 animate-spin text-blue-400\" />\n              <p className=\"text-slate-300 mt-2\">\n                Scanning stocks for swing trading opportunities...\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Error Display */}\n      {error && (\n        <Card className=\"bg-red-900/20 border-red-500/50\">\n          <CardContent className=\"p-6\">\n            <p className=\"text-red-300 text-center\">{error}</p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Scan Results */}\n      {scanResults && (\n        <div className=\"space-y-6\">\n          {/* Scan Summary */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white\">Scan Summary</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-white\">{scanResults.totalScanned}</div>\n                  <div className=\"text-sm text-slate-300\">Total Scanned</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">{scanResults.successfulScans}</div>\n                  <div className=\"text-sm text-slate-300\">Successful</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-400\">{scanResults.failedScans}</div>\n                  <div className=\"text-sm text-slate-300\">Failed</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">\n                    {scanResults.scanDuration ? `${(scanResults.scanDuration / 1000).toFixed(1)}s` : 'N/A'}\n                  </div>\n                  <div className=\"text-sm text-slate-300\">Duration</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Top Opportunities */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white flex items-center\">\n                <TrendingUp className=\"mr-2 h-5 w-5 text-green-400\" />\n                Top Swing Trading Opportunities\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {scanResults.topOpportunities.map((result, index) => (\n                  <div key={result.symbol} className=\"p-4 bg-slate-700/50 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"text-lg font-bold text-white\">#{result.rank}</div>\n                        <div>\n                          <div className=\"flex items-center space-x-2\">\n                            <span className=\"text-lg font-semibold text-white\">{result.symbol}</span>\n                            {getTrendIcon(result.analysis.trend)}\n                            <Badge className={getRecommendationColor(result.analysis.recommendation)}>\n                              {result.analysis.recommendation.replace('_', ' ')}\n                            </Badge>\n                          </div>\n                          <div className=\"text-sm text-slate-300\">{result.name}</div>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-xl font-bold text-white\">\n                          {result.score.toFixed(1)}/100\n                        </div>\n                        <div className=\"text-sm text-slate-300\">Score</div>\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                      <div>\n                        <div className=\"text-slate-300\">Price</div>\n                        <div className=\"text-white font-semibold\">\n                          {formatCurrency(result.quote.price)}\n                        </div>\n                        <div className={result.quote.change >= 0 ? \"text-green-400\" : \"text-red-400\"}>\n                          {formatPercentage(result.quote.changePercent)}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">Entry</div>\n                        <div className=\"text-blue-400 font-semibold\">\n                          {formatCurrency(result.analysis.entryPrice)}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">R/R Ratio</div>\n                        <div className=\"text-green-400 font-semibold\">\n                          {result.analysis.riskRewardRatio.toFixed(2)}:1\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">Confidence</div>\n                        <div className=\"text-white font-semibold\">\n                          {result.analysis.confidence.toFixed(1)}%\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AARA;;;;;;;AAcO,SAAS,aAAa,KAAkC;QAAlC,EAAE,WAAW,KAAK,EAAgB,GAAlC;;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yMAAQ,EAAC;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yMAAQ,EAAqB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yMAAQ,EAA8B;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yMAAQ,EAAS;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yMAAQ,EAAgB;IAElD,MAAM,UAAU;QACd;QAAc;QAAsB;QAAc;QAClD;QAAa;QAAY;QAA0B;KACpD;IAED,0CAA0C;IAC1C,IAAA,0MAAS;kCAAC;YACR,IAAI,UAAU;gBACZ;YACF;QACF;iCAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB;QACtB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,gCAAgC;YAChC,MAAM,UAAuB;gBAC3B,cAAc,KAAK,YAAY;gBAC/B,iBAAiB,KAAK,OAAO,CAAC,MAAM;gBACpC,aAAa,KAAK,YAAY,GAAG,KAAK,OAAO,CAAC,MAAM;gBACpD,kBAAkB,KAAK,OAAO;gBAC9B,iBAAiB,CAAC;gBAClB,cAAc;YAChB;YAEA,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,oBAAoB;QACpC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB;QACvB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,uBAAyD,OAAnC,mBAAmB,iBAAgB;YACvF,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6NAAC,mQAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6NAAC,yQAAY;oBAAC,WAAU;;;;;;YACjC;gBACE,qBAAO,6NAAC,gPAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,IAAI,eAAe,QAAQ,CAAC,QAAQ,OAAO;QAC3C,IAAI,eAAe,QAAQ,CAAC,SAAS,OAAO;QAC5C,OAAO;IACT;IAEA,qBACE,6NAAC;QAAI,WAAU;;0BAEb,6NAAC,2KAAI;gBAAC,WAAU;;kCACd,6NAAC,iLAAU;;0CACT,6NAAC,gLAAS;gCAAC,WAAU;;kDACnB,6NAAC,mPAAM;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGnD,6NAAC,sLAAe;gCAAC,WAAU;0CAAiB;;;;;;;;;;;;kCAI9C,6NAAC,kLAAW;;0CACV,6NAAC;gCAAI,WAAU;;kDACb,6NAAC,+KAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;;4CAET,cAAc,iBAAiB,wBAC9B,6NAAC,+PAAO;gDAAC,WAAU;;;;;uDACjB;4CAAK;;;;;;;kDAIX,6NAAC,+KAAM;wCACL,SAAS;wCACT,UAAU;wCACV,SAAQ;wCACR,WAAU;;4CAET,cAAc,iBAAiB,uBAC9B,6NAAC,+PAAO;gDAAC,WAAU;;;;;uDACjB;4CAAK;;;;;;;kDAIX,6NAAC;wCAAI,WAAU;;0DACb,6NAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,WAAU;gDACV,UAAU;0DAET,QAAQ,GAAG,CAAC,CAAA,uBACX,6NAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;0DAGjB,6NAAC,+KAAM;gDACL,SAAS;gDACT,UAAU;gDACV,SAAQ;gDACR,WAAU;;oDAET,cAAc,iBAAiB,yBAC9B,6NAAC,+PAAO;wDAAC,WAAU;;;;;+DACjB;oDAAK;;;;;;;;;;;;;;;;;;;4BAMd,4BACC,6NAAC;gCAAI,WAAU;;kDACb,6NAAC,+PAAO;wCAAC,WAAU;;;;;;kDACnB,6NAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,uBACC,6NAAC,2KAAI;gBAAC,WAAU;0BACd,cAAA,6NAAC,kLAAW;oBAAC,WAAU;8BACrB,cAAA,6NAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;YAM9C,6BACC,6NAAC;gBAAI,WAAU;;kCAEb,6NAAC,2KAAI;wBAAC,WAAU;;0CACd,6NAAC,iLAAU;0CACT,cAAA,6NAAC,gLAAS;oCAAC,WAAU;8CAAa;;;;;;;;;;;0CAEpC,6NAAC,kLAAW;0CACV,cAAA,6NAAC;oCAAI,WAAU;;sDACb,6NAAC;4CAAI,WAAU;;8DACb,6NAAC;oDAAI,WAAU;8DAAiC,YAAY,YAAY;;;;;;8DACxE,6NAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,6NAAC;4CAAI,WAAU;;8DACb,6NAAC;oDAAI,WAAU;8DAAqC,YAAY,eAAe;;;;;;8DAC/E,6NAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,6NAAC;4CAAI,WAAU;;8DACb,6NAAC;oDAAI,WAAU;8DAAmC,YAAY,WAAW;;;;;;8DACzE,6NAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,6NAAC;4CAAI,WAAU;;8DACb,6NAAC;oDAAI,WAAU;8DACZ,YAAY,YAAY,GAAG,AAAC,GAA+C,OAA7C,CAAC,YAAY,YAAY,GAAG,IAAI,EAAE,OAAO,CAAC,IAAG,OAAK;;;;;;8DAEnF,6NAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhD,6NAAC,2KAAI;wBAAC,WAAU;;0CACd,6NAAC,iLAAU;0CACT,cAAA,6NAAC,gLAAS;oCAAC,WAAU;;sDACnB,6NAAC,mQAAU;4CAAC,WAAU;;;;;;wCAAgC;;;;;;;;;;;;0CAI1D,6NAAC,kLAAW;0CACV,cAAA,6NAAC;oCAAI,WAAU;8CACZ,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzC,6NAAC;4CAAwB,WAAU;;8DACjC,6NAAC;oDAAI,WAAU;;sEACb,6NAAC;4DAAI,WAAU;;8EACb,6NAAC;oEAAI,WAAU;;wEAA+B;wEAAE,OAAO,IAAI;;;;;;;8EAC3D,6NAAC;;sFACC,6NAAC;4EAAI,WAAU;;8FACb,6NAAC;oFAAK,WAAU;8FAAoC,OAAO,MAAM;;;;;;gFAChE,aAAa,OAAO,QAAQ,CAAC,KAAK;8FACnC,6NAAC,6KAAK;oFAAC,WAAW,uBAAuB,OAAO,QAAQ,CAAC,cAAc;8FACpE,OAAO,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sFAGjD,6NAAC;4EAAI,WAAU;sFAA0B,OAAO,IAAI;;;;;;;;;;;;;;;;;;sEAGxD,6NAAC;4DAAI,WAAU;;8EACb,6NAAC;oEAAI,WAAU;;wEACZ,OAAO,KAAK,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAE3B,6NAAC;oEAAI,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;8DAI5C,6NAAC;oDAAI,WAAU;;sEACb,6NAAC;;8EACC,6NAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,6NAAC;oEAAI,WAAU;8EACZ,IAAA,wKAAc,EAAC,OAAO,KAAK,CAAC,KAAK;;;;;;8EAEpC,6NAAC;oEAAI,WAAW,OAAO,KAAK,CAAC,MAAM,IAAI,IAAI,mBAAmB;8EAC3D,IAAA,0KAAgB,EAAC,OAAO,KAAK,CAAC,aAAa;;;;;;;;;;;;sEAGhD,6NAAC;;8EACC,6NAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,6NAAC;oEAAI,WAAU;8EACZ,IAAA,wKAAc,EAAC,OAAO,QAAQ,CAAC,UAAU;;;;;;;;;;;;sEAG9C,6NAAC;;8EACC,6NAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,6NAAC;oEAAI,WAAU;;wEACZ,OAAO,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAGhD,6NAAC;;8EACC,6NAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,6NAAC;oEAAI,WAAU;;wEACZ,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;2CAhDrC,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DzC;GAnSgB;KAAA", "debugId": null}}, {"offset": {"line": 1077, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON><PERSON>ding<PERSON>p, BarChart3, Target, Shield, Brain, Zap, Search, Loader2, Scan } from 'lucide-react'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { SwingTradingAnalysis, StockData } from '@/types/trading'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\nimport { SwingScanner } from '@/components/SwingScanner'\nimport { StrategyScanner } from '@/components/StrategyScanner'\n\nexport default function Home() {\n  const [selectedSymbol, setSelectedSymbol] = useState('SPY')\n  const [customSymbol, setCustomSymbol] = useState('')\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [analysis, setAnalysis] = useState<SwingTradingAnalysis | null>(null)\n  const [stockData, setStockData] = useState<StockData | null>(null)\n  const [error, setError] = useState<string | null>(null)\n  const [activeTab, setActiveTab] = useState<'individual' | 'scanner' | 'strategies'>('strategies')\n\n  const popularSymbols = ['SPY', 'QQQ', 'AAPL', 'TSLA', 'NVDA', 'MSFT', 'AMZN', 'GOOGL']\n\n  const handleAnalysis = async (symbol: string) => {\n    setIsAnalyzing(true)\n    setError(null)\n    setAnalysis(null)\n    setStockData(null)\n\n    try {\n      // Fetch stock quote and analysis in parallel\n      const [quoteResponse, analysisResponse] = await Promise.all([\n        fetch(`/api/stocks/quote/${symbol}`),\n        fetch(`/api/analysis/swing/${symbol}`)\n      ])\n\n      if (!quoteResponse.ok) {\n        const quoteError = await quoteResponse.text()\n        console.error('Quote API error:', quoteError)\n        throw new Error(`Failed to fetch quote data: ${quoteResponse.status}`)\n      }\n\n      if (!analysisResponse.ok) {\n        const analysisError = await analysisResponse.text()\n        console.error('Analysis API error:', analysisError)\n        throw new Error(`Failed to fetch analysis data: ${analysisResponse.status}`)\n      }\n\n      const [quoteData, analysisData] = await Promise.all([\n        quoteResponse.json(),\n        analysisResponse.json()\n      ])\n\n      setStockData(quoteData)\n      setAnalysis(analysisData)\n    } catch (err: any) {\n      const errorMessage = err.message?.includes('rate limit') || err.message?.includes('429')\n        ? 'API rate limit reached. Please wait a moment and try again.'\n        : 'Failed to analyze stock. Please try again.'\n      setError(errorMessage)\n      console.error('Analysis error:', err)\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  const handleCustomSymbolSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (customSymbol.trim()) {\n      handleAnalysis(customSymbol.toUpperCase())\n      setSelectedSymbol(customSymbol.toUpperCase())\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900\">\n      {/* Header */}\n      <header className=\"border-b border-slate-800 bg-slate-900/50 backdrop-blur-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <Brain className=\"h-8 w-8 text-blue-400\" />\n              <h1 className=\"text-2xl font-bold text-white\">SwingTrader AI</h1>\n            </div>\n            <nav className=\"hidden md:flex items-center space-x-6\">\n              <button\n                onClick={() => setActiveTab('strategies')}\n                className={`transition-colors ${activeTab === 'strategies' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Pro Strategies\n              </button>\n              <button\n                onClick={() => setActiveTab('scanner')}\n                className={`transition-colors ${activeTab === 'scanner' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Basic Scanner\n              </button>\n              <button\n                onClick={() => setActiveTab('individual')}\n                className={`transition-colors ${activeTab === 'individual' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Individual Analysis\n              </button>\n              <Button variant=\"outline\" className=\"border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white\">\n                Sign In\n              </Button>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20 px-4\">\n        <div className=\"container mx-auto text-center\">\n          <h2 className=\"text-5xl font-bold text-white mb-6\">\n            AI-Powered Swing Trading Analysis\n          </h2>\n          <p className=\"text-xl text-slate-300 mb-8 max-w-3xl mx-auto\">\n            Professional swing trading strategies with automated scanning, precise entry/exit rules,\n            and risk management based on proven methodologies.\n          </p>\n\n          {/* Tab Navigation */}\n          <div className=\"flex justify-center mb-8\">\n            <div className=\"bg-slate-800/50 rounded-lg p-1 flex\">\n              <button\n                onClick={() => setActiveTab('strategies')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'strategies'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Zap className=\"inline mr-2 h-4 w-4\" />\n                Pro Strategies\n              </button>\n              <button\n                onClick={() => setActiveTab('scanner')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'scanner'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Scan className=\"inline mr-2 h-4 w-4\" />\n                Basic Scanner\n              </button>\n              <button\n                onClick={() => setActiveTab('individual')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'individual'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Search className=\"inline mr-2 h-4 w-4\" />\n                Individual Analysis\n              </button>\n            </div>\n          </div>\n\n          {/* Content based on active tab */}\n          {activeTab === 'strategies' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Professional Swing Trading Strategies</h3>\n              <p className=\"text-slate-400 mb-6\">\n                Overnight Momentum & Technical Breakout strategies with precise entry/exit rules and position sizing\n              </p>\n            </div>\n          ) : activeTab === 'scanner' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Basic Swing Trading Scanner</h3>\n              <p className=\"text-slate-400 mb-6\">\n                General swing trading analysis with technical indicators and trend detection\n              </p>\n            </div>\n          ) : (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Individual Stock Analysis</h3>\n              <div className=\"flex flex-wrap justify-center gap-2 mb-6\">\n                {popularSymbols.map((symbol) => (\n                  <Button\n                    key={symbol}\n                    variant={selectedSymbol === symbol ? \"default\" : \"outline\"}\n                    onClick={() => {\n                      setSelectedSymbol(symbol)\n                      handleAnalysis(symbol)\n                    }}\n                    disabled={isAnalyzing}\n                    className={selectedSymbol === symbol\n                      ? \"bg-blue-600 hover:bg-blue-700\"\n                      : \"border-slate-600 text-slate-300 hover:bg-slate-800\"\n                    }\n                  >\n                    {symbol}\n                  </Button>\n                ))}\n              </div>\n\n              {/* Custom Symbol Input */}\n              <form onSubmit={handleCustomSymbolSubmit} className=\"flex justify-center gap-2 mb-6\">\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Enter symbol (e.g., AAPL)\"\n                    value={customSymbol}\n                    onChange={(e) => setCustomSymbol(e.target.value)}\n                    className=\"pl-10 pr-4 py-2 bg-slate-800 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    disabled={isAnalyzing}\n                  />\n                </div>\n                <Button\n                  type=\"submit\"\n                  disabled={isAnalyzing || !customSymbol.trim()}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  Analyze\n                </Button>\n              </form>\n\n              <Button\n                size=\"lg\"\n                onClick={() => handleAnalysis(selectedSymbol)}\n                disabled={isAnalyzing}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3\"\n              >\n                {isAnalyzing ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\n                    Analyzing {selectedSymbol}...\n                  </>\n                ) : (\n                  <>\n                    <Zap className=\"mr-2 h-5 w-5\" />\n                    Get AI Analysis for {selectedSymbol}\n                  </>\n                )}\n              </Button>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* Strategy Scanner Section */}\n      {activeTab === 'strategies' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <StrategyScanner autoScan={true} accountSize={100000} />\n          </div>\n        </section>\n      )}\n\n      {/* Basic Scanner Section */}\n      {activeTab === 'scanner' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <SwingScanner autoScan={false} />\n          </div>\n        </section>\n      )}\n\n      {/* Error Display */}\n      {error && (\n        <section className=\"py-8 px-4\">\n          <div className=\"container mx-auto\">\n            <Card className=\"bg-red-900/20 border-red-500/50\">\n              <CardContent className=\"p-6\">\n                <p className=\"text-red-300 text-center\">{error}</p>\n              </CardContent>\n            </Card>\n          </div>\n        </section>\n      )}\n\n      {/* Analysis Results */}\n      {activeTab === 'individual' && (stockData || analysis) && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n\n              {/* Stock Quote Card */}\n              {stockData && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <BarChart3 className=\"mr-2 h-5 w-5 text-blue-400\" />\n                      {stockData.symbol} Quote\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Price:</span>\n                        <span className=\"text-white font-semibold\">{formatCurrency(stockData.price)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Change:</span>\n                        <span className={stockData.change >= 0 ? \"text-green-400\" : \"text-red-400\"}>\n                          {formatCurrency(stockData.change)} ({formatPercentage(stockData.changePercent)})\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Volume:</span>\n                        <span className=\"text-white\">{stockData.volume.toLocaleString()}</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Trading Levels Card */}\n              {analysis && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <Target className=\"mr-2 h-5 w-5 text-green-400\" />\n                      Trading Levels\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Entry:</span>\n                        <span className=\"text-white font-semibold\">{formatCurrency(analysis.entryPrice)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Stop Loss:</span>\n                        <span className=\"text-red-400\">{formatCurrency(analysis.stopLoss)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Take Profit:</span>\n                        <span className=\"text-green-400\">{formatCurrency(analysis.takeProfit)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Risk/Reward:</span>\n                        <span className=\"text-blue-400 font-semibold\">{analysis.riskRewardRatio.toFixed(2)}:1</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Analysis Summary Card */}\n              {analysis && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <Brain className=\"mr-2 h-5 w-5 text-purple-400\" />\n                      AI Analysis\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Trend:</span>\n                        <span className={`font-semibold ${\n                          analysis.trend === 'BULLISH' ? 'text-green-400' :\n                          analysis.trend === 'BEARISH' ? 'text-red-400' : 'text-yellow-400'\n                        }`}>\n                          {analysis.trend}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Confidence:</span>\n                        <span className=\"text-white font-semibold\">{analysis.confidence.toFixed(1)}%</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Recommendation:</span>\n                        <span className={`font-semibold ${\n                          analysis.recommendation.includes('BUY') ? 'text-green-400' :\n                          analysis.recommendation.includes('SELL') ? 'text-red-400' : 'text-yellow-400'\n                        }`}>\n                          {analysis.recommendation.replace('_', ' ')}\n                        </span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n\n            {/* Technical Indicators */}\n            {analysis && (\n              <Card className=\"mt-6 bg-slate-800/50 border-slate-700\">\n                <CardHeader>\n                  <CardTitle className=\"text-white flex items-center\">\n                    <TrendingUp className=\"mr-2 h-5 w-5 text-orange-400\" />\n                    Technical Indicators\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                    {analysis.indicators.map((indicator, index) => (\n                      <div key={index} className=\"p-4 bg-slate-700/50 rounded-lg\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"text-white font-medium\">{indicator.name}</h4>\n                          <span className={`px-2 py-1 rounded text-xs font-semibold ${\n                            indicator.signal === 'BUY' ? 'bg-green-500/20 text-green-400' :\n                            indicator.signal === 'SELL' ? 'bg-red-500/20 text-red-400' :\n                            'bg-yellow-500/20 text-yellow-400'\n                          }`}>\n                            {indicator.signal}\n                          </span>\n                        </div>\n                        <p className=\"text-slate-300 text-sm\">{indicator.description}</p>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Support and Resistance Levels */}\n            {analysis && (analysis.supportLevels.length > 0 || analysis.resistanceLevels.length > 0) && (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\">\n                {analysis.supportLevels.length > 0 && (\n                  <Card className=\"bg-slate-800/50 border-slate-700\">\n                    <CardHeader>\n                      <CardTitle className=\"text-white flex items-center\">\n                        <Shield className=\"mr-2 h-5 w-5 text-green-400\" />\n                        Support Levels\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-2\">\n                        {analysis.supportLevels.map((level, index) => (\n                          <div key={index} className=\"flex justify-between\">\n                            <span className=\"text-slate-300\">Support {index + 1}:</span>\n                            <span className=\"text-green-400 font-semibold\">{formatCurrency(level)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n\n                {analysis.resistanceLevels.length > 0 && (\n                  <Card className=\"bg-slate-800/50 border-slate-700\">\n                    <CardHeader>\n                      <CardTitle className=\"text-white flex items-center\">\n                        <Shield className=\"mr-2 h-5 w-5 text-red-400\" />\n                        Resistance Levels\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-2\">\n                        {analysis.resistanceLevels.map((level, index) => (\n                          <div key={index} className=\"flex justify-between\">\n                            <span className=\"text-slate-300\">Resistance {index + 1}:</span>\n                            <span className=\"text-red-400 font-semibold\">{formatCurrency(level)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n              </div>\n            )}\n          </div>\n        </section>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;;;AATA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yMAAQ,EAAC;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yMAAQ,EAAC;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yMAAQ,EAAC;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yMAAQ,EAA8B;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yMAAQ,EAAmB;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yMAAQ,EAAgB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yMAAQ,EAA0C;IAEpF,MAAM,iBAAiB;QAAC;QAAO;QAAO;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAQ;IAEtF,MAAM,iBAAiB,OAAO;QAC5B,eAAe;QACf,SAAS;QACT,YAAY;QACZ,aAAa;QAEb,IAAI;YACF,6CAA6C;YAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC1D,MAAM,AAAC,qBAA2B,OAAP;gBAC3B,MAAM,AAAC,uBAA6B,OAAP;aAC9B;YAED,IAAI,CAAC,cAAc,EAAE,EAAE;gBACrB,MAAM,aAAa,MAAM,cAAc,IAAI;gBAC3C,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,MAAM,IAAI,MAAM,AAAC,+BAAmD,OAArB,cAAc,MAAM;YACrE;YAEA,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBACxB,MAAM,gBAAgB,MAAM,iBAAiB,IAAI;gBACjD,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,MAAM,IAAI,MAAM,AAAC,kCAAyD,OAAxB,iBAAiB,MAAM;YAC3E;YAEA,MAAM,CAAC,WAAW,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClD,cAAc,IAAI;gBAClB,iBAAiB,IAAI;aACtB;YAED,aAAa;YACb,YAAY;QACd,EAAE,OAAO,KAAU;gBACI,cAAuC;YAA5D,MAAM,eAAe,EAAA,eAAA,IAAI,OAAO,cAAX,mCAAA,aAAa,QAAQ,CAAC,oBAAiB,gBAAA,IAAI,OAAO,cAAX,oCAAA,cAAa,QAAQ,CAAC,UAC9E,gEACA;YACJ,SAAS;YACT,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,EAAE,cAAc;QAChB,IAAI,aAAa,IAAI,IAAI;YACvB,eAAe,aAAa,WAAW;YACvC,kBAAkB,aAAa,WAAW;QAC5C;IACF;IAEA,qBACE,6NAAC;QAAI,WAAU;;0BAEb,6NAAC;gBAAO,WAAU;0BAChB,cAAA,6NAAC;oBAAI,WAAU;8BACb,cAAA,6NAAC;wBAAI,WAAU;;0CACb,6NAAC;gCAAI,WAAU;;kDACb,6NAAC,gPAAK;wCAAC,WAAU;;;;;;kDACjB,6NAAC;wCAAG,WAAU;kDAAgC;;;;;;;;;;;;0CAEhD,6NAAC;gCAAI,WAAU;;kDACb,6NAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,qBAAkG,OAA9E,cAAc,eAAe,eAAe;kDAC7E;;;;;;kDAGD,6NAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,qBAA+F,OAA3E,cAAc,YAAY,eAAe;kDAC1E;;;;;;kDAGD,6NAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,qBAAkG,OAA9E,cAAc,eAAe,eAAe;kDAC7E;;;;;;kDAGD,6NAAC,+KAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAAmE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/G,6NAAC;gBAAQ,WAAU;0BACjB,cAAA,6NAAC;oBAAI,WAAU;;sCACb,6NAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,6NAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAM7D,6NAAC;4BAAI,WAAU;sCACb,cAAA,6NAAC;gCAAI,WAAU;;kDACb,6NAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,uCAIX,OAHC,cAAc,eACV,2BACA;;0DAGN,6NAAC,0OAAG;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGzC,6NAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,uCAIX,OAHC,cAAc,YACV,2BACA;;0DAGN,6NAAC,6OAAI;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG1C,6NAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,uCAIX,OAHC,cAAc,eACV,2BACA;;0DAGN,6NAAC,mPAAM;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;;;;;;;;;;;;wBAO/C,cAAc,6BACb,6NAAC;4BAAI,WAAU;;8CACb,6NAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,6NAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;mCAInC,cAAc,0BAChB,6NAAC;4BAAI,WAAU;;8CACb,6NAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,6NAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;iDAKrC,6NAAC;4BAAI,WAAU;;8CACb,6NAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,6NAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6NAAC,+KAAM;4CAEL,SAAS,mBAAmB,SAAS,YAAY;4CACjD,SAAS;gDACP,kBAAkB;gDAClB,eAAe;4CACjB;4CACA,UAAU;4CACV,WAAW,mBAAmB,SAC1B,kCACA;sDAGH;2CAZI;;;;;;;;;;8CAkBX,6NAAC;oCAAK,UAAU;oCAA0B,WAAU;;sDAClD,6NAAC;4CAAI,WAAU;;8DACb,6NAAC,mPAAM;oDAAC,WAAU;;;;;;8DAClB,6NAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,WAAU;oDACV,UAAU;;;;;;;;;;;;sDAGd,6NAAC,+KAAM;4CACL,MAAK;4CACL,UAAU,eAAe,CAAC,aAAa,IAAI;4CAC3C,WAAU;sDACX;;;;;;;;;;;;8CAKH,6NAAC,+KAAM;oCACL,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,UAAU;oCACV,WAAU;8CAET,4BACC;;0DACE,6NAAC,+PAAO;gDAAC,WAAU;;;;;;4CAA8B;4CACtC;4CAAe;;qEAG5B;;0DACE,6NAAC,0OAAG;gDAAC,WAAU;;;;;;4CAAiB;4CACX;;;;;;;;;;;;;;;;;;;;;;;;;YAUlC,cAAc,8BACb,6NAAC;gBAAQ,WAAU;0BACjB,cAAA,6NAAC;oBAAI,WAAU;8BACb,cAAA,6NAAC,2LAAe;wBAAC,UAAU;wBAAM,aAAa;;;;;;;;;;;;;;;;YAMnD,cAAc,2BACb,6NAAC;gBAAQ,WAAU;0BACjB,cAAA,6NAAC;oBAAI,WAAU;8BACb,cAAA,6NAAC,qLAAY;wBAAC,UAAU;;;;;;;;;;;;;;;;YAM7B,uBACC,6NAAC;gBAAQ,WAAU;0BACjB,cAAA,6NAAC;oBAAI,WAAU;8BACb,cAAA,6NAAC,2KAAI;wBAAC,WAAU;kCACd,cAAA,6NAAC,kLAAW;4BAAC,WAAU;sCACrB,cAAA,6NAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;YAQlD,cAAc,gBAAgB,CAAC,aAAa,QAAQ,mBACnD,6NAAC;gBAAQ,WAAU;0BACjB,cAAA,6NAAC;oBAAI,WAAU;;sCACb,6NAAC;4BAAI,WAAU;;gCAGZ,2BACC,6NAAC,2KAAI;oCAAC,WAAU;;sDACd,6NAAC,iLAAU;sDACT,cAAA,6NAAC,gLAAS;gDAAC,WAAU;;kEACnB,6NAAC,kQAAS;wDAAC,WAAU;;;;;;oDACpB,UAAU,MAAM;oDAAC;;;;;;;;;;;;sDAGtB,6NAAC,kLAAW;sDACV,cAAA,6NAAC;gDAAI,WAAU;;kEACb,6NAAC;wDAAI,WAAU;;0EACb,6NAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,6NAAC;gEAAK,WAAU;0EAA4B,IAAA,wKAAc,EAAC,UAAU,KAAK;;;;;;;;;;;;kEAE5E,6NAAC;wDAAI,WAAU;;0EACb,6NAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,6NAAC;gEAAK,WAAW,UAAU,MAAM,IAAI,IAAI,mBAAmB;;oEACzD,IAAA,wKAAc,EAAC,UAAU,MAAM;oEAAE;oEAAG,IAAA,0KAAgB,EAAC,UAAU,aAAa;oEAAE;;;;;;;;;;;;;kEAGnF,6NAAC;wDAAI,WAAU;;0EACb,6NAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,6NAAC;gEAAK,WAAU;0EAAc,UAAU,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQtE,0BACC,6NAAC,2KAAI;oCAAC,WAAU;;sDACd,6NAAC,iLAAU;sDACT,cAAA,6NAAC,gLAAS;gDAAC,WAAU;;kEACnB,6NAAC,mPAAM;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;;;;;;sDAItD,6NAAC,kLAAW;sDACV,cAAA,6NAAC;gDAAI,WAAU;;kEACb,6NAAC;wDAAI,WAAU;;0EACb,6NAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,6NAAC;gEAAK,WAAU;0EAA4B,IAAA,wKAAc,EAAC,SAAS,UAAU;;;;;;;;;;;;kEAEhF,6NAAC;wDAAI,WAAU;;0EACb,6NAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,6NAAC;gEAAK,WAAU;0EAAgB,IAAA,wKAAc,EAAC,SAAS,QAAQ;;;;;;;;;;;;kEAElE,6NAAC;wDAAI,WAAU;;0EACb,6NAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,6NAAC;gEAAK,WAAU;0EAAkB,IAAA,wKAAc,EAAC,SAAS,UAAU;;;;;;;;;;;;kEAEtE,6NAAC;wDAAI,WAAU;;0EACb,6NAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,6NAAC;gEAAK,WAAU;;oEAA+B,SAAS,eAAe,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQ5F,0BACC,6NAAC,2KAAI;oCAAC,WAAU;;sDACd,6NAAC,iLAAU;sDACT,cAAA,6NAAC,gLAAS;gDAAC,WAAU;;kEACnB,6NAAC,gPAAK;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;;;;;;sDAItD,6NAAC,kLAAW;sDACV,cAAA,6NAAC;gDAAI,WAAU;;kEACb,6NAAC;wDAAI,WAAU;;0EACb,6NAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,6NAAC;gEAAK,WAAW,AAAC,iBAGjB,OAFC,SAAS,KAAK,KAAK,YAAY,mBAC/B,SAAS,KAAK,KAAK,YAAY,iBAAiB;0EAE/C,SAAS,KAAK;;;;;;;;;;;;kEAGnB,6NAAC;wDAAI,WAAU;;0EACb,6NAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,6NAAC;gEAAK,WAAU;;oEAA4B,SAAS,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE7E,6NAAC;wDAAI,WAAU;;0EACb,6NAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,6NAAC;gEAAK,WAAW,AAAC,iBAGjB,OAFC,SAAS,cAAc,CAAC,QAAQ,CAAC,SAAS,mBAC1C,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU,iBAAiB;0EAE3D,SAAS,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAUnD,0BACC,6NAAC,2KAAI;4BAAC,WAAU;;8CACd,6NAAC,iLAAU;8CACT,cAAA,6NAAC,gLAAS;wCAAC,WAAU;;0DACnB,6NAAC,mQAAU;gDAAC,WAAU;;;;;;4CAAiC;;;;;;;;;;;;8CAI3D,6NAAC,kLAAW;8CACV,cAAA,6NAAC;wCAAI,WAAU;kDACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACnC,6NAAC;gDAAgB,WAAU;;kEACzB,6NAAC;wDAAI,WAAU;;0EACb,6NAAC;gEAAG,WAAU;0EAA0B,UAAU,IAAI;;;;;;0EACtD,6NAAC;gEAAK,WAAW,AAAC,2CAIjB,OAHC,UAAU,MAAM,KAAK,QAAQ,mCAC7B,UAAU,MAAM,KAAK,SAAS,+BAC9B;0EAEC,UAAU,MAAM;;;;;;;;;;;;kEAGrB,6NAAC;wDAAE,WAAU;kEAA0B,UAAU,WAAW;;;;;;;+CAXpD;;;;;;;;;;;;;;;;;;;;;wBAoBnB,YAAY,CAAC,SAAS,aAAa,CAAC,MAAM,GAAG,KAAK,SAAS,gBAAgB,CAAC,MAAM,GAAG,CAAC,mBACrF,6NAAC;4BAAI,WAAU;;gCACZ,SAAS,aAAa,CAAC,MAAM,GAAG,mBAC/B,6NAAC,2KAAI;oCAAC,WAAU;;sDACd,6NAAC,iLAAU;sDACT,cAAA,6NAAC,gLAAS;gDAAC,WAAU;;kEACnB,6NAAC,mPAAM;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;;;;;;sDAItD,6NAAC,kLAAW;sDACV,cAAA,6NAAC;gDAAI,WAAU;0DACZ,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,sBAClC,6NAAC;wDAAgB,WAAU;;0EACzB,6NAAC;gEAAK,WAAU;;oEAAiB;oEAAS,QAAQ;oEAAE;;;;;;;0EACpD,6NAAC;gEAAK,WAAU;0EAAgC,IAAA,wKAAc,EAAC;;;;;;;uDAFvD;;;;;;;;;;;;;;;;;;;;;gCAUnB,SAAS,gBAAgB,CAAC,MAAM,GAAG,mBAClC,6NAAC,2KAAI;oCAAC,WAAU;;sDACd,6NAAC,iLAAU;sDACT,cAAA,6NAAC,gLAAS;gDAAC,WAAU;;kEACnB,6NAAC,mPAAM;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;;;;;;sDAIpD,6NAAC,kLAAW;sDACV,cAAA,6NAAC;gDAAI,WAAU;0DACZ,SAAS,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACrC,6NAAC;wDAAgB,WAAU;;0EACzB,6NAAC;gEAAK,WAAU;;oEAAiB;oEAAY,QAAQ;oEAAE;;;;;;;0EACvD,6NAAC;gEAAK,WAAU;0EAA8B,IAAA,wKAAc,EAAC;;;;;;;uDAFrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBpC;GApcwB;KAAA", "debugId": null}}]}
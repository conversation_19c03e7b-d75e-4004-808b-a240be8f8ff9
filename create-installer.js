/**
 * Swing Trading System - Self-Installing Package Creator
 * Creates a complete installer that sets up everything automatically
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Creating Swing Trading System Installer...\n');

// Create installer directory
const installerDir = 'SwingTrader-Installer';
if (!fs.existsSync(installerDir)) {
    fs.mkdirSync(installerDir);
}

// Create the main installer script
const installerScript = `
@echo off
title Swing Trading System Installer
color 0A

echo.
echo ========================================
echo    SWING TRADING SYSTEM INSTALLER
echo ========================================
echo.
echo This will install your AI-powered swing trading system
echo on this computer automatically.
echo.
pause

echo.
echo [1/5] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found! 
    echo.
    echo Please install Node.js first:
    echo 1. Go to https://nodejs.org
    echo 2. Download and install the LTS version
    echo 3. Restart this installer
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js found!
)

echo.
echo [2/5] Creating application directory...
if not exist "SwingTrader-AI" mkdir SwingTrader-AI
cd SwingTrader-AI

echo.
echo [3/5] Extracting application files...
powershell -command "Expand-Archive -Path '../swing-trader-app.zip' -DestinationPath '.' -Force"

echo.
echo [4/5] Installing dependencies (this may take a few minutes)...
call npm install

echo.
echo [5/5] Creating desktop shortcut...
echo @echo off > "Start-SwingTrader.bat"
echo title Swing Trading System >> "Start-SwingTrader.bat"
echo color 0A >> "Start-SwingTrader.bat"
echo echo. >> "Start-SwingTrader.bat"
echo echo ========================================= >> "Start-SwingTrader.bat"
echo echo    SWING TRADING SYSTEM STARTING... >> "Start-SwingTrader.bat"
echo echo ========================================= >> "Start-SwingTrader.bat"
echo echo. >> "Start-SwingTrader.bat"
echo echo Your trading system is starting up... >> "Start-SwingTrader.bat"
echo echo Once ready, it will open in your browser automatically. >> "Start-SwingTrader.bat"
echo echo. >> "Start-SwingTrader.bat"
echo echo Local URL: http://localhost:3000 >> "Start-SwingTrader.bat"
echo echo Network URL: Will be shown below >> "Start-SwingTrader.bat"
echo echo. >> "Start-SwingTrader.bat"
echo call npm run dev >> "Start-SwingTrader.bat"

copy "Start-SwingTrader.bat" "%USERPROFILE%\\Desktop\\Start-SwingTrader.bat"

echo.
echo ========================================
echo    INSTALLATION COMPLETE! ✅
echo ========================================
echo.
echo Your Swing Trading System has been installed successfully!
echo.
echo To start the system:
echo 1. Double-click "Start-SwingTrader.bat" on your desktop
echo 2. Wait for it to load (about 30 seconds)
echo 3. Your browser will open automatically
echo.
echo The system includes:
echo ✅ Real-time stock scanning
echo ✅ AI-powered trade analysis  
echo ✅ Paper trading functionality
echo ✅ Professional trading cards
echo.
echo Press any key to finish...
pause >nul

echo.
echo Would you like to start the system now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo.
    echo Starting Swing Trading System...
    start "" "%USERPROFILE%\\Desktop\\Start-SwingTrader.bat"
)

echo.
echo Installation complete! You can close this window.
pause
`;

// Write the installer script
fs.writeFileSync(path.join(installerDir, 'INSTALL.bat'), installerScript);

// Create a PowerShell script to zip the source files
const zipScript = `
Write-Host "Creating application package..." -ForegroundColor Green
Compress-Archive -Path "swing-trader-upload\\*" -DestinationPath "${installerDir}\\swing-trader-app.zip" -Force
Write-Host "Package created successfully!" -ForegroundColor Green
`;

fs.writeFileSync('create-zip.ps1', zipScript);

// Create README for the installer
const readmeContent = `
# 🚀 SWING TRADING SYSTEM INSTALLER

## What This Is:
A complete, self-installing package for your AI-powered swing trading system.

## What It Includes:
✅ Complete trading application
✅ AI analysis system
✅ Real-time stock scanning
✅ Paper trading functionality
✅ Automatic setup and installation

## How To Use:

### On This Computer:
1. Run "CREATE-PACKAGE.bat" to create the installer
2. Copy the entire "SwingTrader-Installer" folder to a USB drive

### On Your Downstairs Computer:
1. Copy the "SwingTrader-Installer" folder from USB
2. Double-click "INSTALL.bat"
3. Follow the on-screen instructions
4. System will install automatically!

### After Installation:
- Double-click "Start-SwingTrader.bat" on desktop to run
- System opens in your browser automatically
- Access from any device on your network

## Requirements:
- Windows 10/11
- Node.js (installer will guide you if missing)
- Internet connection for stock data

## What Gets Installed:
- Complete trading system
- All dependencies
- Desktop shortcut
- Auto-start script

No technical knowledge required - everything is automatic!
`;

fs.writeFileSync(path.join(installerDir, 'README.txt'), readmeContent);

// Create the package creation script
const packageScript = `
@echo off
title Creating Swing Trading Installer Package
color 0A

echo.
echo ========================================
echo    CREATING INSTALLER PACKAGE
echo ========================================
echo.

echo [1/2] Packaging application files...
powershell -ExecutionPolicy Bypass -File "create-zip.ps1"

echo.
echo [2/2] Finalizing installer...

echo.
echo ========================================
echo    PACKAGE CREATION COMPLETE! ✅
echo ========================================
echo.
echo Your installer package is ready in the "SwingTrader-Installer" folder!
echo.
echo To install on your downstairs computer:
echo 1. Copy the entire "SwingTrader-Installer" folder to a USB drive
echo 2. On the other computer, run "INSTALL.bat"
echo 3. Follow the instructions - everything is automatic!
echo.
echo The installer includes:
echo ✅ Complete trading system
echo ✅ Automatic dependency installation
echo ✅ Desktop shortcut creation
echo ✅ Easy startup script
echo.
pause
`;

fs.writeFileSync('CREATE-PACKAGE.bat', packageScript);

console.log('✅ Installer creation scripts ready!\n');
console.log('🎯 NEXT STEPS:');
console.log('1. Double-click "CREATE-PACKAGE.bat"');
console.log('2. Copy "SwingTrader-Installer" folder to USB drive');
console.log('3. On downstairs computer, run "INSTALL.bat"');
console.log('4. Everything installs automatically!\n');

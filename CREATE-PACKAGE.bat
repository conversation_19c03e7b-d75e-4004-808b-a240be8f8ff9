
@echo off
title Creating Swing Trading Installer Package
color 0A

echo.
echo ========================================
echo    CREATING INSTALLER PACKAGE
echo ========================================
echo.

echo [1/2] Packaging application files...
powershell -ExecutionPolicy Bypass -File "create-zip.ps1"

echo.
echo [2/2] Finalizing installer...

echo.
echo ========================================
echo    PACKAGE CREATION COMPLETE! ✅
echo ========================================
echo.
echo Your installer package is ready in the "SwingTrader-Installer" folder!
echo.
echo To install on your downstairs computer:
echo 1. Copy the entire "SwingTrader-Installer" folder to a USB drive
echo 2. On the other computer, run "INSTALL.bat"
echo 3. Follow the instructions - everything is automatic!
echo.
echo The installer includes:
echo ✅ Complete trading system
echo ✅ Automatic dependency installation
echo ✅ Desktop shortcut creation
echo ✅ Easy startup script
echo.
pause

// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/types.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../src/app/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/analysis/ai-setup/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/analysis/ai-setup">> = Specific
  const handler = {} as typeof import("../../src/app/api/analysis/ai-setup/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/analysis/strategy/[symbol]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/analysis/strategy/[symbol]">> = Specific
  const handler = {} as typeof import("../../src/app/api/analysis/strategy/[symbol]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/analysis/swing/[symbol]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/analysis/swing/[symbol]">> = Specific
  const handler = {} as typeof import("../../src/app/api/analysis/swing/[symbol]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/paper-trading/execute/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/paper-trading/execute">> = Specific
  const handler = {} as typeof import("../../src/app/api/paper-trading/execute/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/scanner/full/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/scanner/full">> = Specific
  const handler = {} as typeof import("../../src/app/api/scanner/full/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/scanner/quick/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/scanner/quick">> = Specific
  const handler = {} as typeof import("../../src/app/api/scanner/quick/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/scanner/sector/[sector]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/scanner/sector/[sector]">> = Specific
  const handler = {} as typeof import("../../src/app/api/scanner/sector/[sector]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/scanner/strategies/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/scanner/strategies">> = Specific
  const handler = {} as typeof import("../../src/app/api/scanner/strategies/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/scanner/test/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/scanner/test">> = Specific
  const handler = {} as typeof import("../../src/app/api/scanner/test/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/stocks/historical/[symbol]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/stocks/historical/[symbol]">> = Specific
  const handler = {} as typeof import("../../src/app/api/stocks/historical/[symbol]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/stocks/quote/[symbol]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/stocks/quote/[symbol]">> = Specific
  const handler = {} as typeof import("../../src/app/api/stocks/quote/[symbol]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/stocks/search/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/stocks/search">> = Specific
  const handler = {} as typeof import("../../src/app/api/stocks/search/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}





// Validate ../../src/app/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

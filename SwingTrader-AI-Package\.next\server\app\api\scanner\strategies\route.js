var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/scanner/strategies/route.js")
R.c("server/chunks/7b731_next_0cbcf582._.js")
R.c("server/chunks/7b731_axios_lib_7462c230._.js")
R.c("server/chunks/7b731_mime-db_cef17743._.js")
R.c("server/chunks/7b731_@stoqey_ib_dist_8ec9c608._.js")
R.c("server/chunks/7b731_rxjs_dist_cjs_4942e14c._.js")
R.c("server/chunks/7b731_9e716fb1._.js")
R.c("server/chunks/[root-of-the-server]__dc3d6b71._.js")
R.m("[project]/SwingTrader-AI-Package/.next-internal/server/app/api/scanner/strategies/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/SwingTrader-AI-Package/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/SwingTrader-AI-Package/src/app/api/scanner/strategies/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/SwingTrader-AI-Package/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/SwingTrader-AI-Package/src/app/api/scanner/strategies/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports

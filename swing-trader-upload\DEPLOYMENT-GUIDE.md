# 🚀 SwingTrader AI - Deployment Guide

## Quick Start Options

### 🌐 Option 1: Cloud Deployment (Recommended - 1 Click)

**Easiest way to share with anyone, anywhere:**

1. **Install Vercel CLI:**
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel:**
   ```bash
   vercel login
   ```

3. **Deploy:**
   ```bash
   node deploy.js
   ```

4. **Share the URL** - Anyone can access it instantly!

**Benefits:**
- ✅ Works on any device (phone, tablet, computer)
- ✅ No installation required for users
- ✅ Automatic updates when you redeploy
- ✅ Professional URL you can share

---

### 🖥️ Option 2: Windows Installation

**For Windows computers:**

1. **Copy the entire `swing-trader-ai` folder** to the target computer
2. **Double-click `install-windows.bat`**
3. **Add your API keys** when prompted
4. **Double-click `start-swingtrader.bat`** to run
5. **Open browser** to `http://localhost:3000`

---

### 🍎 Option 3: Mac/Linux Installation

**For Mac or Linux computers:**

1. **Copy the entire `swing-trader-ai` folder** to the target computer
2. **Open Terminal** in the folder
3. **Run installation:**
   ```bash
   chmod +x install-mac-linux.sh
   ./install-mac-linux.sh
   ```
4. **Add your API keys** when prompted
5. **Start the app:**
   ```bash
   ./start-swingtrader.sh
   ```
6. **Open browser** to `http://localhost:3000`

---

## 🔑 API Keys Required

You'll need these API keys (you already have them):

```env
POLYGON_API_KEY=********************************
FMP_API_KEY=K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7
ALPACA_API_KEY=PKKKYLNNZZT2EI7F3CVL
ALPACA_SECRET_KEY=Bgh3CLNSueS9Odyeb6U38UddNEluGDSIflunjinD
```

---

## 📁 What to Share

### For Cloud Deployment:
- Just share the URL after deployment!

### For Local Installation:
**Zip and share this entire folder:**
- `swing-trader-ai/` (complete project)
- Include all files and subfolders
- The recipient runs the install script for their OS

---

## 🎯 Features Included

### Professional Swing Trading Strategies:
- **Overnight Momentum Continuation**
- **Technical Breakout Trend-Follow**
- **Automated Stock Scanning** (70+ stocks)
- **Risk Management & Position Sizing**
- **Real-time Market Data**

### Technical Analysis:
- RSI, MACD, Moving Averages
- Support/Resistance Detection
- Volume Analysis
- ATR-based Risk Management

### User Interface:
- Professional dark theme
- Real-time scanning
- Strategy comparison
- Risk warnings and alerts

---

## 🔧 Troubleshooting

### Common Issues:

**"Node.js not found"**
- Install Node.js from https://nodejs.org/
- Choose the LTS version

**"API errors"**
- Check that API keys are correctly added to `.env.local`
- Ensure no extra spaces or quotes around keys

**"Port 3000 in use"**
- Close other applications using port 3000
- Or change port in `package.json` scripts

**"Build failed"**
- Run `npm install` first
- Check internet connection for dependencies

---

## 🌟 Sharing Instructions

### To Share with Others:

1. **Choose deployment method** (cloud recommended)
2. **For cloud:** Share the live URL
3. **For local:** Zip the entire project folder
4. **Include this guide** for setup instructions

### What Recipients Need:
- **For cloud:** Just a web browser!
- **For local:** Node.js installed on their computer

---

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Ensure all API keys are correctly configured
3. Verify Node.js is installed (version 18+ recommended)
4. Check that all files were copied completely

---

## 🎉 You're Ready!

Your SwingTrader AI platform is ready to deploy and share. Choose the method that works best for your needs:

- **Cloud deployment** for maximum accessibility
- **Local installation** for private use or offline access

The platform includes all the professional swing trading strategies and analysis tools you need!

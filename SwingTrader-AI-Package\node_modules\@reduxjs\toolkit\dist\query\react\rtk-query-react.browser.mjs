import{build<PERSON><PERSON><PERSON><PERSON> as qe,coreModule as Ke}from"@reduxjs/toolkit/query";import"@reduxjs/toolkit";import{batch as be,useDispatch as Ee,useSelector as ke,useStore as Me}from"react-redux";import{createSelector as Oe}from"reselect";function z(e){return e.replace(e[0],e[0].toUpperCase())}function ie(e){return e.type==="query"}function se(e){return e.type==="mutation"}function _(e){return e.type==="infinitequery"}function v(e,...Q){return Object.assign(e,...Q)}import{formatProdErrorMessage as xe}from"@reduxjs/toolkit";import{QueryStatus as me,skipToken as U}from"@reduxjs/toolkit/query";import{useCallback as O,useDebugValue as ee,useEffect as N,useLayoutEffect as Se,useMemo as m,useRef as L,useState as ue}from"react";import{shallowEqual as te}from"react-redux";var q=Symbol();import{useEffect as ce,useRef as le,useMemo as Re}from"react";import{copyWithStructuralSharing as ge}from"@reduxjs/toolkit/query";function j(e){let Q=le(e),l=Re(()=>ge(Q.current,e),[e]);return ce(()=>{Q.current!==l&&(Q.current=l)},[l]),l}import{useEffect as Te,useRef as De}from"react";import{shallowEqual as ae}from"react-redux";function K(e){let Q=De(e);return Te(()=>{ae(Q.current,e)||(Q.current=e)},[e]),ae(Q.current,e)?Q.current:e}var Be=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Ae=Be(),Pe=()=>typeof navigator<"u"&&navigator.product==="ReactNative",he=Pe(),Ie=()=>Ae||he?Se:N,Ue=Ie(),ye=e=>e.isUninitialized?{...e,isUninitialized:!1,isFetching:!0,isLoading:e.data===void 0,status:me.pending}:e;function ne(e,...Q){let l={};return Q.forEach(S=>{l[S]=e[S]}),l}var re=["data","status","isLoading","isSuccess","isError","error"];function oe({api:e,moduleOptions:{batch:Q,hooks:{useDispatch:l,useSelector:S,useStore:V},unstable__sideEffectsInRender:A,createSelector:F},serializeQueryArgs:h,context:I}){let b=A?t=>t():N;return{buildQueryHooks:C,buildInfiniteQueryHooks:Y,buildMutationHook:J,usePrefetch:Z};function $(t,i,p){if(i?.endpointName&&t.isUninitialized){let{endpointName:a}=i,y=I.endpointDefinitions[a];p!==U&&h({queryArgs:i.originalArgs,endpointDefinition:y,endpointName:a})===h({queryArgs:p,endpointDefinition:y,endpointName:a})&&(i=void 0)}let u=t.isSuccess?t.data:i?.data;u===void 0&&(u=t.data);let s=u!==void 0,n=t.isLoading,r=(!i||i.isLoading||i.isUninitialized)&&!s&&n,o=t.isSuccess||s&&(n&&!i?.isError||t.isUninitialized);return{...t,data:u,currentData:t.data,isFetching:n,isLoading:r,isSuccess:o}}function G(t,i,p){if(i?.endpointName&&t.isUninitialized){let{endpointName:a}=i,y=I.endpointDefinitions[a];p!==U&&h({queryArgs:i.originalArgs,endpointDefinition:y,endpointName:a})===h({queryArgs:p,endpointDefinition:y,endpointName:a})&&(i=void 0)}let u=t.isSuccess?t.data:i?.data;u===void 0&&(u=t.data);let s=u!==void 0,n=t.isLoading,r=(!i||i.isLoading||i.isUninitialized)&&!s&&n,o=t.isSuccess||n&&s;return{...t,data:u,currentData:t.data,isFetching:n,isLoading:r,isSuccess:o}}function Z(t,i){let p=l(),u=K(i);return O((s,n)=>p(e.util.prefetch(t,s,{...u,...n})),[t,p,u])}function D(t,i,{refetchOnReconnect:p,refetchOnFocus:u,refetchOnMountOrArgChange:s,skip:n=!1,pollingInterval:r=0,skipPollingIfUnfocused:o=!1,...a}={}){let{initiate:y}=e.endpoints[t],d=l(),x=L(void 0);if(!x.current){let M=d(e.internalActions.internal_getRTKQSubscriptions());x.current=M}let f=j(n?U:i),c=K({refetchOnReconnect:p,refetchOnFocus:u,pollingInterval:r,skipPollingIfUnfocused:o}),R=a.initialPageParam,g=K(R),T=L(void 0),{queryCacheKey:P,requestId:k}=T.current||{},H=!1;P&&k&&(H=x.current.isRequestSubscribed(P,k));let X=!H&&T.current!==void 0;return b(()=>{X&&(T.current=void 0)},[X]),b(()=>{let M=T.current;if(f===U){M?.unsubscribe(),T.current=void 0;return}let Qe=T.current?.subscriptionOptions;if(!M||M.arg!==f){M?.unsubscribe();let de=d(y(f,{subscriptionOptions:c,forceRefetch:s,..._(I.endpointDefinitions[t])?{initialPageParam:g}:{}}));T.current=de}else c!==Qe&&M.updateSubscriptionOptions(c)},[d,y,s,f,c,X,g,t]),[T,d,y,c]}function w(t,i){return(u,{skip:s=!1,selectFromResult:n}={})=>{let{select:r}=e.endpoints[t],o=j(s?U:u),a=L(void 0),y=m(()=>F([r(o),(R,g)=>g,R=>o],i,{memoizeOptions:{resultEqualityCheck:te}}),[r,o]),d=m(()=>n?F([y],n,{devModeChecks:{identityFunctionCheck:"never"}}):y,[y,n]),x=S(R=>d(R,a.current),te),f=V(),c=y(f.getState(),a.current);return Ue(()=>{a.current=c},[c]),x}}function B(t){N(()=>()=>{t.current?.unsubscribe?.(),t.current=void 0},[t])}function E(t){if(!t.current)throw new Error(xe(38));return t.current.refetch()}function C(t){let i=(s,n={})=>{let[r]=D(t,s,n);return B(r),m(()=>({refetch:()=>E(r)}),[r])},p=({refetchOnReconnect:s,refetchOnFocus:n,pollingInterval:r=0,skipPollingIfUnfocused:o=!1}={})=>{let{initiate:a}=e.endpoints[t],y=l(),[d,x]=ue(q),f=L(void 0),c=K({refetchOnReconnect:s,refetchOnFocus:n,pollingInterval:r,skipPollingIfUnfocused:o});b(()=>{let P=f.current?.subscriptionOptions;c!==P&&f.current?.updateSubscriptionOptions(c)},[c]);let R=L(c);b(()=>{R.current=c},[c]);let g=O(function(P,k=!1){let H;return Q(()=>{f.current?.unsubscribe(),f.current=H=y(a(P,{subscriptionOptions:R.current,forceRefetch:!k})),x(P)}),H},[y,a]),T=O(()=>{f.current?.queryCacheKey&&y(e.internalActions.removeQueryResult({queryCacheKey:f.current?.queryCacheKey}))},[y]);return N(()=>()=>{f?.current?.unsubscribe()},[]),N(()=>{d!==q&&!f.current&&g(d,!0)},[d,g]),m(()=>[g,d,{reset:T}],[g,d,T])},u=w(t,$);return{useQueryState:u,useQuerySubscription:i,useLazyQuerySubscription:p,useLazyQuery(s){let[n,r,{reset:o}]=p(s),a=u(r,{...s,skip:r===q}),y=m(()=>({lastArg:r}),[r]);return m(()=>[n,{...a,reset:o},y],[n,a,o,y])},useQuery(s,n){let r=i(s,n),o=u(s,{selectFromResult:s===U||n?.skip?void 0:ye,...n}),a=ne(o,...re);return ee(a),m(()=>({...o,...r}),[o,r])}}}function Y(t){let i=(u,s={})=>{let[n,r,o,a]=D(t,u,s),y=L(a);b(()=>{y.current=a},[a]);let d=O(function(c,R){let g;return Q(()=>{n.current?.unsubscribe(),n.current=g=r(o(c,{subscriptionOptions:y.current,direction:R}))}),g},[n,r,o]);B(n);let x=j(s.skip?U:u),f=O(()=>E(n),[n]);return m(()=>({trigger:d,refetch:f,fetchNextPage:()=>d(x,"forward"),fetchPreviousPage:()=>d(x,"backward")}),[f,d,x])},p=w(t,G);return{useInfiniteQueryState:p,useInfiniteQuerySubscription:i,useInfiniteQuery(u,s){let{refetch:n,fetchNextPage:r,fetchPreviousPage:o}=i(u,s),a=p(u,{selectFromResult:u===U||s?.skip?void 0:ye,...s}),y=ne(a,...re,"hasNextPage","hasPreviousPage");return ee(y),m(()=>({...a,fetchNextPage:r,fetchPreviousPage:o,refetch:n}),[a,r,o,n])}}}function J(t){return({selectFromResult:i,fixedCacheKey:p}={})=>{let{select:u,initiate:s}=e.endpoints[t],n=l(),[r,o]=ue();N(()=>()=>{r?.arg.fixedCacheKey||r?.reset()},[r]);let a=O(function(P){let k=n(s(P,{fixedCacheKey:p}));return o(k),k},[n,s,p]),{requestId:y}=r||{},d=m(()=>u({fixedCacheKey:p,requestId:r?.requestId}),[p,r,u]),x=m(()=>i?F([d],i):d,[i,d]),f=S(x,te),c=p==null?r?.arg.originalArgs:void 0,R=O(()=>{Q(()=>{r&&o(void 0),p&&n(e.internalActions.removeMutationResult({requestId:y,fixedCacheKey:p}))})},[n,p,r,y]),g=ne(f,...re,"endpointName");ee(g);let T=m(()=>({...f,originalArgs:c,reset:R}),[f,c,R]);return m(()=>[a,T],[a,T])}}}var pe=Symbol(),fe=({batch:e=be,hooks:Q={useDispatch:Ee,useSelector:ke,useStore:Me},createSelector:l=Oe,unstable__sideEffectsInRender:S=!1,...V}={})=>({name:pe,init(A,{serializeQueryArgs:F},h){let I=A,{buildQueryHooks:b,buildInfiniteQueryHooks:$,buildMutationHook:G,usePrefetch:Z}=oe({api:A,moduleOptions:{batch:e,hooks:Q,unstable__sideEffectsInRender:S,createSelector:l},serializeQueryArgs:F,context:h});return v(I,{usePrefetch:Z}),v(h,{batch:e}),{injectEndpoint(D,w){if(ie(w)){let{useQuery:B,useLazyQuery:E,useLazyQuerySubscription:C,useQueryState:Y,useQuerySubscription:J}=b(D);v(I.endpoints[D],{useQuery:B,useLazyQuery:E,useLazyQuerySubscription:C,useQueryState:Y,useQuerySubscription:J}),A[`use${z(D)}Query`]=B,A[`useLazy${z(D)}Query`]=E}if(se(w)){let B=G(D);v(I.endpoints[D],{useMutation:B}),A[`use${z(D)}Mutation`]=B}else if(_(w)){let{useInfiniteQuery:B,useInfiniteQuerySubscription:E,useInfiniteQueryState:C}=$(D);v(I.endpoints[D],{useInfiniteQuery:B,useInfiniteQuerySubscription:E,useInfiniteQueryState:C}),A[`use${z(D)}InfiniteQuery`]=B}}}}});export*from"@reduxjs/toolkit/query";import{configureStore as Fe,formatProdErrorMessage as we}from"@reduxjs/toolkit";import{useContext as ve}from"react";import{useEffect as Le}from"react";import*as W from"react";import{Provider as Ne,ReactReduxContext as Ce}from"react-redux";import{setupListeners as He}from"@reduxjs/toolkit/query";function ze(e){let Q=e.context||Ce;if(ve(Q))throw new Error(we(35));let[S]=W.useState(()=>Fe({reducer:{[e.api.reducerPath]:e.api.reducer},middleware:V=>V().concat(e.api.middleware)}));return Le(()=>e.setupListeners===!1?void 0:He(S.dispatch,e.setupListeners),[e.setupListeners,S.dispatch]),W.createElement(Ne,{store:S,context:Q},e.children)}var Ut=qe(Ke(),fe());export{ze as ApiProvider,q as UNINITIALIZED_VALUE,Ut as createApi,fe as reactHooksModule,pe as reactHooksModuleName};
//# sourceMappingURL=rtk-query-react.browser.mjs.map
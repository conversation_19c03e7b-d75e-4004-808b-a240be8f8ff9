module.exports = [
"[project]/SwingTrader-AI-Package/.next-internal/server/app/api/analysis/ai-setup/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/SwingTrader-AI-Package/src/app/api/analysis/ai-setup/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
;
const HARDCODED_API_KEY = '********************************************************************************************************************************************************************';
console.log('🔑 Hardcoded API Key prefix:', HARDCODED_API_KEY.substring(0, 15));
console.log('🔑 Environment API Key prefix:', process.env.OPENAI_API_KEY?.substring(0, 15));
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: HARDCODED_API_KEY
});
async function POST(request) {
    try {
        console.log('🚀 AI Analysis endpoint called');
        console.log('🔑 Using hardcoded API key:', HARDCODED_API_KEY.substring(0, 15));
        const { scanResult } = await request.json();
        if (!scanResult) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Scan result is required'
            }, {
                status: 400
            });
        }
        // Prepare context for AI analysis
        const setup = scanResult.overnightSetup || scanResult.breakoutSetup;
        if (!setup) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No trading setup found'
            }, {
                status: 400
            });
        }
        const strategyType = scanResult.overnightSetup ? 'overnight momentum' : 'technical breakout';
        const currentPrice = scanResult.quote.price;
        const changePercent = scanResult.quote.changePercent || 0;
        const prompt = `As a professional swing trading analyst, provide a comprehensive analysis for ${scanResult.symbol} (${scanResult.name}) in the ${scanResult.sector} sector.

CURRENT SETUP:
- Strategy: ${strategyType}
- Current Price: $${currentPrice}
- Daily Change: ${changePercent.toFixed(2)}%
- Entry Price: $${setup.entryPrice}
- Stop Loss: $${setup.stopLoss}
- Target: $${setup.targets[0]}
- Confidence: ${setup.confidence}%
- Overall Score: ${scanResult.overallScore}/100

TRADING ALERTS: ${scanResult.alerts.join(', ') || 'None'}
RISK WARNINGS: ${scanResult.riskWarnings.join(', ') || 'None'}

Please provide a JSON response with the following structure:
{
  "setupExplanation": "Clear explanation of why this setup meets swing trading criteria",
  "catalysts": ["List of potential market catalysts supporting this trade"],
  "riskAssessment": "Detailed risk analysis and what could go wrong",
  "keyLevels": ["Important price levels to watch"],
  "timeframe": "Expected timeframe for trade to play out",
  "confidence": number between 1-100
}

Focus on:
1. Technical analysis backing the setup
2. Market conditions and sector trends
3. Risk/reward analysis
4. Specific entry/exit strategy
5. Potential catalysts or headwinds`;
        const completion = await openai.chat.completions.create({
            model: "gpt-4",
            messages: [
                {
                    role: "system",
                    content: "You are a professional swing trading analyst with expertise in technical analysis, market dynamics, and risk management. Provide actionable, data-driven insights."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.3,
            max_tokens: 1000
        });
        const aiResponse = completion.choices[0]?.message?.content;
        if (!aiResponse) {
            throw new Error('No response from AI');
        }
        // Parse the JSON response
        let analysis;
        try {
            analysis = JSON.parse(aiResponse);
        } catch (parseError) {
            // Fallback if JSON parsing fails
            analysis = {
                setupExplanation: aiResponse.substring(0, 300) + '...',
                catalysts: [
                    'AI analysis available - see full response'
                ],
                riskAssessment: 'Standard swing trading risks apply',
                keyLevels: [
                    `Entry: $${setup.entryPrice}`,
                    `Stop: $${setup.stopLoss}`,
                    `Target: $${setup.targets[0]}`
                ],
                timeframe: '3-10 trading days',
                confidence: Math.round(setup.confidence)
            };
        }
        const fullAnalysis = {
            symbol: scanResult.symbol,
            setupExplanation: analysis.setupExplanation || 'Setup analysis not available',
            catalysts: analysis.catalysts || [],
            riskAssessment: analysis.riskAssessment || 'Standard risks apply',
            keyLevels: analysis.keyLevels || [],
            timeframe: analysis.timeframe || '3-10 days',
            confidence: analysis.confidence || Math.round(setup.confidence),
            lastUpdated: new Date().toISOString()
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(fullAnalysis);
    } catch (error) {
        console.error('AI Analysis error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate AI analysis'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__91b334b8._.js.map
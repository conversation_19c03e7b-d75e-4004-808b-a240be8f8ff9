{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(value)\n}\n\nexport function formatPercentage(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'percent',\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(value / 100)\n}\n\nexport function calculateRiskReward(\n  entryPrice: number,\n  stopLoss: number,\n  takeProfit: number\n): number {\n  const risk = Math.abs(entryPrice - stopLoss)\n  const reward = Math.abs(takeProfit - entryPrice)\n  return reward / risk\n}\n\nexport function calculatePositionSize(\n  accountSize: number,\n  riskPercentage: number,\n  entryPrice: number,\n  stopLoss: number\n): number {\n  const riskAmount = accountSize * (riskPercentage / 100)\n  const riskPerShare = Math.abs(entryPrice - stopLoss)\n  return Math.floor(riskAmount / riskPerShare)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sMAAO,EAAC,IAAA,6KAAI,EAAC;AACtB;AAEO,SAAS,eAAe,KAAa;IAC1C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,QAAQ;AACpB;AAEO,SAAS,oBACd,UAAkB,EAClB,QAAgB,EAChB,UAAkB;IAElB,MAAM,OAAO,KAAK,GAAG,CAAC,aAAa;IACnC,MAAM,SAAS,KAAK,GAAG,CAAC,aAAa;IACrC,OAAO,SAAS;AAClB;AAEO,SAAS,sBACd,WAAmB,EACnB,cAAsB,EACtB,UAAkB,EAClB,QAAgB;IAEhB,MAAM,aAAa,cAAc,CAAC,iBAAiB,GAAG;IACtD,MAAM,eAAe,KAAK,GAAG,CAAC,aAAa;IAC3C,OAAO,KAAK,KAAK,CAAC,aAAa;AACjC", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uMAAG,EACxB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,mPAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,wMAAI,GAAG;IAC9B,qBACE,8QAAC;QACC,WAAW,IAAA,yJAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,mPAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QACC,KAAK;QACL,WAAW,IAAA,yJAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,mPAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QACC,KAAK;QACL,WAAW,IAAA,yJAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,mPAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QACC,KAAK;QACL,WAAW,IAAA,yJAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,mPAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QACC,KAAK;QACL,WAAW,IAAA,yJAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,mPAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QAAI,KAAK;QAAK,WAAW,IAAA,yJAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,mPAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QACC,KAAK;QACL,WAAW,IAAA,yJAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,IAAA,uMAAG,EACvB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8QAAC;QAAI,WAAW,IAAA,yJAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/SwingScanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, Search, TrendingUp, TrendingDown, Minus, Target, Shield, BarChart3 } from 'lucide-react'\nimport { ScanResult, ScanSummary } from '@/lib/swingScanner'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface ScannerProps {\n  autoScan?: boolean\n}\n\nexport function SwingScanner({ autoScan = false }: ScannerProps) {\n  const [isScanning, setIsScanning] = useState(false)\n  const [scanResults, setScanResults] = useState<ScanSummary | null>(null)\n  const [selectedScan, setSelectedScan] = useState<'quick' | 'full' | 'sector'>('quick')\n  const [selectedSector, setSelectedSector] = useState<string>('Technology')\n  const [error, setError] = useState<string | null>(null)\n\n  const sectors = [\n    'Technology', 'Financial Services', 'Healthcare', 'Industrial', \n    'Materials', 'Consumer', 'Communication Services', 'Energy'\n  ]\n\n  // Auto-scan on component mount if enabled\n  useEffect(() => {\n    if (autoScan) {\n      handleQuickScan()\n    }\n  }, [autoScan])\n\n  const handleQuickScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch('/api/scanner/quick?limit=15')\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      \n      // Convert to ScanSummary format\n      const summary: ScanSummary = {\n        totalScanned: data.totalScanned,\n        successfulScans: data.results.length,\n        failedScans: data.totalScanned - data.results.length,\n        topOpportunities: data.results,\n        sectorBreakdown: {},\n        scanDuration: 0\n      }\n      \n      setScanResults(summary)\n    } catch (err) {\n      setError('Failed to perform quick scan. Please try again.')\n      console.error('Quick scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const handleFullScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch('/api/scanner/full?limit=25&concurrent=3')\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform full scan. Please try again.')\n      console.error('Full scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const handleSectorScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch(`/api/scanner/sector/${encodeURIComponent(selectedSector)}?limit=15`)\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform sector scan. Please try again.')\n      console.error('Sector scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const getTrendIcon = (trend: string) => {\n    switch (trend) {\n      case 'BULLISH':\n        return <TrendingUp className=\"h-4 w-4 text-green-400\" />\n      case 'BEARISH':\n        return <TrendingDown className=\"h-4 w-4 text-red-400\" />\n      default:\n        return <Minus className=\"h-4 w-4 text-yellow-400\" />\n    }\n  }\n\n  const getRecommendationColor = (recommendation: string) => {\n    if (recommendation.includes('BUY')) return 'bg-green-500/20 text-green-400'\n    if (recommendation.includes('SELL')) return 'bg-red-500/20 text-red-400'\n    return 'bg-yellow-500/20 text-yellow-400'\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Scanner Controls */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center\">\n            <Search className=\"mr-2 h-5 w-5 text-blue-400\" />\n            Swing Trading Scanner\n          </CardTitle>\n          <CardDescription className=\"text-slate-300\">\n            Automatically scan stocks for the best swing trading opportunities\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex flex-wrap gap-4 mb-4\">\n            <Button\n              onClick={handleQuickScan}\n              disabled={isScanning}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              {isScanning && selectedScan === 'quick' ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : null}\n              Quick Scan (Top 16)\n            </Button>\n            \n            <Button\n              onClick={handleFullScan}\n              disabled={isScanning}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n            >\n              {isScanning && selectedScan === 'full' ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : null}\n              Full Scan (All 70+ Stocks)\n            </Button>\n\n            <div className=\"flex gap-2\">\n              <select\n                value={selectedSector}\n                onChange={(e) => setSelectedSector(e.target.value)}\n                className=\"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n                disabled={isScanning}\n              >\n                {sectors.map(sector => (\n                  <option key={sector} value={sector}>{sector}</option>\n                ))}\n              </select>\n              <Button\n                onClick={handleSectorScan}\n                disabled={isScanning}\n                variant=\"outline\"\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n              >\n                {isScanning && selectedScan === 'sector' ? (\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                ) : null}\n                Scan Sector\n              </Button>\n            </div>\n          </div>\n\n          {isScanning && (\n            <div className=\"text-center py-4\">\n              <Loader2 className=\"mx-auto h-8 w-8 animate-spin text-blue-400\" />\n              <p className=\"text-slate-300 mt-2\">\n                Scanning stocks for swing trading opportunities...\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Error Display */}\n      {error && (\n        <Card className=\"bg-red-900/20 border-red-500/50\">\n          <CardContent className=\"p-6\">\n            <p className=\"text-red-300 text-center\">{error}</p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Scan Results */}\n      {scanResults && (\n        <div className=\"space-y-6\">\n          {/* Scan Summary */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white\">Scan Summary</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-white\">{scanResults.totalScanned}</div>\n                  <div className=\"text-sm text-slate-300\">Total Scanned</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">{scanResults.successfulScans}</div>\n                  <div className=\"text-sm text-slate-300\">Successful</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-400\">{scanResults.failedScans}</div>\n                  <div className=\"text-sm text-slate-300\">Failed</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">\n                    {scanResults.scanDuration ? `${(scanResults.scanDuration / 1000).toFixed(1)}s` : 'N/A'}\n                  </div>\n                  <div className=\"text-sm text-slate-300\">Duration</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Top Opportunities */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white flex items-center\">\n                <TrendingUp className=\"mr-2 h-5 w-5 text-green-400\" />\n                Top Swing Trading Opportunities\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {scanResults.topOpportunities.map((result, index) => (\n                  <div key={result.symbol} className=\"p-4 bg-slate-700/50 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"text-lg font-bold text-white\">#{result.rank}</div>\n                        <div>\n                          <div className=\"flex items-center space-x-2\">\n                            <span className=\"text-lg font-semibold text-white\">{result.symbol}</span>\n                            {getTrendIcon(result.analysis.trend)}\n                            <Badge className={getRecommendationColor(result.analysis.recommendation)}>\n                              {result.analysis.recommendation.replace('_', ' ')}\n                            </Badge>\n                          </div>\n                          <div className=\"text-sm text-slate-300\">{result.name}</div>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-xl font-bold text-white\">\n                          {result.score.toFixed(1)}/100\n                        </div>\n                        <div className=\"text-sm text-slate-300\">Score</div>\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                      <div>\n                        <div className=\"text-slate-300\">Price</div>\n                        <div className=\"text-white font-semibold\">\n                          {formatCurrency(result.quote.price)}\n                        </div>\n                        <div className={result.quote.change >= 0 ? \"text-green-400\" : \"text-red-400\"}>\n                          {formatPercentage(result.quote.changePercent)}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">Entry</div>\n                        <div className=\"text-blue-400 font-semibold\">\n                          {formatCurrency(result.analysis.entryPrice)}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">R/R Ratio</div>\n                        <div className=\"text-green-400 font-semibold\">\n                          {result.analysis.riskRewardRatio.toFixed(2)}:1\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">Confidence</div>\n                        <div className=\"text-white font-semibold\">\n                          {result.analysis.confidence.toFixed(1)}%\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AARA;;;;;;;;AAcO,SAAS,aAAa,EAAE,WAAW,KAAK,EAAgB;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iPAAQ,EAAC;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iPAAQ,EAAqB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iPAAQ,EAA8B;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iPAAQ,EAAS;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iPAAQ,EAAgB;IAElD,MAAM,UAAU;QACd;QAAc;QAAsB;QAAc;QAClD;QAAa;QAAY;QAA0B;KACpD;IAED,0CAA0C;IAC1C,IAAA,kPAAS,EAAC;QACR,IAAI,UAAU;YACZ;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB;QACtB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,gCAAgC;YAChC,MAAM,UAAuB;gBAC3B,cAAc,KAAK,YAAY;gBAC/B,iBAAiB,KAAK,OAAO,CAAC,MAAM;gBACpC,aAAa,KAAK,YAAY,GAAG,KAAK,OAAO,CAAC,MAAM;gBACpD,kBAAkB,KAAK,OAAO;gBAC9B,iBAAiB,CAAC;gBAClB,cAAc;YAChB;YAEA,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,oBAAoB;QACpC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB;QACvB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,mBAAmB,gBAAgB,SAAS,CAAC;YACjG,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8QAAC,gQAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,8QAAC,sQAAY;oBAAC,WAAU;;;;;;YACjC;gBACE,qBAAO,8QAAC,6OAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,IAAI,eAAe,QAAQ,CAAC,QAAQ,OAAO;QAC3C,IAAI,eAAe,QAAQ,CAAC,SAAS,OAAO;QAC5C,OAAO;IACT;IAEA,qBACE,8QAAC;QAAI,WAAU;;0BAEb,8QAAC,wKAAI;gBAAC,WAAU;;kCACd,8QAAC,8KAAU;;0CACT,8QAAC,6KAAS;gCAAC,WAAU;;kDACnB,8QAAC,gPAAM;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGnD,8QAAC,mLAAe;gCAAC,WAAU;0CAAiB;;;;;;;;;;;;kCAI9C,8QAAC,+KAAW;;0CACV,8QAAC;gCAAI,WAAU;;kDACb,8QAAC,4KAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;;4CAET,cAAc,iBAAiB,wBAC9B,8QAAC,4PAAO;gDAAC,WAAU;;;;;uDACjB;4CAAK;;;;;;;kDAIX,8QAAC,4KAAM;wCACL,SAAS;wCACT,UAAU;wCACV,SAAQ;wCACR,WAAU;;4CAET,cAAc,iBAAiB,uBAC9B,8QAAC,4PAAO;gDAAC,WAAU;;;;;uDACjB;4CAAK;;;;;;;kDAIX,8QAAC;wCAAI,WAAU;;0DACb,8QAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,WAAU;gDACV,UAAU;0DAET,QAAQ,GAAG,CAAC,CAAA,uBACX,8QAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;0DAGjB,8QAAC,4KAAM;gDACL,SAAS;gDACT,UAAU;gDACV,SAAQ;gDACR,WAAU;;oDAET,cAAc,iBAAiB,yBAC9B,8QAAC,4PAAO;wDAAC,WAAU;;;;;+DACjB;oDAAK;;;;;;;;;;;;;;;;;;;4BAMd,4BACC,8QAAC;gCAAI,WAAU;;kDACb,8QAAC,4PAAO;wCAAC,WAAU;;;;;;kDACnB,8QAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,uBACC,8QAAC,wKAAI;gBAAC,WAAU;0BACd,cAAA,8QAAC,+KAAW;oBAAC,WAAU;8BACrB,cAAA,8QAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;YAM9C,6BACC,8QAAC;gBAAI,WAAU;;kCAEb,8QAAC,wKAAI;wBAAC,WAAU;;0CACd,8QAAC,8KAAU;0CACT,cAAA,8QAAC,6KAAS;oCAAC,WAAU;8CAAa;;;;;;;;;;;0CAEpC,8QAAC,+KAAW;0CACV,cAAA,8QAAC;oCAAI,WAAU;;sDACb,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAiC,YAAY,YAAY;;;;;;8DACxE,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAqC,YAAY,eAAe;;;;;;8DAC/E,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAmC,YAAY,WAAW;;;;;;8DACzE,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DACZ,YAAY,YAAY,GAAG,GAAG,CAAC,YAAY,YAAY,GAAG,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;8DAEnF,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhD,8QAAC,wKAAI;wBAAC,WAAU;;0CACd,8QAAC,8KAAU;0CACT,cAAA,8QAAC,6KAAS;oCAAC,WAAU;;sDACnB,8QAAC,gQAAU;4CAAC,WAAU;;;;;;wCAAgC;;;;;;;;;;;;0CAI1D,8QAAC,+KAAW;0CACV,cAAA,8QAAC;oCAAI,WAAU;8CACZ,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzC,8QAAC;4CAAwB,WAAU;;8DACjC,8QAAC;oDAAI,WAAU;;sEACb,8QAAC;4DAAI,WAAU;;8EACb,8QAAC;oEAAI,WAAU;;wEAA+B;wEAAE,OAAO,IAAI;;;;;;;8EAC3D,8QAAC;;sFACC,8QAAC;4EAAI,WAAU;;8FACb,8QAAC;oFAAK,WAAU;8FAAoC,OAAO,MAAM;;;;;;gFAChE,aAAa,OAAO,QAAQ,CAAC,KAAK;8FACnC,8QAAC,0KAAK;oFAAC,WAAW,uBAAuB,OAAO,QAAQ,CAAC,cAAc;8FACpE,OAAO,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sFAGjD,8QAAC;4EAAI,WAAU;sFAA0B,OAAO,IAAI;;;;;;;;;;;;;;;;;;sEAGxD,8QAAC;4DAAI,WAAU;;8EACb,8QAAC;oEAAI,WAAU;;wEACZ,OAAO,KAAK,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAE3B,8QAAC;oEAAI,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;8DAI5C,8QAAC;oDAAI,WAAU;;sEACb,8QAAC;;8EACC,8QAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,8QAAC;oEAAI,WAAU;8EACZ,IAAA,qKAAc,EAAC,OAAO,KAAK,CAAC,KAAK;;;;;;8EAEpC,8QAAC;oEAAI,WAAW,OAAO,KAAK,CAAC,MAAM,IAAI,IAAI,mBAAmB;8EAC3D,IAAA,uKAAgB,EAAC,OAAO,KAAK,CAAC,aAAa;;;;;;;;;;;;sEAGhD,8QAAC;;8EACC,8QAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,8QAAC;oEAAI,WAAU;8EACZ,IAAA,qKAAc,EAAC,OAAO,QAAQ,CAAC,UAAU;;;;;;;;;;;;sEAG9C,8QAAC;;8EACC,8QAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,8QAAC;oEAAI,WAAU;;wEACZ,OAAO,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAGhD,8QAAC;;8EACC,8QAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,8QAAC;oEAAI,WAAU;;wEACZ,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;2CAhDrC,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DzC", "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/StrategyScanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, Zap, Target, Clock, AlertTriangle, <PERSON><PERSON>dingUp, Moon, BarChart3 } from 'lucide-react'\nimport { EnhancedScanResult, StrategyScanSummary } from '@/lib/enhancedSwingScanner'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface StrategyScannerProps {\n  autoScan?: boolean\n  accountSize?: number\n}\n\nexport function StrategyScanner({ autoScan = false, accountSize = 100000 }: StrategyScannerProps) {\n  const [isScanning, setIsScanning] = useState(false)\n  const [scanResults, setScanResults] = useState<StrategyScanSummary | null>(null)\n  const [selectedStrategy, setSelectedStrategy] = useState<'both' | 'overnight' | 'breakout'>('both')\n  const [error, setError] = useState<string | null>(null)\n  const [userAccountSize, setUserAccountSize] = useState(accountSize)\n\n  // Auto-scan on component mount if enabled\n  useEffect(() => {\n    if (autoScan) {\n      handleStrategyScan('quick')\n    }\n  }, [autoScan])\n\n  const handleStrategyScan = async (scanType: 'quick' | 'full' | 'test') => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      let response\n      if (scanType === 'test') {\n        response = await fetch('/api/scanner/test')\n      } else {\n        response = await fetch(\n          `/api/scanner/strategies?type=${scanType}&accountSize=${userAccountSize}&limit=20`\n        )\n      }\n\n      if (!response.ok) throw new Error('Failed to fetch strategy scan results')\n\n      const data = await response.json()\n      console.log('📊 API Response:', data) // Debug logging\n      console.log('📊 topSetups:', data?.topSetups) // Debug logging\n      console.log('📊 Setting scan results...') // Debug logging\n      setScanResults(data)\n      console.log('📊 Scan results set!') // Debug logging\n    } catch (err) {\n      setError('Failed to perform strategy scan. Please try again.')\n      console.error('Strategy scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const getStrategyIcon = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return <Moon className=\"h-4 w-4 text-purple-400\" />\n      case 'technical_breakout':\n        return <TrendingUp className=\"h-4 w-4 text-green-400\" />\n      default:\n        return <BarChart3 className=\"h-4 w-4 text-blue-400\" />\n    }\n  }\n\n  const getStrategyName = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return 'Overnight Momentum'\n      case 'technical_breakout':\n        return 'Technical Breakout'\n      default:\n        return 'Mixed Strategy'\n    }\n  }\n\n  const getStrategyColor = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return 'bg-purple-500/20 text-purple-400'\n      case 'technical_breakout':\n        return 'bg-green-500/20 text-green-400'\n      default:\n        return 'bg-blue-500/20 text-blue-400'\n    }\n  }\n\n  const filteredResults = scanResults?.topSetups?.filter(result => {\n    if (selectedStrategy === 'both') return true\n    if (selectedStrategy === 'overnight') return result.overnightSetup\n    if (selectedStrategy === 'breakout') return result.breakoutSetup\n    return true\n  }) || []\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Strategy Scanner Controls */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center\">\n            <Zap className=\"mr-2 h-5 w-5 text-yellow-400\" />\n            Professional Swing Trading Strategies\n          </CardTitle>\n          <CardDescription className=\"text-slate-300\">\n            Automated scanner implementing proven swing trading methodologies with precise entry/exit rules\n            <br />\n            <span className=\"text-xs text-yellow-400\">\n              Status: {isScanning ? 'Scanning...' : scanResults ? `Found ${scanResults.totalScanned} stocks, ${scanResults.overnightSetups} setups` : 'Ready to scan'}\n            </span>\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {/* Account Size Input */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm text-slate-300 mb-2\">Account Size (for position sizing)</label>\n            <input\n              type=\"number\"\n              value={userAccountSize}\n              onChange={(e) => setUserAccountSize(parseInt(e.target.value) || 100000)}\n              className=\"w-32 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n              min=\"10000\"\n              step=\"10000\"\n              disabled={isScanning}\n            />\n          </div>\n\n          {/* Strategy Filter */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm text-slate-300 mb-2\">Strategy Filter</label>\n            <div className=\"flex gap-2\">\n              <Button\n                variant={selectedStrategy === 'both' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('both')}\n                className={selectedStrategy === 'both' ? 'bg-blue-600' : 'border-slate-600 text-slate-300'}\n              >\n                All Strategies\n              </Button>\n              <Button\n                variant={selectedStrategy === 'overnight' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('overnight')}\n                className={selectedStrategy === 'overnight' ? 'bg-purple-600' : 'border-slate-600 text-slate-300'}\n              >\n                <Moon className=\"mr-1 h-3 w-3\" />\n                Overnight\n              </Button>\n              <Button\n                variant={selectedStrategy === 'breakout' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('breakout')}\n                className={selectedStrategy === 'breakout' ? 'bg-green-600' : 'border-slate-600 text-slate-300'}\n              >\n                <TrendingUp className=\"mr-1 h-3 w-3\" />\n                Breakout\n              </Button>\n            </div>\n          </div>\n\n          {/* Scan Buttons */}\n          <div className=\"flex gap-4 mb-4\">\n            <Button\n              onClick={() => handleStrategyScan('quick')}\n              disabled={isScanning}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              {isScanning ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : (\n                <Zap className=\"mr-2 h-4 w-4\" />\n              )}\n              Quick Strategy Scan\n            </Button>\n            \n            <Button\n              onClick={() => handleStrategyScan('full')}\n              disabled={isScanning}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n            >\n              {isScanning ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : (\n                <Target className=\"mr-2 h-4 w-4\" />\n              )}\n              Full Strategy Scan\n            </Button>\n\n            <Button\n              onClick={() => handleStrategyScan('test')}\n              disabled={isScanning}\n              variant=\"outline\"\n              className=\"bg-green-600/10 border-green-600 text-green-400 hover:bg-green-600/20\"\n            >\n              {isScanning ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : (\n                <span className=\"mr-2\">🧪</span>\n              )}\n              Test\n            </Button>\n          </div>\n\n          {/* Market Conditions */}\n          {scanResults?.marketConditions && (\n            <div className=\"p-3 bg-slate-700/50 rounded-lg\">\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-slate-300\">Market Conditions:</span>\n                <div className=\"flex items-center gap-4\">\n                  <span className=\"text-white\">{scanResults.marketConditions.timeOfDay}</span>\n                  <Badge className={scanResults.marketConditions.isOptimalScanTime \n                    ? 'bg-green-500/20 text-green-400' \n                    : 'bg-yellow-500/20 text-yellow-400'\n                  }>\n                    {scanResults.marketConditions.isOptimalScanTime ? 'Optimal Scan Time' : 'Outside Optimal Hours'}\n                  </Badge>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {isScanning && (\n            <div className=\"text-center py-4\">\n              <Loader2 className=\"mx-auto h-8 w-8 animate-spin text-blue-400\" />\n              <p className=\"text-slate-300 mt-2\">\n                Analyzing stocks for professional swing trading setups...\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Error Display */}\n      {error && (\n        <Card className=\"bg-red-900/20 border-red-500/50\">\n          <CardContent className=\"p-6\">\n            <p className=\"text-red-300 text-center\">{error}</p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Strategy Scan Results */}\n      {scanResults && (\n        <div className=\"space-y-6\">\n          {console.log('📊 Rendering results:', scanResults)}\n          {/* Scan Summary */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white\">Strategy Scan Summary</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-white\">{scanResults.totalScanned || 0}</div>\n                  <div className=\"text-sm text-slate-300\">Stocks Scanned</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-400\">{scanResults.overnightSetups || 0}</div>\n                  <div className=\"text-sm text-slate-300\">Overnight Setups</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">{scanResults.breakoutSetups || 0}</div>\n                  <div className=\"text-sm text-slate-300\">Breakout Setups</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">{scanResults.bothStrategies || 0}</div>\n                  <div className=\"text-sm text-slate-300\">Both Strategies</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Strategy Setups */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white flex items-center\">\n                <Target className=\"mr-2 h-5 w-5 text-green-400\" />\n                Professional Trading Setups ({filteredResults.length})\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {filteredResults.map((result, index) => (\n                  <div key={result.symbol} className=\"p-4 bg-slate-700/50 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"text-lg font-bold text-white\">#{result.rank}</div>\n                        <div>\n                          <div className=\"flex items-center space-x-2\">\n                            <span className=\"text-lg font-semibold text-white\">{result.symbol}</span>\n                            {result.bestStrategy && getStrategyIcon(result.bestStrategy)}\n                            <Badge className={result.bestStrategy ? getStrategyColor(result.bestStrategy) : 'bg-gray-500/20 text-gray-400'}>\n                              {result.bestStrategy ? getStrategyName(result.bestStrategy) : 'No Setup'}\n                            </Badge>\n                          </div>\n                          <div className=\"text-sm text-slate-300\">{result.name}</div>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-xl font-bold text-white\">\n                          {result.overallScore.toFixed(1)}/100\n                        </div>\n                        <div className=\"text-sm text-slate-300\">Confidence</div>\n                      </div>\n                    </div>\n\n                    {/* Strategy Details */}\n                    {result.overnightSetup && (\n                      <div className=\"mb-3 p-3 bg-purple-900/20 rounded border border-purple-500/30\">\n                        <div className=\"flex items-center mb-2\">\n                          <Moon className=\"mr-2 h-4 w-4 text-purple-400\" />\n                          <span className=\"text-purple-400 font-semibold\">Overnight Momentum Strategy</span>\n                          <Badge className=\"ml-2 bg-purple-500/20 text-purple-400\">\n                            {result.overnightSetup.confidence.toFixed(1)}% confidence\n                          </Badge>\n                        </div>\n                        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm\">\n                          <div>\n                            <div className=\"text-slate-300\">Entry</div>\n                            <div className=\"text-white font-semibold\">{formatCurrency(result.overnightSetup.entryPrice)}</div>\n                          </div>\n                          <div>\n                            <div className=\"text-slate-300\">Stop</div>\n                            <div className=\"text-red-400 font-semibold\">{formatCurrency(result.overnightSetup.stopLoss)}</div>\n                          </div>\n                          <div>\n                            <div className=\"text-slate-300\">Target</div>\n                            <div className=\"text-green-400 font-semibold\">{formatCurrency(result.overnightSetup.targets[0])}</div>\n                          </div>\n                          <div>\n                            <div className=\"text-slate-300\">Position Size</div>\n                            <div className=\"text-white font-semibold\">{result.overnightSetup.positionSize.toLocaleString()} shares</div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {result.breakoutSetup && (\n                      <div className=\"mb-3 p-3 bg-green-900/20 rounded border border-green-500/30\">\n                        <div className=\"flex items-center mb-2\">\n                          <TrendingUp className=\"mr-2 h-4 w-4 text-green-400\" />\n                          <span className=\"text-green-400 font-semibold\">Technical Breakout Strategy</span>\n                          <Badge className=\"ml-2 bg-green-500/20 text-green-400\">\n                            {result.breakoutSetup.confidence.toFixed(1)}% confidence\n                          </Badge>\n                        </div>\n                        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm\">\n                          <div>\n                            <div className=\"text-slate-300\">Entry</div>\n                            <div className=\"text-white font-semibold\">{formatCurrency(result.breakoutSetup.entryPrice)}</div>\n                          </div>\n                          <div>\n                            <div className=\"text-slate-300\">8-EMA Stop</div>\n                            <div className=\"text-red-400 font-semibold\">{formatCurrency(result.breakoutSetup.stopLoss)}</div>\n                          </div>\n                          <div>\n                            <div className=\"text-slate-300\">First Target</div>\n                            <div className=\"text-green-400 font-semibold\">{formatCurrency(result.breakoutSetup.targets[0])}</div>\n                          </div>\n                          <div>\n                            <div className=\"text-slate-300\">Position Size</div>\n                            <div className=\"text-white font-semibold\">{result.breakoutSetup.positionSize.toLocaleString()} shares</div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Alerts and Warnings */}\n                    {result.alerts.length > 0 && (\n                      <div className=\"mb-2\">\n                        <div className=\"text-sm text-slate-300 mb-1\">Trading Alerts:</div>\n                        {result.alerts.map((alert, i) => (\n                          <div key={i} className=\"text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded mb-1\">\n                            {alert}\n                          </div>\n                        ))}\n                      </div>\n                    )}\n\n                    {result.riskWarnings.length > 0 && (\n                      <div>\n                        <div className=\"text-sm text-slate-300 mb-1\">Risk Warnings:</div>\n                        {result.riskWarnings.map((warning, i) => (\n                          <div key={i} className=\"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded mb-1 flex items-center\">\n                            <AlertTriangle className=\"mr-1 h-3 w-3\" />\n                            {warning}\n                          </div>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AARA;;;;;;;;AAeO,SAAS,gBAAgB,EAAE,WAAW,KAAK,EAAE,cAAc,MAAM,EAAwB;IAC9F,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iPAAQ,EAAC;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iPAAQ,EAA6B;IAC3E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,iPAAQ,EAAoC;IAC5F,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iPAAQ,EAAgB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iPAAQ,EAAC;IAEvD,0CAA0C;IAC1C,IAAA,kPAAS,EAAC;QACR,IAAI,UAAU;YACZ,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,qBAAqB,OAAO;QAChC,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,IAAI;YACJ,IAAI,aAAa,QAAQ;gBACvB,WAAW,MAAM,MAAM;YACzB,OAAO;gBACL,WAAW,MAAM,MACf,CAAC,6BAA6B,EAAE,SAAS,aAAa,EAAE,gBAAgB,SAAS,CAAC;YAEtF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,oBAAoB,OAAM,gBAAgB;YACtD,QAAQ,GAAG,CAAC,iBAAiB,MAAM,YAAW,gBAAgB;YAC9D,QAAQ,GAAG,CAAC,+BAA8B,gBAAgB;YAC1D,eAAe;YACf,QAAQ,GAAG,CAAC,yBAAwB,gBAAgB;QACtD,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,8QAAC,0OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8QAAC,gQAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,8QAAC,+PAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,aAAa,WAAW,OAAO,CAAA;QACrD,IAAI,qBAAqB,QAAQ,OAAO;QACxC,IAAI,qBAAqB,aAAa,OAAO,OAAO,cAAc;QAClE,IAAI,qBAAqB,YAAY,OAAO,OAAO,aAAa;QAChE,OAAO;IACT,MAAM,EAAE;IAER,qBACE,8QAAC;QAAI,WAAU;;0BAEb,8QAAC,wKAAI;gBAAC,WAAU;;kCACd,8QAAC,8KAAU;;0CACT,8QAAC,6KAAS;gCAAC,WAAU;;kDACnB,8QAAC,uOAAG;wCAAC,WAAU;;;;;;oCAAiC;;;;;;;0CAGlD,8QAAC,mLAAe;gCAAC,WAAU;;oCAAiB;kDAE1C,8QAAC;;;;;kDACD,8QAAC;wCAAK,WAAU;;4CAA0B;4CAC/B,aAAa,gBAAgB,cAAc,CAAC,MAAM,EAAE,YAAY,YAAY,CAAC,SAAS,EAAE,YAAY,eAAe,CAAC,OAAO,CAAC,GAAG;;;;;;;;;;;;;;;;;;;kCAI9I,8QAAC,+KAAW;;0CAEV,8QAAC;gCAAI,WAAU;;kDACb,8QAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8QAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wCAChE,WAAU;wCACV,KAAI;wCACJ,MAAK;wCACL,UAAU;;;;;;;;;;;;0CAKd,8QAAC;gCAAI,WAAU;;kDACb,8QAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8QAAC;wCAAI,WAAU;;0DACb,8QAAC,4KAAM;gDACL,SAAS,qBAAqB,SAAS,YAAY;gDACnD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,SAAS,gBAAgB;0DAC1D;;;;;;0DAGD,8QAAC,4KAAM;gDACL,SAAS,qBAAqB,cAAc,YAAY;gDACxD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,cAAc,kBAAkB;;kEAEhE,8QAAC,0OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,8QAAC,4KAAM;gDACL,SAAS,qBAAqB,aAAa,YAAY;gDACvD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,aAAa,iBAAiB;;kEAE9D,8QAAC,gQAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAO7C,8QAAC;gCAAI,WAAU;;kDACb,8QAAC,4KAAM;wCACL,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,WAAU;;4CAET,2BACC,8QAAC,4PAAO;gDAAC,WAAU;;;;;qEAEnB,8QAAC,uOAAG;gDAAC,WAAU;;;;;;4CACf;;;;;;;kDAIJ,8QAAC,4KAAM;wCACL,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,SAAQ;wCACR,WAAU;;4CAET,2BACC,8QAAC,4PAAO;gDAAC,WAAU;;;;;qEAEnB,8QAAC,gPAAM;gDAAC,WAAU;;;;;;4CAClB;;;;;;;kDAIJ,8QAAC,4KAAM;wCACL,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,SAAQ;wCACR,WAAU;;4CAET,2BACC,8QAAC,4PAAO;gDAAC,WAAU;;;;;qEAEnB,8QAAC;gDAAK,WAAU;0DAAO;;;;;;4CACvB;;;;;;;;;;;;;4BAML,aAAa,kCACZ,8QAAC;gCAAI,WAAU;0CACb,cAAA,8QAAC;oCAAI,WAAU;;sDACb,8QAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAK,WAAU;8DAAc,YAAY,gBAAgB,CAAC,SAAS;;;;;;8DACpE,8QAAC,0KAAK;oDAAC,WAAW,YAAY,gBAAgB,CAAC,iBAAiB,GAC5D,mCACA;8DAED,YAAY,gBAAgB,CAAC,iBAAiB,GAAG,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;4BAOjF,4BACC,8QAAC;gCAAI,WAAU;;kDACb,8QAAC,4PAAO;wCAAC,WAAU;;;;;;kDACnB,8QAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,uBACC,8QAAC,wKAAI;gBAAC,WAAU;0BACd,cAAA,8QAAC,+KAAW;oBAAC,WAAU;8BACrB,cAAA,8QAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;YAM9C,6BACC,8QAAC;gBAAI,WAAU;;oBACZ,QAAQ,GAAG,CAAC,yBAAyB;kCAEtC,8QAAC,wKAAI;wBAAC,WAAU;;0CACd,8QAAC,8KAAU;0CACT,cAAA,8QAAC,6KAAS;oCAAC,WAAU;8CAAa;;;;;;;;;;;0CAEpC,8QAAC,+KAAW;0CACV,cAAA,8QAAC;oCAAI,WAAU;;sDACb,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAiC,YAAY,YAAY,IAAI;;;;;;8DAC5E,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAsC,YAAY,eAAe,IAAI;;;;;;8DACpF,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAqC,YAAY,cAAc,IAAI;;;;;;8DAClF,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAoC,YAAY,cAAc,IAAI;;;;;;8DACjF,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhD,8QAAC,wKAAI;wBAAC,WAAU;;0CACd,8QAAC,8KAAU;0CACT,cAAA,8QAAC,6KAAS;oCAAC,WAAU;;sDACnB,8QAAC,gPAAM;4CAAC,WAAU;;;;;;wCAAgC;wCACpB,gBAAgB,MAAM;wCAAC;;;;;;;;;;;;0CAGzD,8QAAC,+KAAW;0CACV,cAAA,8QAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8QAAC;4CAAwB,WAAU;;8DACjC,8QAAC;oDAAI,WAAU;;sEACb,8QAAC;4DAAI,WAAU;;8EACb,8QAAC;oEAAI,WAAU;;wEAA+B;wEAAE,OAAO,IAAI;;;;;;;8EAC3D,8QAAC;;sFACC,8QAAC;4EAAI,WAAU;;8FACb,8QAAC;oFAAK,WAAU;8FAAoC,OAAO,MAAM;;;;;;gFAChE,OAAO,YAAY,IAAI,gBAAgB,OAAO,YAAY;8FAC3D,8QAAC,0KAAK;oFAAC,WAAW,OAAO,YAAY,GAAG,iBAAiB,OAAO,YAAY,IAAI;8FAC7E,OAAO,YAAY,GAAG,gBAAgB,OAAO,YAAY,IAAI;;;;;;;;;;;;sFAGlE,8QAAC;4EAAI,WAAU;sFAA0B,OAAO,IAAI;;;;;;;;;;;;;;;;;;sEAGxD,8QAAC;4DAAI,WAAU;;8EACb,8QAAC;oEAAI,WAAU;;wEACZ,OAAO,YAAY,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAElC,8QAAC;oEAAI,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;gDAK3C,OAAO,cAAc,kBACpB,8QAAC;oDAAI,WAAU;;sEACb,8QAAC;4DAAI,WAAU;;8EACb,8QAAC,0OAAI;oEAAC,WAAU;;;;;;8EAChB,8QAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAChD,8QAAC,0KAAK;oEAAC,WAAU;;wEACd,OAAO,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAGjD,8QAAC;4DAAI,WAAU;;8EACb,8QAAC;;sFACC,8QAAC;4EAAI,WAAU;sFAAiB;;;;;;sFAChC,8QAAC;4EAAI,WAAU;sFAA4B,2KAAe,OAAO,cAAc,CAAC,UAAU;;;;;;;;;;;;8EAE5F,8QAAC;;sFACC,8QAAC;4EAAI,WAAU;sFAAiB;;;;;;sFAChC,8QAAC;4EAAI,WAAU;sFAA8B,2KAAe,OAAO,cAAc,CAAC,QAAQ;;;;;;;;;;;;8EAE5F,8QAAC;;sFACC,8QAAC;4EAAI,WAAU;sFAAiB;;;;;;sFAChC,8QAAC;4EAAI,WAAU;sFAAgC,2KAAe,OAAO,cAAc,CAAC,OAAO,CAAC,EAAE;;;;;;;;;;;;8EAEhG,8QAAC;;sFACC,8QAAC;4EAAI,WAAU;sFAAiB;;;;;;sFAChC,8QAAC;4EAAI,WAAU;;gFAA4B,OAAO,cAAc,CAAC,YAAY,CAAC,cAAc;gFAAG;;;;;;;;;;;;;;;;;;;;;;;;;gDAMtG,OAAO,aAAa,kBACnB,8QAAC;oDAAI,WAAU;;sEACb,8QAAC;4DAAI,WAAU;;8EACb,8QAAC,gQAAU;oEAAC,WAAU;;;;;;8EACtB,8QAAC;oEAAK,WAAU;8EAA+B;;;;;;8EAC/C,8QAAC,0KAAK;oEAAC,WAAU;;wEACd,OAAO,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAGhD,8QAAC;4DAAI,WAAU;;8EACb,8QAAC;;sFACC,8QAAC;4EAAI,WAAU;sFAAiB;;;;;;sFAChC,8QAAC;4EAAI,WAAU;sFAA4B,2KAAe,OAAO,aAAa,CAAC,UAAU;;;;;;;;;;;;8EAE3F,8QAAC;;sFACC,8QAAC;4EAAI,WAAU;sFAAiB;;;;;;sFAChC,8QAAC;4EAAI,WAAU;sFAA8B,2KAAe,OAAO,aAAa,CAAC,QAAQ;;;;;;;;;;;;8EAE3F,8QAAC;;sFACC,8QAAC;4EAAI,WAAU;sFAAiB;;;;;;sFAChC,8QAAC;4EAAI,WAAU;sFAAgC,2KAAe,OAAO,aAAa,CAAC,OAAO,CAAC,EAAE;;;;;;;;;;;;8EAE/F,8QAAC;;sFACC,8QAAC;4EAAI,WAAU;sFAAiB;;;;;;sFAChC,8QAAC;4EAAI,WAAU;;gFAA4B,OAAO,aAAa,CAAC,YAAY,CAAC,cAAc;gFAAG;;;;;;;;;;;;;;;;;;;;;;;;;gDAOrG,OAAO,MAAM,CAAC,MAAM,GAAG,mBACtB,8QAAC;oDAAI,WAAU;;sEACb,8QAAC;4DAAI,WAAU;sEAA8B;;;;;;wDAC5C,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,kBACzB,8QAAC;gEAAY,WAAU;0EACpB;+DADO;;;;;;;;;;;gDAOf,OAAO,YAAY,CAAC,MAAM,GAAG,mBAC5B,8QAAC;;sEACC,8QAAC;4DAAI,WAAU;sEAA8B;;;;;;wDAC5C,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,SAAS,kBACjC,8QAAC;gEAAY,WAAU;;kFACrB,8QAAC,yQAAa;wEAAC,WAAU;;;;;;oEACxB;;+DAFO;;;;;;;;;;;;2CApGR,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoHzC", "debugId": null}}, {"offset": {"line": 2158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON><PERSON>ding<PERSON>p, BarChart3, Target, Shield, Brain, Zap, Search, Loader2, Scan } from 'lucide-react'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { SwingTradingAnalysis, StockData } from '@/types/trading'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\nimport { SwingScanner } from '@/components/SwingScanner'\nimport { StrategyScanner } from '@/components/StrategyScanner'\n\nexport default function Home() {\n  const [selectedSymbol, setSelectedSymbol] = useState('SPY')\n  const [customSymbol, setCustomSymbol] = useState('')\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [analysis, setAnalysis] = useState<SwingTradingAnalysis | null>(null)\n  const [stockData, setStockData] = useState<StockData | null>(null)\n  const [error, setError] = useState<string | null>(null)\n  const [activeTab, setActiveTab] = useState<'individual' | 'scanner' | 'strategies'>('strategies')\n\n  const popularSymbols = ['SPY', 'QQQ', 'AAPL', 'TSLA', 'NVDA', 'MSFT', 'AMZN', 'GOOGL']\n\n  const handleAnalysis = async (symbol: string) => {\n    setIsAnalyzing(true)\n    setError(null)\n    setAnalysis(null)\n    setStockData(null)\n\n    try {\n      // Fetch stock quote and analysis in parallel\n      const [quoteResponse, analysisResponse] = await Promise.all([\n        fetch(`/api/stocks/quote/${symbol}`),\n        fetch(`/api/analysis/swing/${symbol}`)\n      ])\n\n      if (!quoteResponse.ok) {\n        const quoteError = await quoteResponse.text()\n        console.error('Quote API error:', quoteError)\n        throw new Error(`Failed to fetch quote data: ${quoteResponse.status}`)\n      }\n\n      if (!analysisResponse.ok) {\n        const analysisError = await analysisResponse.text()\n        console.error('Analysis API error:', analysisError)\n        throw new Error(`Failed to fetch analysis data: ${analysisResponse.status}`)\n      }\n\n      const [quoteData, analysisData] = await Promise.all([\n        quoteResponse.json(),\n        analysisResponse.json()\n      ])\n\n      setStockData(quoteData)\n      setAnalysis(analysisData)\n    } catch (err: any) {\n      const errorMessage = err.message?.includes('rate limit') || err.message?.includes('429')\n        ? 'API rate limit reached. Please wait a moment and try again.'\n        : 'Failed to analyze stock. Please try again.'\n      setError(errorMessage)\n      console.error('Analysis error:', err)\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  const handleCustomSymbolSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (customSymbol.trim()) {\n      handleAnalysis(customSymbol.toUpperCase())\n      setSelectedSymbol(customSymbol.toUpperCase())\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900\">\n      {/* Header */}\n      <header className=\"border-b border-slate-800 bg-slate-900/50 backdrop-blur-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <Brain className=\"h-8 w-8 text-blue-400\" />\n              <h1 className=\"text-2xl font-bold text-white\">SwingTrader AI</h1>\n            </div>\n            <nav className=\"hidden md:flex items-center space-x-6\">\n              <button\n                onClick={() => setActiveTab('strategies')}\n                className={`transition-colors ${activeTab === 'strategies' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Pro Strategies\n              </button>\n              <button\n                onClick={() => setActiveTab('scanner')}\n                className={`transition-colors ${activeTab === 'scanner' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Basic Scanner\n              </button>\n              <button\n                onClick={() => setActiveTab('individual')}\n                className={`transition-colors ${activeTab === 'individual' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Individual Analysis\n              </button>\n              <Button variant=\"outline\" className=\"border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white\">\n                Sign In\n              </Button>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20 px-4\">\n        <div className=\"container mx-auto text-center\">\n          <h2 className=\"text-5xl font-bold text-white mb-6\">\n            AI-Powered Swing Trading Analysis\n          </h2>\n          <p className=\"text-xl text-slate-300 mb-8 max-w-3xl mx-auto\">\n            Professional swing trading strategies with automated scanning, precise entry/exit rules,\n            and risk management based on proven methodologies.\n          </p>\n\n          {/* Tab Navigation */}\n          <div className=\"flex justify-center mb-8\">\n            <div className=\"bg-slate-800/50 rounded-lg p-1 flex\">\n              <button\n                onClick={() => setActiveTab('strategies')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'strategies'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Zap className=\"inline mr-2 h-4 w-4\" />\n                Pro Strategies\n              </button>\n              <button\n                onClick={() => setActiveTab('scanner')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'scanner'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Scan className=\"inline mr-2 h-4 w-4\" />\n                Basic Scanner\n              </button>\n              <button\n                onClick={() => setActiveTab('individual')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'individual'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Search className=\"inline mr-2 h-4 w-4\" />\n                Individual Analysis\n              </button>\n            </div>\n          </div>\n\n          {/* Content based on active tab */}\n          {activeTab === 'strategies' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Professional Swing Trading Strategies</h3>\n              <p className=\"text-slate-400 mb-6\">\n                Overnight Momentum & Technical Breakout strategies with precise entry/exit rules and position sizing\n              </p>\n            </div>\n          ) : activeTab === 'scanner' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Basic Swing Trading Scanner</h3>\n              <p className=\"text-slate-400 mb-6\">\n                General swing trading analysis with technical indicators and trend detection\n              </p>\n            </div>\n          ) : (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Individual Stock Analysis</h3>\n              <div className=\"flex flex-wrap justify-center gap-2 mb-6\">\n                {popularSymbols.map((symbol) => (\n                  <Button\n                    key={symbol}\n                    variant={selectedSymbol === symbol ? \"default\" : \"outline\"}\n                    onClick={() => {\n                      setSelectedSymbol(symbol)\n                      handleAnalysis(symbol)\n                    }}\n                    disabled={isAnalyzing}\n                    className={selectedSymbol === symbol\n                      ? \"bg-blue-600 hover:bg-blue-700\"\n                      : \"border-slate-600 text-slate-300 hover:bg-slate-800\"\n                    }\n                  >\n                    {symbol}\n                  </Button>\n                ))}\n              </div>\n\n              {/* Custom Symbol Input */}\n              <form onSubmit={handleCustomSymbolSubmit} className=\"flex justify-center gap-2 mb-6\">\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Enter symbol (e.g., AAPL)\"\n                    value={customSymbol}\n                    onChange={(e) => setCustomSymbol(e.target.value)}\n                    className=\"pl-10 pr-4 py-2 bg-slate-800 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    disabled={isAnalyzing}\n                  />\n                </div>\n                <Button\n                  type=\"submit\"\n                  disabled={isAnalyzing || !customSymbol.trim()}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  Analyze\n                </Button>\n              </form>\n\n              <Button\n                size=\"lg\"\n                onClick={() => handleAnalysis(selectedSymbol)}\n                disabled={isAnalyzing}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3\"\n              >\n                {isAnalyzing ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\n                    Analyzing {selectedSymbol}...\n                  </>\n                ) : (\n                  <>\n                    <Zap className=\"mr-2 h-5 w-5\" />\n                    Get AI Analysis for {selectedSymbol}\n                  </>\n                )}\n              </Button>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* Strategy Scanner Section */}\n      {activeTab === 'strategies' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <StrategyScanner autoScan={true} accountSize={100000} />\n          </div>\n        </section>\n      )}\n\n      {/* Basic Scanner Section */}\n      {activeTab === 'scanner' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <SwingScanner autoScan={false} />\n          </div>\n        </section>\n      )}\n\n      {/* Error Display */}\n      {error && (\n        <section className=\"py-8 px-4\">\n          <div className=\"container mx-auto\">\n            <Card className=\"bg-red-900/20 border-red-500/50\">\n              <CardContent className=\"p-6\">\n                <p className=\"text-red-300 text-center\">{error}</p>\n              </CardContent>\n            </Card>\n          </div>\n        </section>\n      )}\n\n      {/* Analysis Results */}\n      {activeTab === 'individual' && (stockData || analysis) && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n\n              {/* Stock Quote Card */}\n              {stockData && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <BarChart3 className=\"mr-2 h-5 w-5 text-blue-400\" />\n                      {stockData.symbol} Quote\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Price:</span>\n                        <span className=\"text-white font-semibold\">{formatCurrency(stockData.price)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Change:</span>\n                        <span className={stockData.change >= 0 ? \"text-green-400\" : \"text-red-400\"}>\n                          {formatCurrency(stockData.change)} ({formatPercentage(stockData.changePercent)})\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Volume:</span>\n                        <span className=\"text-white\">{stockData.volume.toLocaleString()}</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Trading Levels Card */}\n              {analysis && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <Target className=\"mr-2 h-5 w-5 text-green-400\" />\n                      Trading Levels\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Entry:</span>\n                        <span className=\"text-white font-semibold\">{formatCurrency(analysis.entryPrice)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Stop Loss:</span>\n                        <span className=\"text-red-400\">{formatCurrency(analysis.stopLoss)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Take Profit:</span>\n                        <span className=\"text-green-400\">{formatCurrency(analysis.takeProfit)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Risk/Reward:</span>\n                        <span className=\"text-blue-400 font-semibold\">{analysis.riskRewardRatio.toFixed(2)}:1</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Analysis Summary Card */}\n              {analysis && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <Brain className=\"mr-2 h-5 w-5 text-purple-400\" />\n                      AI Analysis\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Trend:</span>\n                        <span className={`font-semibold ${\n                          analysis.trend === 'BULLISH' ? 'text-green-400' :\n                          analysis.trend === 'BEARISH' ? 'text-red-400' : 'text-yellow-400'\n                        }`}>\n                          {analysis.trend}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Confidence:</span>\n                        <span className=\"text-white font-semibold\">{analysis.confidence.toFixed(1)}%</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Recommendation:</span>\n                        <span className={`font-semibold ${\n                          analysis.recommendation.includes('BUY') ? 'text-green-400' :\n                          analysis.recommendation.includes('SELL') ? 'text-red-400' : 'text-yellow-400'\n                        }`}>\n                          {analysis.recommendation.replace('_', ' ')}\n                        </span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n\n            {/* Technical Indicators */}\n            {analysis && (\n              <Card className=\"mt-6 bg-slate-800/50 border-slate-700\">\n                <CardHeader>\n                  <CardTitle className=\"text-white flex items-center\">\n                    <TrendingUp className=\"mr-2 h-5 w-5 text-orange-400\" />\n                    Technical Indicators\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                    {analysis.indicators.map((indicator, index) => (\n                      <div key={index} className=\"p-4 bg-slate-700/50 rounded-lg\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"text-white font-medium\">{indicator.name}</h4>\n                          <span className={`px-2 py-1 rounded text-xs font-semibold ${\n                            indicator.signal === 'BUY' ? 'bg-green-500/20 text-green-400' :\n                            indicator.signal === 'SELL' ? 'bg-red-500/20 text-red-400' :\n                            'bg-yellow-500/20 text-yellow-400'\n                          }`}>\n                            {indicator.signal}\n                          </span>\n                        </div>\n                        <p className=\"text-slate-300 text-sm\">{indicator.description}</p>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Support and Resistance Levels */}\n            {analysis && (analysis.supportLevels.length > 0 || analysis.resistanceLevels.length > 0) && (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\">\n                {analysis.supportLevels.length > 0 && (\n                  <Card className=\"bg-slate-800/50 border-slate-700\">\n                    <CardHeader>\n                      <CardTitle className=\"text-white flex items-center\">\n                        <Shield className=\"mr-2 h-5 w-5 text-green-400\" />\n                        Support Levels\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-2\">\n                        {analysis.supportLevels.map((level, index) => (\n                          <div key={index} className=\"flex justify-between\">\n                            <span className=\"text-slate-300\">Support {index + 1}:</span>\n                            <span className=\"text-green-400 font-semibold\">{formatCurrency(level)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n\n                {analysis.resistanceLevels.length > 0 && (\n                  <Card className=\"bg-slate-800/50 border-slate-700\">\n                    <CardHeader>\n                      <CardTitle className=\"text-white flex items-center\">\n                        <Shield className=\"mr-2 h-5 w-5 text-red-400\" />\n                        Resistance Levels\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-2\">\n                        {analysis.resistanceLevels.map((level, index) => (\n                          <div key={index} className=\"flex justify-between\">\n                            <span className=\"text-slate-300\">Resistance {index + 1}:</span>\n                            <span className=\"text-red-400 font-semibold\">{formatCurrency(level)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n              </div>\n            )}\n          </div>\n        </section>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iPAAQ,EAAC;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iPAAQ,EAAC;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iPAAQ,EAAC;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iPAAQ,EAA8B;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iPAAQ,EAAmB;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iPAAQ,EAAgB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iPAAQ,EAA0C;IAEpF,MAAM,iBAAiB;QAAC;QAAO;QAAO;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAQ;IAEtF,MAAM,iBAAiB,OAAO;QAC5B,eAAe;QACf,SAAS;QACT,YAAY;QACZ,aAAa;QAEb,IAAI;YACF,6CAA6C;YAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC1D,MAAM,CAAC,kBAAkB,EAAE,QAAQ;gBACnC,MAAM,CAAC,oBAAoB,EAAE,QAAQ;aACtC;YAED,IAAI,CAAC,cAAc,EAAE,EAAE;gBACrB,MAAM,aAAa,MAAM,cAAc,IAAI;gBAC3C,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,cAAc,MAAM,EAAE;YACvE;YAEA,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBACxB,MAAM,gBAAgB,MAAM,iBAAiB,IAAI;gBACjD,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,iBAAiB,MAAM,EAAE;YAC7E;YAEA,MAAM,CAAC,WAAW,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClD,cAAc,IAAI;gBAClB,iBAAiB,IAAI;aACtB;YAED,aAAa;YACb,YAAY;QACd,EAAE,OAAO,KAAU;YACjB,MAAM,eAAe,IAAI,OAAO,EAAE,SAAS,iBAAiB,IAAI,OAAO,EAAE,SAAS,SAC9E,gEACA;YACJ,SAAS;YACT,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,EAAE,cAAc;QAChB,IAAI,aAAa,IAAI,IAAI;YACvB,eAAe,aAAa,WAAW;YACvC,kBAAkB,aAAa,WAAW;QAC5C;IACF;IAEA,qBACE,8QAAC;QAAI,WAAU;;0BAEb,8QAAC;gBAAO,WAAU;0BAChB,cAAA,8QAAC;oBAAI,WAAU;8BACb,cAAA,8QAAC;wBAAI,WAAU;;0CACb,8QAAC;gCAAI,WAAU;;kDACb,8QAAC,6OAAK;wCAAC,WAAU;;;;;;kDACjB,8QAAC;wCAAG,WAAU;kDAAgC;;;;;;;;;;;;0CAEhD,8QAAC;gCAAI,WAAU;;kDACb,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,kBAAkB,EAAE,cAAc,eAAe,eAAe,mCAAmC;kDAChH;;;;;;kDAGD,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,kBAAkB,EAAE,cAAc,YAAY,eAAe,mCAAmC;kDAC7G;;;;;;kDAGD,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,kBAAkB,EAAE,cAAc,eAAe,eAAe,mCAAmC;kDAChH;;;;;;kDAGD,8QAAC,4KAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAAmE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/G,8QAAC;gBAAQ,WAAU;0BACjB,cAAA,8QAAC;oBAAI,WAAU;;sCACb,8QAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8QAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAM7D,8QAAC;4BAAI,WAAU;sCACb,cAAA,8QAAC;gCAAI,WAAU;;kDACb,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,eACV,2BACA,yDACJ;;0DAEF,8QAAC,uOAAG;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGzC,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,YACV,2BACA,yDACJ;;0DAEF,8QAAC,0OAAI;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG1C,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,eACV,2BACA,yDACJ;;0DAEF,8QAAC,gPAAM;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;;;;;;;;;;;;wBAO/C,cAAc,6BACb,8QAAC;4BAAI,WAAU;;8CACb,8QAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8QAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;mCAInC,cAAc,0BAChB,8QAAC;4BAAI,WAAU;;8CACb,8QAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8QAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;iDAKrC,8QAAC;4BAAI,WAAU;;8CACb,8QAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8QAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,8QAAC,4KAAM;4CAEL,SAAS,mBAAmB,SAAS,YAAY;4CACjD,SAAS;gDACP,kBAAkB;gDAClB,eAAe;4CACjB;4CACA,UAAU;4CACV,WAAW,mBAAmB,SAC1B,kCACA;sDAGH;2CAZI;;;;;;;;;;8CAkBX,8QAAC;oCAAK,UAAU;oCAA0B,WAAU;;sDAClD,8QAAC;4CAAI,WAAU;;8DACb,8QAAC,gPAAM;oDAAC,WAAU;;;;;;8DAClB,8QAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,WAAU;oDACV,UAAU;;;;;;;;;;;;sDAGd,8QAAC,4KAAM;4CACL,MAAK;4CACL,UAAU,eAAe,CAAC,aAAa,IAAI;4CAC3C,WAAU;sDACX;;;;;;;;;;;;8CAKH,8QAAC,4KAAM;oCACL,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,UAAU;oCACV,WAAU;8CAET,4BACC;;0DACE,8QAAC,4PAAO;gDAAC,WAAU;;;;;;4CAA8B;4CACtC;4CAAe;;qEAG5B;;0DACE,8QAAC,uOAAG;gDAAC,WAAU;;;;;;4CAAiB;4CACX;;;;;;;;;;;;;;;;;;;;;;;;;YAUlC,cAAc,8BACb,8QAAC;gBAAQ,WAAU;0BACjB,cAAA,8QAAC;oBAAI,WAAU;8BACb,cAAA,8QAAC,wLAAe;wBAAC,UAAU;wBAAM,aAAa;;;;;;;;;;;;;;;;YAMnD,cAAc,2BACb,8QAAC;gBAAQ,WAAU;0BACjB,cAAA,8QAAC;oBAAI,WAAU;8BACb,cAAA,8QAAC,kLAAY;wBAAC,UAAU;;;;;;;;;;;;;;;;YAM7B,uBACC,8QAAC;gBAAQ,WAAU;0BACjB,cAAA,8QAAC;oBAAI,WAAU;8BACb,cAAA,8QAAC,wKAAI;wBAAC,WAAU;kCACd,cAAA,8QAAC,+KAAW;4BAAC,WAAU;sCACrB,cAAA,8QAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;YAQlD,cAAc,gBAAgB,CAAC,aAAa,QAAQ,mBACnD,8QAAC;gBAAQ,WAAU;0BACjB,cAAA,8QAAC;oBAAI,WAAU;;sCACb,8QAAC;4BAAI,WAAU;;gCAGZ,2BACC,8QAAC,wKAAI;oCAAC,WAAU;;sDACd,8QAAC,8KAAU;sDACT,cAAA,8QAAC,6KAAS;gDAAC,WAAU;;kEACnB,8QAAC,+PAAS;wDAAC,WAAU;;;;;;oDACpB,UAAU,MAAM;oDAAC;;;;;;;;;;;;sDAGtB,8QAAC,+KAAW;sDACV,cAAA,8QAAC;gDAAI,WAAU;;kEACb,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;0EAA4B,IAAA,qKAAc,EAAC,UAAU,KAAK;;;;;;;;;;;;kEAE5E,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAW,UAAU,MAAM,IAAI,IAAI,mBAAmB;;oEACzD,IAAA,qKAAc,EAAC,UAAU,MAAM;oEAAE;oEAAG,IAAA,uKAAgB,EAAC,UAAU,aAAa;oEAAE;;;;;;;;;;;;;kEAGnF,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;0EAAc,UAAU,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQtE,0BACC,8QAAC,wKAAI;oCAAC,WAAU;;sDACd,8QAAC,8KAAU;sDACT,cAAA,8QAAC,6KAAS;gDAAC,WAAU;;kEACnB,8QAAC,gPAAM;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;;;;;;sDAItD,8QAAC,+KAAW;sDACV,cAAA,8QAAC;gDAAI,WAAU;;kEACb,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;0EAA4B,IAAA,qKAAc,EAAC,SAAS,UAAU;;;;;;;;;;;;kEAEhF,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;0EAAgB,IAAA,qKAAc,EAAC,SAAS,QAAQ;;;;;;;;;;;;kEAElE,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;0EAAkB,IAAA,qKAAc,EAAC,SAAS,UAAU;;;;;;;;;;;;kEAEtE,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;;oEAA+B,SAAS,eAAe,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQ5F,0BACC,8QAAC,wKAAI;oCAAC,WAAU;;sDACd,8QAAC,8KAAU;sDACT,cAAA,8QAAC,6KAAS;gDAAC,WAAU;;kEACnB,8QAAC,6OAAK;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;;;;;;sDAItD,8QAAC,+KAAW;sDACV,cAAA,8QAAC;gDAAI,WAAU;;kEACb,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAW,CAAC,cAAc,EAC9B,SAAS,KAAK,KAAK,YAAY,mBAC/B,SAAS,KAAK,KAAK,YAAY,iBAAiB,mBAChD;0EACC,SAAS,KAAK;;;;;;;;;;;;kEAGnB,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;;oEAA4B,SAAS,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE7E,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAW,CAAC,cAAc,EAC9B,SAAS,cAAc,CAAC,QAAQ,CAAC,SAAS,mBAC1C,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU,iBAAiB,mBAC5D;0EACC,SAAS,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAUnD,0BACC,8QAAC,wKAAI;4BAAC,WAAU;;8CACd,8QAAC,8KAAU;8CACT,cAAA,8QAAC,6KAAS;wCAAC,WAAU;;0DACnB,8QAAC,gQAAU;gDAAC,WAAU;;;;;;4CAAiC;;;;;;;;;;;;8CAI3D,8QAAC,+KAAW;8CACV,cAAA,8QAAC;wCAAI,WAAU;kDACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACnC,8QAAC;gDAAgB,WAAU;;kEACzB,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAG,WAAU;0EAA0B,UAAU,IAAI;;;;;;0EACtD,8QAAC;gEAAK,WAAW,CAAC,wCAAwC,EACxD,UAAU,MAAM,KAAK,QAAQ,mCAC7B,UAAU,MAAM,KAAK,SAAS,+BAC9B,oCACA;0EACC,UAAU,MAAM;;;;;;;;;;;;kEAGrB,8QAAC;wDAAE,WAAU;kEAA0B,UAAU,WAAW;;;;;;;+CAXpD;;;;;;;;;;;;;;;;;;;;;wBAoBnB,YAAY,CAAC,SAAS,aAAa,CAAC,MAAM,GAAG,KAAK,SAAS,gBAAgB,CAAC,MAAM,GAAG,CAAC,mBACrF,8QAAC;4BAAI,WAAU;;gCACZ,SAAS,aAAa,CAAC,MAAM,GAAG,mBAC/B,8QAAC,wKAAI;oCAAC,WAAU;;sDACd,8QAAC,8KAAU;sDACT,cAAA,8QAAC,6KAAS;gDAAC,WAAU;;kEACnB,8QAAC,gPAAM;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;;;;;;sDAItD,8QAAC,+KAAW;sDACV,cAAA,8QAAC;gDAAI,WAAU;0DACZ,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,sBAClC,8QAAC;wDAAgB,WAAU;;0EACzB,8QAAC;gEAAK,WAAU;;oEAAiB;oEAAS,QAAQ;oEAAE;;;;;;;0EACpD,8QAAC;gEAAK,WAAU;0EAAgC,IAAA,qKAAc,EAAC;;;;;;;uDAFvD;;;;;;;;;;;;;;;;;;;;;gCAUnB,SAAS,gBAAgB,CAAC,MAAM,GAAG,mBAClC,8QAAC,wKAAI;oCAAC,WAAU;;sDACd,8QAAC,8KAAU;sDACT,cAAA,8QAAC,6KAAS;gDAAC,WAAU;;kEACnB,8QAAC,gPAAM;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;;;;;;sDAIpD,8QAAC,+KAAW;sDACV,cAAA,8QAAC;gDAAI,WAAU;0DACZ,SAAS,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACrC,8QAAC;wDAAgB,WAAU;;0EACzB,8QAAC;gEAAK,WAAU;;oEAAiB;oEAAY,QAAQ;oEAAE;;;;;;;0EACvD,8QAAC;gEAAK,WAAU;0EAA8B,IAAA,qKAAc,EAAC;;;;;;;uDAFrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBpC", "debugId": null}}]}
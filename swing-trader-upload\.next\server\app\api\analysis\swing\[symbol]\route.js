var R=require("../../../../../chunks/[turbopack]_runtime.js")("server/app/api/analysis/swing/[symbol]/route.js")
R.c("server/chunks/7b731_next_16128c68._.js")
R.c("server/chunks/7b731_axios_lib_7462c230._.js")
R.c("server/chunks/7b731_mime-db_cef17743._.js")
R.c("server/chunks/7b731_tailwind-merge_dist_bundle-mjs_mjs_d9fa9fcc._.js")
R.c("server/chunks/7b731_e4728619._.js")
R.c("server/chunks/[root-of-the-server]__82389699._.js")
R.m("[project]/SwingTrader-AI-Package/.next-internal/server/app/api/analysis/swing/[symbol]/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/SwingTrader-AI-Package/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/SwingTrader-AI-Package/src/app/api/analysis/swing/[symbol]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/SwingTrader-AI-Package/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/SwingTrader-AI-Package/src/app/api/analysis/swing/[symbol]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports

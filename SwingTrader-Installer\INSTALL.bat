
@echo off
title Swing Trading System Installer
color 0A

echo.
echo ========================================
echo    SWING TRADING SYSTEM INSTALLER
echo ========================================
echo.
echo This will install your AI-powered swing trading system
echo on this computer automatically.
echo.
pause

echo.
echo [1/5] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found! 
    echo.
    echo Please install Node.js first:
    echo 1. Go to https://nodejs.org
    echo 2. Download and install the LTS version
    echo 3. Restart this installer
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js found!
)

echo.
echo [2/5] Creating application directory...
if not exist "SwingTrader-AI" mkdir SwingTrader-AI
cd SwingTrader-AI

echo.
echo [3/5] Extracting application files...
powershell -command "Expand-Archive -Path '../swing-trader-app.zip' -DestinationPath '.' -Force"

echo.
echo [4/5] Installing dependencies (this may take a few minutes)...
call npm install

echo.
echo [5/5] Creating desktop shortcut...
echo @echo off > "Start-SwingTrader.bat"
echo title Swing Trading System >> "Start-SwingTrader.bat"
echo color 0A >> "Start-SwingTrader.bat"
echo echo. >> "Start-SwingTrader.bat"
echo echo ========================================= >> "Start-SwingTrader.bat"
echo echo    SWING TRADING SYSTEM STARTING... >> "Start-SwingTrader.bat"
echo echo ========================================= >> "Start-SwingTrader.bat"
echo echo. >> "Start-SwingTrader.bat"
echo echo Your trading system is starting up... >> "Start-SwingTrader.bat"
echo echo Once ready, it will open in your browser automatically. >> "Start-SwingTrader.bat"
echo echo. >> "Start-SwingTrader.bat"
echo echo Local URL: http://localhost:3000 >> "Start-SwingTrader.bat"
echo echo Network URL: Will be shown below >> "Start-SwingTrader.bat"
echo echo. >> "Start-SwingTrader.bat"
echo call npm run dev >> "Start-SwingTrader.bat"

copy "Start-SwingTrader.bat" "%USERPROFILE%\Desktop\Start-SwingTrader.bat"

echo.
echo ========================================
echo    INSTALLATION COMPLETE! ✅
echo ========================================
echo.
echo Your Swing Trading System has been installed successfully!
echo.
echo To start the system:
echo 1. Double-click "Start-SwingTrader.bat" on your desktop
echo 2. Wait for it to load (about 30 seconds)
echo 3. Your browser will open automatically
echo.
echo The system includes:
echo ✅ Real-time stock scanning
echo ✅ AI-powered trade analysis  
echo ✅ Paper trading functionality
echo ✅ Professional trading cards
echo.
echo Press any key to finish...
pause >nul

echo.
echo Would you like to start the system now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo.
    echo Starting Swing Trading System...
    start "" "%USERPROFILE%\Desktop\Start-SwingTrader.bat"
)

echo.
echo Installation complete! You can close this window.
pause

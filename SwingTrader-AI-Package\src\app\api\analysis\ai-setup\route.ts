import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { EnhancedScanResult } from '@/lib/enhancedSwingScanner'
import { AIAnalysis } from '@/types/paperTrading'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function POST(request: NextRequest) {
  try {
    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {
      return NextResponse.json({
        error: 'OpenAI API key not configured. Please add your OpenAI API key to .env.local'
      }, { status: 400 })
    }

    // Debug: Check API key
    console.log('OpenAI API Key exists:', !!process.env.OPENAI_API_KEY)
    console.log('OpenAI API Key prefix:', process.env.OPENAI_API_KEY?.substring(0, 15))

    const { scanResult }: { scanResult: EnhancedScanResult } = await request.json()

    if (!scanResult) {
      return NextResponse.json({ error: 'Scan result is required' }, { status: 400 })
    }

    // Prepare context for AI analysis
    const setup = scanResult.overnightSetup || scanResult.breakoutSetup
    if (!setup) {
      return NextResponse.json({ error: 'No trading setup found' }, { status: 400 })
    }

    const strategyType = scanResult.overnightSetup ? 'overnight momentum' : 'technical breakout'
    const currentPrice = scanResult.quote.price
    const changePercent = scanResult.quote.changePercent || 0

    const prompt = `As a professional swing trading analyst, provide a comprehensive analysis for ${scanResult.symbol} (${scanResult.name}) in the ${scanResult.sector} sector.

CURRENT SETUP:
- Strategy: ${strategyType}
- Current Price: $${currentPrice}
- Daily Change: ${changePercent.toFixed(2)}%
- Entry Price: $${setup.entryPrice}
- Stop Loss: $${setup.stopLoss}
- Target: $${setup.targets[0]}
- Confidence: ${setup.confidence}%
- Overall Score: ${scanResult.overallScore}/100

TRADING ALERTS: ${scanResult.alerts.join(', ') || 'None'}
RISK WARNINGS: ${scanResult.riskWarnings.join(', ') || 'None'}

Please provide a JSON response with the following structure:
{
  "setupExplanation": "Clear explanation of why this setup meets swing trading criteria",
  "catalysts": ["List of potential market catalysts supporting this trade"],
  "riskAssessment": "Detailed risk analysis and what could go wrong",
  "keyLevels": ["Important price levels to watch"],
  "timeframe": "Expected timeframe for trade to play out",
  "confidence": number between 1-100
}

Focus on:
1. Technical analysis backing the setup
2. Market conditions and sector trends
3. Risk/reward analysis
4. Specific entry/exit strategy
5. Potential catalysts or headwinds`

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a professional swing trading analyst with expertise in technical analysis, market dynamics, and risk management. Provide actionable, data-driven insights."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 1000,
    })

    const aiResponse = completion.choices[0]?.message?.content
    if (!aiResponse) {
      throw new Error('No response from AI')
    }

    // Parse the JSON response
    let analysis: Partial<AIAnalysis>
    try {
      analysis = JSON.parse(aiResponse)
    } catch (parseError) {
      // Fallback if JSON parsing fails
      analysis = {
        setupExplanation: aiResponse.substring(0, 300) + '...',
        catalysts: ['AI analysis available - see full response'],
        riskAssessment: 'Standard swing trading risks apply',
        keyLevels: [`Entry: $${setup.entryPrice}`, `Stop: $${setup.stopLoss}`, `Target: $${setup.targets[0]}`],
        timeframe: '3-10 trading days',
        confidence: Math.round(setup.confidence)
      }
    }

    const fullAnalysis: AIAnalysis = {
      symbol: scanResult.symbol,
      setupExplanation: analysis.setupExplanation || 'Setup analysis not available',
      catalysts: analysis.catalysts || [],
      riskAssessment: analysis.riskAssessment || 'Standard risks apply',
      keyLevels: analysis.keyLevels || [],
      timeframe: analysis.timeframe || '3-10 days',
      confidence: analysis.confidence || Math.round(setup.confidence),
      lastUpdated: new Date().toISOString()
    }

    return NextResponse.json(fullAnalysis)

  } catch (error) {
    console.error('AI Analysis error:', error)
    return NextResponse.json(
      { error: 'Failed to generate AI analysis' },
      { status: 500 }
    )
  }
}

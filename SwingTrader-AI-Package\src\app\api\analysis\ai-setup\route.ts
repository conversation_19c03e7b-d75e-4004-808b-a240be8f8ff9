import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { EnhancedScanResult } from '@/lib/enhancedSwingScanner'
import { AIAnalysis } from '@/types/paperTrading'
import { marketDataService } from '@/lib/marketDataEnrichment'

// REAL OpenAI API key - hardcoded for seamless transfer between computers
const apiKey = '********************************************************************************************************************************************************************'

console.log('🔑 Using hardcoded API Key prefix:', apiKey.substring(0, 15))
console.log('🔑 Source: Hardcoded for seamless computer transfer')

const openai = new OpenAI({
  apiKey: api<PERSON><PERSON>,
})



export async function POST(request: NextRequest) {
  try {
    console.log('🚀 AI Analysis endpoint called')
    console.log('🔑 Using hardcoded API key:', apiKey.substring(0, 15))
    console.log('🔑 API key length:', apiKey.length)

    const { scanResult }: { scanResult: EnhancedScanResult } = await request.json()

    if (!scanResult) {
      return NextResponse.json({ error: 'Scan result is required' }, { status: 400 })
    }

    // Prepare context for AI analysis
    const setup = scanResult.overnightSetup || scanResult.breakoutSetup
    if (!setup) {
      return NextResponse.json({ error: 'No trading setup found' }, { status: 400 })
    }

    const strategyType = scanResult.overnightSetup ? 'overnight momentum' : 'technical breakout'
    const currentPrice = scanResult.quote?.price || setup?.currentPrice || 0
    const changePercent = scanResult.quote?.changePercent || setup?.momentum || 0

    // Get enriched market data for more specific analysis
    const enrichedData = await marketDataService.getEnrichedData(scanResult.symbol, scanResult.sector)

    const prompt = `You are a senior equity research analyst providing institutional-grade swing trading analysis for ${scanResult.symbol}.

MANDATORY REQUIREMENTS - Every statement must include:
1. EXACT DATES, NUMBERS, and PERCENTAGES
2. SPECIFIC SOURCE ATTRIBUTION
3. VERIFIABLE DATA POINTS
4. PROBABILITY-BASED ASSESSMENTS

CURRENT SETUP DATA:
- Symbol: ${scanResult.symbol} (${scanResult.name || 'N/A'})
- Sector: ${scanResult.sector || 'Technology'}
- Strategy: ${strategyType}
- Current Price: $${currentPrice}
- Daily Change: ${changePercent.toFixed(2)}%
- Entry: $${setup.entryPrice}
- Stop Loss: $${setup.stopLoss} (Risk: ${(((setup.entryPrice - setup.stopLoss) / setup.entryPrice) * 100).toFixed(1)}%)
- Target: $${setup.targets[0]} (Reward: ${(((setup.targets[0] - setup.entryPrice) / setup.entryPrice) * 100).toFixed(1)}%)
- Risk/Reward Ratio: ${((setup.targets[0] - setup.entryPrice) / (setup.entryPrice - setup.stopLoss)).toFixed(2)}:1
- Setup Confidence: ${setup.confidence}%
- Overall Score: ${scanResult.overallScore}/100

ENRICHED MARKET DATA:
${enrichedData.recentEarnings ? `
RECENT EARNINGS:
- Report Date: ${enrichedData.recentEarnings.reportDate}
- Actual EPS: $${enrichedData.recentEarnings.actualEPS} vs Est: $${enrichedData.recentEarnings.estimatedEPS}
- Beat by: $${enrichedData.recentEarnings.beatAmount} (${enrichedData.recentEarnings.beat ? 'BEAT' : 'MISS'})
- Revenue: $${(enrichedData.recentEarnings.revenue / 1e9).toFixed(1)}B vs Est: $${(enrichedData.recentEarnings.revenueEstimate / 1e9).toFixed(1)}B
- Next Earnings: ${enrichedData.recentEarnings.nextEarningsDate}
` : ''}
${enrichedData.analystActions && enrichedData.analystActions.length > 0 ? `
RECENT ANALYST ACTIONS:
${enrichedData.analystActions.map(action =>
  `- ${action.firm}: ${action.action.toUpperCase()} to ${action.rating}, $${action.priceTarget} target (${action.date})`
).join('\n')}
` : ''}
SECTOR PERFORMANCE:
- Sector ETF (${enrichedData.sectorPerformance?.sectorETF}): +${enrichedData.sectorPerformance?.sectorReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.sectorReturn1M.toFixed(1)}% (1M)
- S&P 500: +${enrichedData.sectorPerformance?.spyReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.spyReturn1M.toFixed(1)}% (1M)
- Relative Strength: ${enrichedData.sectorPerformance?.relativeStrength.toFixed(2)}x
- Sector Ranking: #${enrichedData.sectorPerformance?.ranking} of ${enrichedData.sectorPerformance?.totalSectors}

VOLATILITY METRICS:
- 30-day IV: ${enrichedData.volatilityMetrics?.impliedVolatility30d}% vs 6M avg: ${enrichedData.volatilityMetrics?.impliedVolatility6m}%
- Gap Frequency: ${enrichedData.volatilityMetrics?.gapFrequency}% of days have >2% gaps
- Average Gap Size: ${enrichedData.volatilityMetrics?.averageGapSize}%

TECHNICAL LEVELS:
- Support: ${enrichedData.technicalLevels?.support.map(s => `$${s.price} (strength: ${(s.strength * 100).toFixed(0)}%, last test: ${s.lastTest})`).join(', ')}
- Resistance: ${enrichedData.technicalLevels?.resistance.map(r => `$${r.price} (strength: ${(r.strength * 100).toFixed(0)}%, last test: ${r.lastTest})`).join(', ')}
- VWAP: $${enrichedData.technicalLevels?.vwap}
- SMA20: $${enrichedData.technicalLevels?.sma20}, SMA50: $${enrichedData.technicalLevels?.sma50}

INSTITUTIONAL RESEARCH STANDARDS - MANDATORY SPECIFICITY:

**EARNINGS & FUNDAMENTALS** (Must include exact data):
- Latest quarterly results: "Q3 2024 EPS $X.XX vs $X.XX est (+/-X.X% beat/miss), reported [DATE]"
- Revenue figures: "$XX.XB vs $XX.XB est (+/-X.X%)"
- Forward guidance: "Management raised/lowered FY2024 EPS to $X.XX-$X.XX from $X.XX-$X.XX"
- Source attribution: "Source: Company 10-Q filing, earnings call transcript"

**ANALYST COVERAGE** (Must include firm names, dates, targets):
- Recent upgrades/downgrades: "[FIRM] upgraded to [RATING] from [PRIOR], $XXX target on [DATE]"
- Price target changes: "[FIRM] raised PT to $XXX from $XXX (+/-X.X%) on [DATE]"
- Consensus data: "Street consensus: XX Buy, XX Hold, XX Sell ratings, $XXX avg PT"
- Source: "Source: Bloomberg, FactSet, company filings"

**SECTOR & MARKET DYNAMICS** (Must include exact percentages):
- Sector performance: "[SECTOR ETF] +/-X.X% vs S&P 500 +/-X.X% over [TIMEFRAME]"
- Relative strength: "Stock outperformed sector by +/-X.X% over past [X] days"
- Economic catalysts: "[ECONOMIC EVENT] on [DATE]: [SPECIFIC IMPACT] affects [STOCK] because [REASON]"
- Source: "Source: Federal Reserve, Bureau of Labor Statistics, sector ETF data"

**TECHNICAL ANALYSIS** (Must include exact levels with historical context):
- Support levels: "$XXX support tested [X] times since [DATE], held with avg volume of [X]M shares"
- Resistance levels: "$XXX resistance from [DATE] high, [X]% above current price"
- Volume analysis: "Average daily volume [X]M vs [X]M 30-day avg (+/-X.X%)"
- Volatility: "30-day IV at X.X% vs 6-month avg X.X% (+/-X.X% vs historical)"

**RISK QUANTIFICATION** (Must include probabilities and historical data):
- Gap risk: "Stock gaps >2% on X% of trading days over past 90 days"
- Event risk: "[UPCOMING EVENT] on [DATE] has X% probability of [OUTCOME] based on [SOURCE]"
- Market correlation: "Beta of X.X vs S&P 500, correlation coefficient X.X over past year"
- Downside scenarios: "If [SPECIFIC CATALYST] occurs, target downside to $XXX (-X.X%)"

EXAMPLES OF REQUIRED SPECIFICITY:
✅ REQUIRED: "MSFT reported Q1 2024 EPS $3.30 vs $3.10 est (+6.5% beat) on Oct 24, 2024. Azure revenue +29% YoY to $25.7B. Morgan Stanley raised PT to $550 from $520 on Oct 25, citing cloud acceleration. Source: Microsoft 10-Q, MS Research."

❌ FORBIDDEN: "Recent earnings were strong" or "analysts are bullish" or "sector performing well"

Provide institutional-grade research analysis in markdown format. Every statement must include specific data, dates, sources, and quantified metrics:

## 📊 TECHNICAL SETUP ANALYSIS
- Risk/Reward calculation with exact percentages
- Historical price level significance with test dates and volumes
- Technical indicator readings with specific values and timeframes
- Volume analysis vs historical averages with exact numbers

## 📈 FUNDAMENTAL CATALYSTS
- Latest earnings results with exact EPS, revenue, beat/miss percentages, and report date
- Recent analyst actions with firm names, rating changes, price targets, and dates
- Upcoming events with specific dates and probability-based impact assessments
- Sector performance vs benchmarks with exact percentages and timeframes

## ⚠️ QUANTIFIED RISK ASSESSMENT
- Volatility metrics with specific IV percentages vs historical averages
- Gap risk statistics with historical frequency data
- Event risk probabilities with specific dates and potential outcomes
- Market correlation data with beta and correlation coefficients

## 🎯 PRECISE PRICE LEVELS
- Support levels with exact prices, test dates, and volume confirmation
- Resistance levels with historical significance and percentage distances
- Moving averages with current values and trend analysis
- Volume profile levels with specific price points and significance

## ⏰ TRADE EXECUTION FRAMEWORK
- Specific entry/exit timing based on upcoming catalysts
- Position sizing recommendations based on volatility and risk metrics
- Stop-loss and profit-taking levels with mathematical justification
- Expected timeframe with market-based reasoning

## 📊 PROBABILITY-BASED OUTLOOK
- Success probability with historical precedent analysis
- Scenario analysis with specific price targets and probabilities
- Risk-adjusted return expectations with quantified metrics
- Source attribution for all data points and analysis

CRITICAL: Replace ALL generic statements with specific, verifiable data points and source citations.`

    console.log('🤖 Making REAL OpenAI API call...')
    console.log('🔑 API Key being used:', apiKey.substring(0, 20) + '...')
    console.log('📝 Prompt length:', prompt.length)

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini", // Using reliable model
      messages: [
        {
          role: "system",
          content: "You are a senior equity research analyst at a top-tier investment bank. Provide institutional-grade analysis with MANDATORY specificity: exact dates, precise numbers, verifiable sources, probability assessments, and quantified risk metrics. Every statement must be backed by concrete data. NO generic language allowed - treat this as a research report for institutional clients who demand verifiable intelligence."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.1, // Very low temperature for maximum precision and factual accuracy
      max_tokens: 2500, // Increased for comprehensive institutional-grade analysis
    })
    console.log('✅ REAL OpenAI API call successful')
    console.log('📊 Response length:', completion.choices[0]?.message?.content?.length || 0)

    const aiResponse = completion.choices[0]?.message?.content
    console.log('🎯 AI Response received:', aiResponse ? 'YES' : 'NO')
    console.log('📄 AI Response preview:', aiResponse?.substring(0, 200) + '...')

    if (!aiResponse) {
      throw new Error('No response from AI')
    }

    // Use the AI response directly as professional analysis
    const analysis: Partial<AIAnalysis> = {
      setupExplanation: aiResponse,
      catalysts: [
        `Risk/Reward: ${((setup.targets[0] - setup.entryPrice) / (setup.entryPrice - setup.stopLoss)).toFixed(2)}:1 ratio`,
        `Entry: $${setup.entryPrice} | Stop: $${setup.stopLoss} | Target: $${setup.targets[0]}`,
        `Setup Score: ${scanResult.overallScore}/100 | Confidence: ${setup.confidence}%`
      ],
      riskAssessment: `Downside risk: ${(((setup.entryPrice - setup.stopLoss) / setup.entryPrice) * 100).toFixed(1)}% | Upside potential: ${(((setup.targets[0] - setup.entryPrice) / setup.entryPrice) * 100).toFixed(1)}%`,
      keyLevels: [
        `Entry Zone: $${setup.entryPrice}`,
        `Stop Loss: $${setup.stopLoss} (${(((setup.entryPrice - setup.stopLoss) / setup.entryPrice) * 100).toFixed(1)}% risk)`,
        `Target: $${setup.targets[0]} (${(((setup.targets[0] - setup.entryPrice) / setup.entryPrice) * 100).toFixed(1)}% reward)`
      ],
      timeframe: 'See detailed analysis for specific timeframe and catalysts',
      confidence: scanResult.overallScore
    }

    console.log('✅ Analysis object created successfully')

    const fullAnalysis: AIAnalysis = {
      symbol: scanResult.symbol,
      setupExplanation: analysis.setupExplanation || 'Setup analysis not available',
      catalysts: analysis.catalysts || [],
      riskAssessment: analysis.riskAssessment || 'Standard risks apply',
      keyLevels: analysis.keyLevels || [],
      timeframe: analysis.timeframe || '3-10 days',
      confidence: analysis.confidence || Math.round(setup.confidence),
      lastUpdated: new Date().toISOString()
    }

    return NextResponse.json(fullAnalysis)

  } catch (error) {
    console.error('❌ AI Analysis error:', error)
    console.error('❌ Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      apiKeyPrefix: apiKey.substring(0, 15)
    })

    return NextResponse.json(
      {
        error: 'Failed to generate AI analysis',
        details: error instanceof Error ? error.message : 'Unknown error',
        apiKeyStatus: apiKey ? 'Present' : 'Missing'
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { EnhancedScanResult } from '@/lib/enhancedSwingScanner'
import { AIAnalysis } from '@/types/paperTrading'
import { marketDataService } from '@/lib/marketDataEnrichment'

// Use environment variable for security (fallback to hardcoded for local development only)
const apiKey = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************'

console.log('🔑 Using API Key prefix:', apiKey.substring(0, 15))
console.log('🔑 Source:', process.env.OPENAI_API_KEY ? 'Environment Variable' : 'Hardcoded Fallback')

const openai = new OpenAI({
  apiKey: apiKey,
})

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 AI Analysis endpoint called')
    console.log('🔑 Using API key:', apiKey.substring(0, 15))

    const { scanResult }: { scanResult: EnhancedScanResult } = await request.json()

    if (!scanResult) {
      return NextResponse.json({ error: 'Scan result is required' }, { status: 400 })
    }

    // Prepare context for AI analysis
    const setup = scanResult.overnightSetup || scanResult.breakoutSetup
    if (!setup) {
      return NextResponse.json({ error: 'No trading setup found' }, { status: 400 })
    }

    const strategyType = scanResult.overnightSetup ? 'overnight momentum' : 'technical breakout'
    const currentPrice = scanResult.quote.price
    const changePercent = scanResult.quote.changePercent || 0

    // Get enriched market data for more specific analysis
    const enrichedData = await marketDataService.getEnrichedData(scanResult.symbol, scanResult.sector)

    const prompt = `You are a professional swing trader providing DATA-DRIVEN analysis for ${scanResult.symbol} (${scanResult.name}).

CRITICAL REQUIREMENT: Provide SPECIFIC, QUANTIFIABLE data points. NO generic statements allowed.

CURRENT SETUP DATA:
- Symbol: ${scanResult.symbol} (${scanResult.name})
- Sector: ${scanResult.sector}
- Strategy: ${strategyType}
- Current Price: $${currentPrice}
- Daily Change: ${changePercent.toFixed(2)}%
- Entry: $${setup.entryPrice}
- Stop Loss: $${setup.stopLoss}
- Target: $${setup.targets[0]}
- Setup Confidence: ${setup.confidence}%
- Overall Score: ${scanResult.overallScore}/100
- Alerts: ${scanResult.alerts.join(', ') || 'None'}
- Risk Warnings: ${scanResult.riskWarnings.join(', ') || 'None'}

ENRICHED MARKET DATA:
${enrichedData.recentEarnings ? `
RECENT EARNINGS:
- Report Date: ${enrichedData.recentEarnings.reportDate}
- Actual EPS: $${enrichedData.recentEarnings.actualEPS} vs Est: $${enrichedData.recentEarnings.estimatedEPS}
- Beat by: $${enrichedData.recentEarnings.beatAmount} (${enrichedData.recentEarnings.beat ? 'BEAT' : 'MISS'})
- Revenue: $${(enrichedData.recentEarnings.revenue / 1e9).toFixed(1)}B vs Est: $${(enrichedData.recentEarnings.revenueEstimate / 1e9).toFixed(1)}B
- Next Earnings: ${enrichedData.recentEarnings.nextEarningsDate}
` : ''}
${enrichedData.analystActions && enrichedData.analystActions.length > 0 ? `
RECENT ANALYST ACTIONS:
${enrichedData.analystActions.map(action =>
  `- ${action.firm}: ${action.action.toUpperCase()} to ${action.rating}, $${action.priceTarget} target (${action.date})`
).join('\n')}
` : ''}
SECTOR PERFORMANCE:
- Sector ETF (${enrichedData.sectorPerformance?.sectorETF}): +${enrichedData.sectorPerformance?.sectorReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.sectorReturn1M.toFixed(1)}% (1M)
- S&P 500: +${enrichedData.sectorPerformance?.spyReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.spyReturn1M.toFixed(1)}% (1M)
- Relative Strength: ${enrichedData.sectorPerformance?.relativeStrength.toFixed(2)}x
- Sector Ranking: #${enrichedData.sectorPerformance?.ranking} of ${enrichedData.sectorPerformance?.totalSectors}

VOLATILITY METRICS:
- 30-day IV: ${enrichedData.volatilityMetrics?.impliedVolatility30d}% vs 6M avg: ${enrichedData.volatilityMetrics?.impliedVolatility6m}%
- Gap Frequency: ${enrichedData.volatilityMetrics?.gapFrequency}% of days have >2% gaps
- Average Gap Size: ${enrichedData.volatilityMetrics?.averageGapSize}%

TECHNICAL LEVELS:
- Support: ${enrichedData.technicalLevels?.support.map(s => `$${s.price} (strength: ${(s.strength * 100).toFixed(0)}%, last test: ${s.lastTest})`).join(', ')}
- Resistance: ${enrichedData.technicalLevels?.resistance.map(r => `$${r.price} (strength: ${(r.strength * 100).toFixed(0)}%, last test: ${r.lastTest})`).join(', ')}
- VWAP: $${enrichedData.technicalLevels?.vwap}
- SMA20: $${enrichedData.technicalLevels?.sma20}, SMA50: $${enrichedData.technicalLevels?.sma50}

MANDATORY ANALYSIS REQUIREMENTS:

**MARKET CATALYSTS** - Must include SPECIFIC data:
- Recent earnings with actual numbers and dates (e.g., "Q3 EPS $2.45 vs $2.30 est, reported Nov 21")
- Analyst actions with firm names and targets (e.g., "Goldman upgraded to Buy, $250 target on Nov 22")
- Sector performance vs benchmarks with exact % (e.g., "Materials (XLB) +3.2% vs S&P +1.1% this week")
- Recent news with dates and sources (e.g., "Reuters Nov 20: Company announced $2B contract")
- Economic data affecting stock (e.g., "Fed cut rates 0.25% yesterday, benefits growth stocks")

**RISK ASSESSMENT** - Must include QUANTIFIABLE risks:
- Volatility metrics (e.g., "30-day IV at 45% vs 6-month avg 32%")
- Gap risk stats (e.g., "Stock gaps >2% on 15% of trading days past 3 months")
- Support/resistance with context (e.g., "$180 support held 3x in Oct, volume spike at $185")
- Market conditions with probabilities (e.g., "Fed meeting Dec 18 has 70% chance of rate cut")

**KEY LEVELS** - Must be EXACT price points:
- Support levels with historical significance
- Resistance that matters for this trade
- VWAP and moving averages currently relevant
- Volume profile levels

EXAMPLES:
✅ GOOD: "NVDA beat Q3 EPS by $0.15 ($2.45 vs $2.30), reported Nov 21. Goldman upgraded to $250 target Nov 22."
❌ BAD: "Market sentiment is bullish" or "sector performing well"

Return JSON format:
{
  "setupExplanation": "Technical setup explanation with specific price levels and indicators",
  "catalysts": ["Specific catalysts with dates, numbers, sources"],
  "riskAssessment": "Quantified risks with historical data and probabilities",
  "keyLevels": ["Exact price levels: $XXX support (held 3x), $XXX resistance"],
  "timeframe": "Specific timeframe with reasoning",
  "confidence": number 1-100
}

Provide professional-grade analysis a trader can act on immediately with REAL DATA POINTS.`

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a professional swing trading analyst providing DATA-DRIVEN analysis. You must include specific numbers, dates, sources, and quantifiable metrics. Never use generic statements. Focus on actionable intelligence with real market data."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.2, // Lower temperature for more focused, factual responses
      max_tokens: 1500, // Increased for more detailed analysis
    })

    const aiResponse = completion.choices[0]?.message?.content
    if (!aiResponse) {
      throw new Error('No response from AI')
    }

    // Parse the JSON response
    let analysis: Partial<AIAnalysis>
    try {
      analysis = JSON.parse(aiResponse)
    } catch (parseError) {
      // Fallback if JSON parsing fails
      analysis = {
        setupExplanation: aiResponse.substring(0, 300) + '...',
        catalysts: ['AI analysis available - see full response'],
        riskAssessment: 'Standard swing trading risks apply',
        keyLevels: [`Entry: $${setup.entryPrice}`, `Stop: $${setup.stopLoss}`, `Target: $${setup.targets[0]}`],
        timeframe: '3-10 trading days',
        confidence: Math.round(setup.confidence)
      }
    }

    const fullAnalysis: AIAnalysis = {
      symbol: scanResult.symbol,
      setupExplanation: analysis.setupExplanation || 'Setup analysis not available',
      catalysts: analysis.catalysts || [],
      riskAssessment: analysis.riskAssessment || 'Standard risks apply',
      keyLevels: analysis.keyLevels || [],
      timeframe: analysis.timeframe || '3-10 days',
      confidence: analysis.confidence || Math.round(setup.confidence),
      lastUpdated: new Date().toISOString()
    }

    return NextResponse.json(fullAnalysis)

  } catch (error) {
    console.error('AI Analysis error:', error)
    return NextResponse.json(
      { error: 'Failed to generate AI analysis' },
      { status: 500 }
    )
  }
}

var He=(l=>(l.uninitialized="uninitialized",l.pending="pending",l.fulfilled="fulfilled",l.rejected="rejected",l))(He||{});function Ve(e){return{status:e,isUninitialized:e==="uninitialized",isLoading:e==="pending",isSuccess:e==="fulfilled",isError:e==="rejected"}}import{createAction as Y,createSlice as ae,createSelector as et,createAsyncThunk as _e,combineReducers as tt,createNextState as Se,isAnyOf as oe,isAllOf as ke,isAction as nt,isPending as Be,isRejected as fe,isFulfilled as W,isRejectedWithValue as me,isAsyncThunkAction as ze,prepareAutoBatched as ge,SHOULD_AUTOBATCH as we,isPlainObject as ee,nanoid as Me}from"@reduxjs/toolkit";var rt=ee;function Ce(e,n){if(e===n||!(rt(e)&&rt(n)||Array.isArray(e)&&Array.isArray(n)))return n;let p=Object.keys(n),g=Object.keys(e),l=p.length===g.length,R=Array.isArray(n)?[]:{};for(let T of p)R[T]=Ce(e[T],n[T]),l&&(l=e[T]===R[T]);return l?e:R}function Fe(e){let n=0;for(let p in e)n++;return n}var We=e=>[].concat(...e);function it(e){return new RegExp("(^|:)//").test(e)}function at(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}function se(e){return e!=null}function ot(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}var Nt=e=>e.replace(/\/$/,""),qt=e=>e.replace(/^\//,"");function st(e,n){if(!e)return n;if(!n)return e;if(it(n))return n;let p=e.endsWith("/")||!n.startsWith("?")?"/":"";return e=Nt(e),n=qt(n),`${e}${p}${n}`}function ut(e,n,p){return e.has(n)?e.get(n):e.set(n,p).get(n)}function xe(e,n,p){return e.has(n)?e.get(n):e.set(n,p(n)).get(n)}var De=()=>new Map;var yt=(...e)=>fetch(...e),Kt=e=>e.status>=200&&e.status<=299,Ut=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function pt(e){if(!ee(e))return e;let n={...e};for(let[p,g]of Object.entries(n))g===void 0&&delete n[p];return n}function Lt({baseUrl:e,prepareHeaders:n=x=>x,fetchFn:p=yt,paramsSerializer:g,isJsonContentType:l=Ut,jsonContentType:R="application/json",jsonReplacer:T,timeout:B,responseHandler:P,validateStatus:S,...b}={}){return typeof fetch>"u"&&p===yt&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(h,I,M)=>{let{getState:E,extra:D,endpoint:r,forced:f,type:i}=I,s,{url:d,headers:Q=new Headers(b.headers),params:A=void 0,responseHandler:m=P??"json",validateStatus:y=S??Kt,timeout:u=B,...t}=typeof h=="string"?{url:h}:h,a,o=I.signal;u&&(a=new AbortController,I.signal.addEventListener("abort",a.abort),o=a.signal);let c={...b,signal:o,...t};Q=new Headers(pt(Q)),c.headers=await n(Q,{getState:E,arg:h,extra:D,endpoint:r,forced:f,type:i,extraOptions:M})||Q;let k=q=>typeof q=="object"&&(ee(q)||Array.isArray(q)||typeof q.toJSON=="function");if(!c.headers.has("content-type")&&k(c.body)&&c.headers.set("content-type",R),k(c.body)&&l(c.headers)&&(c.body=JSON.stringify(c.body,T)),A){let q=~d.indexOf("?")?"&":"?",U=g?g(A):new URLSearchParams(pt(A));d+=q+U}d=st(e,d);let F=new Request(d,c);s={request:new Request(d,c)};let C,w=!1,O=a&&setTimeout(()=>{w=!0,a.abort()},u);try{C=await p(F)}catch(q){return{error:{status:w?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(q)},meta:s}}finally{O&&clearTimeout(O),a?.signal.removeEventListener("abort",a.abort)}let H=C.clone();s.response=H;let _,L="";try{let q;if(await Promise.all([x(C,m).then(U=>_=U,U=>q=U),H.text().then(U=>L=U,()=>{})]),q)throw q}catch(q){return{error:{status:"PARSING_ERROR",originalStatus:C.status,data:L,error:String(q)},meta:s}}return y(C,_)?{data:_,meta:s}:{error:{status:C.status,data:_},meta:s}};async function x(h,I){if(typeof I=="function")return I(h);if(I==="content-type"&&(I=l(h.headers)?"json":"text"),I==="json"){let M=await h.text();return M.length?JSON.parse(M):null}return h.text()}}var J=class{constructor(n,p=void 0){this.value=n;this.meta=p}};async function jt(e=0,n=5){let p=Math.min(e,n),g=~~((Math.random()+.4)*(300<<p));await new Promise(l=>setTimeout(R=>l(R),g))}function Ht(e,n){throw Object.assign(new J({error:e,meta:n}),{throwImmediately:!0})}var dt={},Vt=(e,n)=>async(p,g,l)=>{let R=[5,(n||dt).maxRetries,(l||dt).maxRetries].filter(b=>b!==void 0),[T]=R.slice(-1),P={maxRetries:T,backoff:jt,retryCondition:(b,x,{attempt:h})=>h<=T,...n,...l},S=0;for(;;)try{let b=await e(p,g,l);if(b.error)throw new J(b);return b}catch(b){if(S++,b.throwImmediately){if(b instanceof J)return b.value;throw b}if(b instanceof J&&!P.retryCondition(b.value.error,p,{attempt:S,baseQueryApi:g,extraOptions:l}))return b.value;await P.backoff(S,P.maxRetries)}},_t=Object.assign(Vt,{fail:Ht});var te=Y("__rtkq/focused"),Qe=Y("__rtkq/unfocused"),ne=Y("__rtkq/online"),Te=Y("__rtkq/offline"),$e=!1;function zt(e,n){function p(){let g=()=>e(te()),l=()=>e(Qe()),R=()=>e(ne()),T=()=>e(Te()),B=()=>{window.document.visibilityState==="visible"?g():l()};return $e||typeof window<"u"&&window.addEventListener&&(window.addEventListener("visibilitychange",B,!1),window.addEventListener("focus",g,!1),window.addEventListener("online",R,!1),window.addEventListener("offline",T,!1),$e=!0),()=>{window.removeEventListener("focus",g),window.removeEventListener("visibilitychange",B),window.removeEventListener("online",R),window.removeEventListener("offline",T),$e=!1}}return n?n(e,{onFocus:te,onFocusLost:Qe,onOffline:Te,onOnline:ne}):p()}function ue(e){return e.type==="query"}function ct(e){return e.type==="mutation"}function ye(e){return e.type==="infinitequery"}function he(e){return ue(e)||ye(e)}function be(e,n,p,g,l,R){return Wt(e)?e(n,p,g,l).filter(se).map(ve).map(R):Array.isArray(e)?e.map(ve).map(R):[]}function Wt(e){return typeof e=="function"}function ve(e){return typeof e=="string"?{type:e}:e}import{isDraftable as Jt,produceWithPatches as Gt}from"immer";import"@reduxjs/toolkit";function lt(e,n){return e.catch(n)}var Re=Symbol("forceQueryFn"),Ee=e=>typeof e[Re]=="function";function ft({serializeQueryArgs:e,queryThunk:n,infiniteQueryThunk:p,mutationThunk:g,api:l,context:R,internalState:T}){let{runningQueries:B,runningMutations:P}=T,{unsubscribeQueryResult:S,removeMutationResult:b,updateSubscriptionOptions:x}=l.internalActions;return{buildInitiateQuery:f,buildInitiateInfiniteQuery:i,buildInitiateMutation:s,getRunningQueryThunk:h,getRunningMutationThunk:I,getRunningQueriesThunk:M,getRunningMutationsThunk:E};function h(d,Q){return A=>{let m=R.endpointDefinitions[d],y=e({queryArgs:Q,endpointDefinition:m,endpointName:d});return B.get(A)?.[y]}}function I(d,Q){return A=>P.get(A)?.[Q]}function M(){return d=>Object.values(B.get(d)||{}).filter(se)}function E(){return d=>Object.values(P.get(d)||{}).filter(se)}function D(d){}function r(d,Q){let A=(m,{subscribe:y=!0,forceRefetch:u,subscriptionOptions:t,[Re]:a,...o}={})=>(c,k)=>{let F=e({queryArgs:m,endpointDefinition:Q,endpointName:d}),K,C={...o,type:"query",subscribe:y,forceRefetch:u,subscriptionOptions:t,endpointName:d,originalArgs:m,queryCacheKey:F,[Re]:a};if(ue(Q))K=n(C);else{let{direction:v,initialPageParam:N}=o;K=p({...C,direction:v,initialPageParam:N})}let w=l.endpoints[d].select(m),O=c(K),H=w(k());let{requestId:_,abort:L}=O,q=H.requestId!==_,U=B.get(c)?.[F],j=()=>w(k()),V=Object.assign(a?O.then(j):q&&!U?Promise.resolve(H):Promise.all([U,O]).then(j),{arg:m,requestId:_,subscriptionOptions:t,queryCacheKey:F,abort:L,async unwrap(){let v=await V;if(v.isError)throw v.error;return v.data},refetch:()=>c(A(m,{subscribe:!1,forceRefetch:!0})),unsubscribe(){y&&c(S({queryCacheKey:F,requestId:_}))},updateSubscriptionOptions(v){V.subscriptionOptions=v,c(x({endpointName:d,requestId:_,queryCacheKey:F,options:v}))}});if(!U&&!q&&!a){let v=ut(B,c,{});v[F]=V,V.then(()=>{delete v[F],Fe(v)||B.delete(c)})}return V};return A}function f(d,Q){return r(d,Q)}function i(d,Q){return r(d,Q)}function s(d){return(Q,{track:A=!0,fixedCacheKey:m}={})=>(y,u)=>{let t=g({type:"mutation",endpointName:d,originalArgs:Q,track:A,fixedCacheKey:m}),a=y(t);let{requestId:o,abort:c,unwrap:k}=a,F=lt(a.unwrap().then(O=>({data:O})),O=>({error:O})),K=()=>{y(b({requestId:o,fixedCacheKey:m}))},C=Object.assign(F,{arg:a.arg,requestId:o,abort:c,unwrap:k,reset:K}),w=P.get(y)||{};return P.set(y,w),w[o]=C,C.then(()=>{delete w[o],Fe(w)||P.delete(y)}),m&&(w[m]=C,C.then(()=>{w[m]===C&&(delete w[m],Fe(w)||P.delete(y))})),C}}}import{SchemaError as $t}from"@standard-schema/utils";var Ae=class extends $t{constructor(p,g,l,R){super(p);this.value=g;this.schemaName=l;this._bqMeta=R}},re=(e,n)=>Array.isArray(e)?e.includes(n):!!e;async function ie(e,n,p,g){let l=await e["~standard"].validate(n);if(l.issues)throw new Ae(l.issues,n,p,g);return l.value}function mt(e){return e}var Pe=(e={})=>({...e,[we]:!0});function gt({reducerPath:e,baseQuery:n,context:{endpointDefinitions:p},serializeQueryArgs:g,api:l,assertTagType:R,selectors:T,onSchemaFailure:B,catchSchemaFailure:P,skipSchemaValidation:S}){let b=(t,a,o,c)=>(k,F)=>{let K=p[t],C=g({queryArgs:a,endpointDefinition:K,endpointName:t});if(k(l.internalActions.queryResultPatched({queryCacheKey:C,patches:o})),!c)return;let w=l.endpoints[t].select(a)(F()),O=be(K.providesTags,w.data,void 0,a,{},R);k(l.internalActions.updateProvidedBy([{queryCacheKey:C,providedTags:O}]))};function x(t,a,o=0){let c=[a,...t];return o&&c.length>o?c.slice(0,-1):c}function h(t,a,o=0){let c=[...t,a];return o&&c.length>o?c.slice(1):c}let I=(t,a,o,c=!0)=>(k,F)=>{let C=l.endpoints[t].select(a)(F()),w={patches:[],inversePatches:[],undo:()=>k(l.util.patchQueryData(t,a,w.inversePatches,c))};if(C.status==="uninitialized")return w;let O;if("data"in C)if(Jt(C.data)){let[H,_,L]=Gt(C.data,o);w.patches.push(..._),w.inversePatches.push(...L),O=H}else O=o(C.data),w.patches.push({op:"replace",path:[],value:O}),w.inversePatches.push({op:"replace",path:[],value:C.data});return w.patches.length===0||k(l.util.patchQueryData(t,a,w.patches,c)),w},M=(t,a,o)=>c=>c(l.endpoints[t].initiate(a,{subscribe:!1,forceRefetch:!0,[Re]:()=>({data:o})})),E=(t,a)=>t.query&&t[a]?t[a]:mt,D=async(t,{signal:a,abort:o,rejectWithValue:c,fulfillWithValue:k,dispatch:F,getState:K,extra:C})=>{let w=p[t.endpointName],{metaSchema:O,skipSchemaValidation:H=S}=w;try{let _=mt,L={signal:a,abort:o,dispatch:F,getState:K,extra:C,endpoint:t.endpointName,type:t.type,forced:t.type==="query"?r(t,K()):void 0,queryCacheKey:t.type==="query"?t.queryCacheKey:void 0},q=t.type==="query"?t[Re]:void 0,U,j=async(v,N,z,de)=>{if(N==null&&v.pages.length)return Promise.resolve({data:v});let X={queryArg:t.originalArgs,pageParam:N},ce=await V(X),$=de?x:h;return{data:{pages:$(v.pages,ce.data,z),pageParams:$(v.pageParams,N,z)},meta:ce.meta}};async function V(v){let N,{extraOptions:z,argSchema:de,rawResponseSchema:X,responseSchema:ce}=w;if(de&&!re(H,"arg")&&(v=await ie(de,v,"argSchema",{})),q?N=q():w.query?(_=E(w,"transformResponse"),N=await n(w.query(v),L,z)):N=await w.queryFn(v,L,z,le=>n(le,L,z)),N.error)throw new J(N.error,N.meta);let{data:$}=N;X&&!re(H,"rawResponse")&&($=await ie(X,N.data,"rawResponseSchema",N.meta));let Z=await _($,N.meta,v);return ce&&!re(H,"response")&&(Z=await ie(ce,Z,"responseSchema",N.meta)),{...N,data:Z}}if(t.type==="query"&&"infiniteQueryOptions"in w){let{infiniteQueryOptions:v}=w,{maxPages:N=1/0}=v,z,de={pages:[],pageParams:[]},X=T.selectQueryEntry(K(),t.queryCacheKey)?.data,$=r(t,K())&&!t.direction||!X?de:X;if("direction"in t&&t.direction&&$.pages.length){let Z=t.direction==="backward",Ie=(Z?Je:Oe)(v,$,t.originalArgs);z=await j($,Ie,N,Z)}else{let{initialPageParam:Z=v.initialPageParam}=t,le=X?.pageParams??[],Ie=le[0]??Z,Xe=le.length;z=await j($,Ie,N),q&&(z={data:z.data.pages[0]});for(let Ze=1;Ze<Xe;Ze++){let Ot=Oe(v,z.data,t.originalArgs);z=await j(z.data,Ot,N)}}U=z}else U=await V(t.originalArgs);return O&&!re(H,"meta")&&U.meta&&(U.meta=await ie(O,U.meta,"metaSchema",U.meta)),k(U.data,Pe({fulfilledTimeStamp:Date.now(),baseQueryMeta:U.meta}))}catch(_){let L=_;if(L instanceof J){let q=E(w,"transformErrorResponse"),{rawErrorResponseSchema:U,errorResponseSchema:j}=w,{value:V,meta:v}=L;try{U&&!re(H,"rawErrorResponse")&&(V=await ie(U,V,"rawErrorResponseSchema",v)),O&&!re(H,"meta")&&(v=await ie(O,v,"metaSchema",v));let N=await q(V,v,t.originalArgs);return j&&!re(H,"errorResponse")&&(N=await ie(j,N,"errorResponseSchema",v)),c(N,Pe({baseQueryMeta:v}))}catch(N){L=N}}try{if(L instanceof Ae){let q={endpoint:t.endpointName,arg:t.originalArgs,type:t.type,queryCacheKey:t.type==="query"?t.queryCacheKey:void 0};w.onSchemaFailure?.(L,q),B?.(L,q);let{catchSchemaFailure:U=P}=w;if(U)return c(U(L,q),Pe({baseQueryMeta:L._bqMeta}))}}catch(q){L=q}throw console.error(L),L}};function r(t,a){let o=T.selectQueryEntry(a,t.queryCacheKey),c=T.selectConfig(a).refetchOnMountOrArgChange,k=o?.fulfilledTimeStamp,F=t.forceRefetch??(t.subscribe&&c);return F?F===!0||(Number(new Date)-Number(k))/1e3>=F:!1}let f=()=>_e(`${e}/executeQuery`,D,{getPendingMeta({arg:a}){let o=p[a.endpointName];return Pe({startedTimeStamp:Date.now(),...ye(o)?{direction:a.direction}:{}})},condition(a,{getState:o}){let c=o(),k=T.selectQueryEntry(c,a.queryCacheKey),F=k?.fulfilledTimeStamp,K=a.originalArgs,C=k?.originalArgs,w=p[a.endpointName],O=a.direction;return Ee(a)?!0:k?.status==="pending"?!1:r(a,c)||ue(w)&&w?.forceRefetch?.({currentArg:K,previousArg:C,endpointState:k,state:c})?!0:!(F&&!O)},dispatchConditionRejection:!0}),i=f(),s=f(),d=_e(`${e}/executeMutation`,D,{getPendingMeta(){return Pe({startedTimeStamp:Date.now()})}}),Q=t=>"force"in t,A=t=>"ifOlderThan"in t,m=(t,a,o)=>(c,k)=>{let F=Q(o)&&o.force,K=A(o)&&o.ifOlderThan,C=(O=!0)=>{let H={forceRefetch:O,isPrefetch:!0};return l.endpoints[t].initiate(a,H)},w=l.endpoints[t].select(a)(k());if(F)c(C());else if(K){let O=w?.fulfilledTimeStamp;if(!O){c(C());return}(Number(new Date)-Number(new Date(O)))/1e3>=K&&c(C())}else c(C(!1))};function y(t){return a=>a?.meta?.arg?.endpointName===t}function u(t,a){return{matchPending:ke(Be(t),y(a)),matchFulfilled:ke(W(t),y(a)),matchRejected:ke(fe(t),y(a))}}return{queryThunk:i,mutationThunk:d,infiniteQueryThunk:s,prefetch:m,updateQueryData:I,upsertQueryData:M,patchQueryData:b,buildMatchThunkActions:u}}function Oe(e,{pages:n,pageParams:p},g){let l=n.length-1;return e.getNextPageParam(n[l],n,p[l],p,g)}function Je(e,{pages:n,pageParams:p},g){return e.getPreviousPageParam?.(n[0],n,p[0],p,g)}function Ne(e,n,p,g){return be(p[e.meta.arg.endpointName][n],W(e)?e.payload:void 0,me(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,g)}import{isDraft as Yt}from"immer";import{applyPatches as Qt,original as Xt}from"immer";function qe(e,n,p){let g=e[n];g&&p(g)}function pe(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function Tt(e,n,p){let g=e[pe(n)];g&&p(g)}var Ke={};function ht({reducerPath:e,queryThunk:n,mutationThunk:p,serializeQueryArgs:g,context:{endpointDefinitions:l,apiUid:R,extractRehydrationInfo:T,hasRehydrationInfo:B},assertTagType:P,config:S}){let b=Y(`${e}/resetApiState`);function x(y,u,t,a){y[u.queryCacheKey]??={status:"uninitialized",endpointName:u.endpointName},qe(y,u.queryCacheKey,o=>{o.status="pending",o.requestId=t&&o.requestId?o.requestId:a.requestId,u.originalArgs!==void 0&&(o.originalArgs=u.originalArgs),o.startedTimeStamp=a.startedTimeStamp;let c=l[a.arg.endpointName];ye(c)&&"direction"in u&&(o.direction=u.direction)})}function h(y,u,t,a){qe(y,u.arg.queryCacheKey,o=>{if(o.requestId!==u.requestId&&!a)return;let{merge:c}=l[u.arg.endpointName];if(o.status="fulfilled",c)if(o.data!==void 0){let{fulfilledTimeStamp:k,arg:F,baseQueryMeta:K,requestId:C}=u,w=Se(o.data,O=>c(O,t,{arg:F.originalArgs,baseQueryMeta:K,fulfilledTimeStamp:k,requestId:C}));o.data=w}else o.data=t;else o.data=l[u.arg.endpointName].structuralSharing??!0?Ce(Yt(o.data)?Xt(o.data):o.data,t):t;delete o.error,o.fulfilledTimeStamp=u.fulfilledTimeStamp})}let I=ae({name:`${e}/queries`,initialState:Ke,reducers:{removeQueryResult:{reducer(y,{payload:{queryCacheKey:u}}){delete y[u]},prepare:ge()},cacheEntriesUpserted:{reducer(y,u){for(let t of u.payload){let{queryDescription:a,value:o}=t;x(y,a,!0,{arg:a,requestId:u.meta.requestId,startedTimeStamp:u.meta.timestamp}),h(y,{arg:a,requestId:u.meta.requestId,fulfilledTimeStamp:u.meta.timestamp,baseQueryMeta:{}},o,!0)}},prepare:y=>({payload:y.map(a=>{let{endpointName:o,arg:c,value:k}=a,F=l[o];return{queryDescription:{type:"query",endpointName:o,originalArgs:a.arg,queryCacheKey:g({queryArgs:c,endpointDefinition:F,endpointName:o})},value:k}}),meta:{[we]:!0,requestId:Me(),timestamp:Date.now()}})},queryResultPatched:{reducer(y,{payload:{queryCacheKey:u,patches:t}}){qe(y,u,a=>{a.data=Qt(a.data,t.concat())})},prepare:ge()}},extraReducers(y){y.addCase(n.pending,(u,{meta:t,meta:{arg:a}})=>{let o=Ee(a);x(u,a,o,t)}).addCase(n.fulfilled,(u,{meta:t,payload:a})=>{let o=Ee(t.arg);h(u,t,a,o)}).addCase(n.rejected,(u,{meta:{condition:t,arg:a,requestId:o},error:c,payload:k})=>{qe(u,a.queryCacheKey,F=>{if(!t){if(F.requestId!==o)return;F.status="rejected",F.error=k??c}})}).addMatcher(B,(u,t)=>{let{queries:a}=T(t);for(let[o,c]of Object.entries(a))(c?.status==="fulfilled"||c?.status==="rejected")&&(u[o]=c)})}}),M=ae({name:`${e}/mutations`,initialState:Ke,reducers:{removeMutationResult:{reducer(y,{payload:u}){let t=pe(u);t in y&&delete y[t]},prepare:ge()}},extraReducers(y){y.addCase(p.pending,(u,{meta:t,meta:{requestId:a,arg:o,startedTimeStamp:c}})=>{o.track&&(u[pe(t)]={requestId:a,status:"pending",endpointName:o.endpointName,startedTimeStamp:c})}).addCase(p.fulfilled,(u,{payload:t,meta:a})=>{a.arg.track&&Tt(u,a,o=>{o.requestId===a.requestId&&(o.status="fulfilled",o.data=t,o.fulfilledTimeStamp=a.fulfilledTimeStamp)})}).addCase(p.rejected,(u,{payload:t,error:a,meta:o})=>{o.arg.track&&Tt(u,o,c=>{c.requestId===o.requestId&&(c.status="rejected",c.error=t??a)})}).addMatcher(B,(u,t)=>{let{mutations:a}=T(t);for(let[o,c]of Object.entries(a))(c?.status==="fulfilled"||c?.status==="rejected")&&o!==c?.requestId&&(u[o]=c)})}}),E={tags:{},keys:{}},D=ae({name:`${e}/invalidation`,initialState:E,reducers:{updateProvidedBy:{reducer(y,u){for(let{queryCacheKey:t,providedTags:a}of u.payload){r(y,t);for(let{type:o,id:c}of a){let k=(y.tags[o]??={})[c||"__internal_without_id"]??=[];k.includes(t)||k.push(t)}y.keys[t]=a}},prepare:ge()}},extraReducers(y){y.addCase(I.actions.removeQueryResult,(u,{payload:{queryCacheKey:t}})=>{r(u,t)}).addMatcher(B,(u,t)=>{let{provided:a}=T(t);for(let[o,c]of Object.entries(a.tags??{}))for(let[k,F]of Object.entries(c)){let K=(u.tags[o]??={})[k||"__internal_without_id"]??=[];for(let C of F)K.includes(C)||K.push(C),u.keys[C]=a.keys[C]}}).addMatcher(oe(W(n),me(n)),(u,t)=>{f(u,[t])}).addMatcher(I.actions.cacheEntriesUpserted.match,(u,t)=>{let a=t.payload.map(({queryDescription:o,value:c})=>({type:"UNKNOWN",payload:c,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:o}}));f(u,a)})}});function r(y,u){let t=y.keys[u]??[];for(let a of t){let o=a.type,c=a.id??"__internal_without_id",k=y.tags[o]?.[c];k&&(y.tags[o][c]=k.filter(F=>F!==u))}delete y.keys[u]}function f(y,u){let t=u.map(a=>{let o=Ne(a,"providesTags",l,P),{queryCacheKey:c}=a.meta.arg;return{queryCacheKey:c,providedTags:o}});D.caseReducers.updateProvidedBy(y,D.actions.updateProvidedBy(t))}let i=ae({name:`${e}/subscriptions`,initialState:Ke,reducers:{updateSubscriptionOptions(y,u){},unsubscribeQueryResult(y,u){},internal_getRTKQSubscriptions(){}}}),s=ae({name:`${e}/internalSubscriptions`,initialState:Ke,reducers:{subscriptionsUpdated:{reducer(y,u){return Qt(y,u.payload)},prepare:ge()}}}),d=ae({name:`${e}/config`,initialState:{online:ot(),focused:at(),middlewareRegistered:!1,...S},reducers:{middlewareRegistered(y,{payload:u}){y.middlewareRegistered=y.middlewareRegistered==="conflict"||R!==u?"conflict":!0}},extraReducers:y=>{y.addCase(ne,u=>{u.online=!0}).addCase(Te,u=>{u.online=!1}).addCase(te,u=>{u.focused=!0}).addCase(Qe,u=>{u.focused=!1}).addMatcher(B,u=>({...u}))}}),Q=tt({queries:I.reducer,mutations:M.reducer,provided:D.reducer,subscriptions:s.reducer,config:d.reducer}),A=(y,u)=>Q(b.match(u)?void 0:y,u),m={...d.actions,...I.actions,...i.actions,...s.actions,...M.actions,...D.actions,resetApiState:b};return{reducer:A,actions:m}}var Ue=Symbol.for("RTKQ/skipToken"),St={status:"uninitialized"},Rt=Se(St,()=>{}),At=Se(St,()=>{});function xt({serializeQueryArgs:e,reducerPath:n,createSelector:p}){let g=i=>Rt,l=i=>At;return{buildQuerySelector:h,buildInfiniteQuerySelector:I,buildMutationSelector:M,selectInvalidatedBy:E,selectCachedArgsForQuery:D,selectApiState:T,selectQueries:B,selectMutations:S,selectQueryEntry:P,selectConfig:b};function R(i){return{...i,...Ve(i.status)}}function T(i){return i[n]}function B(i){return T(i)?.queries}function P(i,s){return B(i)?.[s]}function S(i){return T(i)?.mutations}function b(i){return T(i)?.config}function x(i,s,d){return Q=>{if(Q===Ue)return p(g,d);let A=e({queryArgs:Q,endpointDefinition:s,endpointName:i});return p(y=>P(y,A)??Rt,d)}}function h(i,s){return x(i,s,R)}function I(i,s){let{infiniteQueryOptions:d}=s;function Q(A){let m={...A,...Ve(A.status)},{isLoading:y,isError:u,direction:t}=m,a=t==="forward",o=t==="backward";return{...m,hasNextPage:r(d,m.data,m.originalArgs),hasPreviousPage:f(d,m.data,m.originalArgs),isFetchingNextPage:y&&a,isFetchingPreviousPage:y&&o,isFetchNextPageError:u&&a,isFetchPreviousPageError:u&&o}}return x(i,s,Q)}function M(){return i=>{let s;return typeof i=="object"?s=pe(i)??Ue:s=i,p(s===Ue?l:A=>T(A)?.mutations?.[s]??At,R)}}function E(i,s){let d=i[n],Q=new Set;for(let A of s.filter(se).map(ve)){let m=d.provided.tags[A.type];if(!m)continue;let y=(A.id!==void 0?m[A.id]:We(Object.values(m)))??[];for(let u of y)Q.add(u)}return We(Array.from(Q.values()).map(A=>{let m=d.queries[A];return m?[{queryCacheKey:A,endpointName:m.endpointName,originalArgs:m.originalArgs}]:[]}))}function D(i,s){return Object.values(B(i)).filter(d=>d?.endpointName===s&&d.status!=="uninitialized").map(d=>d.originalArgs)}function r(i,s,d){return s?Oe(i,s,d)!=null:!1}function f(i,s,d){return!s||!i.getPreviousPageParam?!1:Je(i,s,d)!=null}}import{formatProdErrorMessage as Zt}from"@reduxjs/toolkit";var Dt=WeakMap?new WeakMap:void 0,Le=({endpointName:e,queryArgs:n})=>{let p="",g=Dt?.get(n);if(typeof g=="string")p=g;else{let l=JSON.stringify(n,(R,T)=>(T=typeof T=="bigint"?{$bigint:T.toString()}:T,T=ee(T)?Object.keys(T).sort().reduce((B,P)=>(B[P]=T[P],B),{}):T,T));ee(n)&&Dt?.set(n,l),p=l}return`${e}(${p})`};import{weakMapMemoize as bt}from"reselect";function Ge(...e){return function(p){let g=bt(S=>p.extractRehydrationInfo?.(S,{reducerPath:p.reducerPath??"api"})),l={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...p,extractRehydrationInfo:g,serializeQueryArgs(S){let b=Le;if("serializeQueryArgs"in S.endpointDefinition){let x=S.endpointDefinition.serializeQueryArgs;b=h=>{let I=x(h);return typeof I=="string"?I:Le({...h,queryArgs:I})}}else p.serializeQueryArgs&&(b=p.serializeQueryArgs);return b(S)},tagTypes:[...p.tagTypes||[]]},R={endpointDefinitions:{},batch(S){S()},apiUid:Me(),extractRehydrationInfo:g,hasRehydrationInfo:bt(S=>g(S)!=null)},T={injectEndpoints:P,enhanceEndpoints({addTagTypes:S,endpoints:b}){if(S)for(let x of S)l.tagTypes.includes(x)||l.tagTypes.push(x);if(b)for(let[x,h]of Object.entries(b))typeof h=="function"?h(R.endpointDefinitions[x]):Object.assign(R.endpointDefinitions[x]||{},h);return T}},B=e.map(S=>S.init(T,l,R));function P(S){let b=S.endpoints({query:x=>({...x,type:"query"}),mutation:x=>({...x,type:"mutation"}),infiniteQuery:x=>({...x,type:"infinitequery"})});for(let[x,h]of Object.entries(b)){if(S.overrideExisting!==!0&&x in R.endpointDefinitions){if(S.overrideExisting==="throw")throw new Error(Zt(39));continue}R.endpointDefinitions[x]=h;for(let I of B)I.injectEndpoint(x,h)}return T}return T.injectEndpoints({endpoints:p.endpoints})}}import{formatProdErrorMessage as en}from"@reduxjs/toolkit";var tn=Symbol();function nn(){return function(){throw new Error(en(33))}}import{enablePatches as on}from"immer";function G(e,...n){return Object.assign(e,...n)}import{produceWithPatches as rn}from"immer";var Et=({api:e,queryThunk:n,internalState:p,mwApi:g})=>{let l=`${e.reducerPath}/subscriptions`,R=null,T=null,{updateSubscriptionOptions:B,unsubscribeQueryResult:P}=e.internalActions,S=(E,D)=>{if(B.match(D)){let{queryCacheKey:f,requestId:i,options:s}=D.payload,d=E.get(f);return d?.has(i)&&d.set(i,s),!0}if(P.match(D)){let{queryCacheKey:f,requestId:i}=D.payload,s=E.get(f);return s&&s.delete(i),!0}if(e.internalActions.removeQueryResult.match(D))return E.delete(D.payload.queryCacheKey),!0;if(n.pending.match(D)){let{meta:{arg:f,requestId:i}}=D,s=xe(E,f.queryCacheKey,De);return f.subscribe&&s.set(i,f.subscriptionOptions??s.get(i)??{}),!0}let r=!1;if(n.rejected.match(D)){let{meta:{condition:f,arg:i,requestId:s}}=D;if(f&&i.subscribe){let d=xe(E,i.queryCacheKey,De);d.set(s,i.subscriptionOptions??d.get(s)??{}),r=!0}}return r},b=()=>p.currentSubscriptions,I={getSubscriptions:b,getSubscriptionCount:E=>b().get(E)?.size??0,isRequestSubscribed:(E,D)=>!!b()?.get(E)?.get(D)};function M(E){return JSON.parse(JSON.stringify(Object.fromEntries([...E].map(([D,r])=>[D,Object.fromEntries(r)]))))}return(E,D)=>{if(R||(R=M(p.currentSubscriptions)),e.util.resetApiState.match(E))return R={},p.currentSubscriptions.clear(),T=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(E))return[!1,I];let r=S(p.currentSubscriptions,E),f=!0;if(r){T||(T=setTimeout(()=>{let d=M(p.currentSubscriptions),[,Q]=rn(R,()=>d);D.next(e.internalActions.subscriptionsUpdated(Q)),R=d,T=null},500));let i=typeof E.type=="string"&&!!E.type.startsWith(l),s=n.rejected.match(E)&&E.meta.condition&&!!E.meta.arg.subscribe;f=!i&&!s}return[f,!1]}};var an=2147483647/1e3-1,Pt=({reducerPath:e,api:n,queryThunk:p,context:g,internalState:l,selectors:{selectQueryEntry:R,selectConfig:T},getRunningQueryThunk:B,mwApi:P})=>{let{removeQueryResult:S,unsubscribeQueryResult:b,cacheEntriesUpserted:x}=n.internalActions,h=l.runningQueries.get(P.dispatch),I=oe(b.match,p.fulfilled,p.rejected,x.match);function M(i){let s=l.currentSubscriptions.get(i);if(!s)return!1;let d=s.size>0,Q=h?.[i]!==void 0;return d||Q}let E={},D=(i,s,d)=>{let Q=s.getState(),A=T(Q);if(I(i)){let m;if(x.match(i))m=i.payload.map(y=>y.queryDescription.queryCacheKey);else{let{queryCacheKey:y}=b.match(i)?i.payload:i.meta.arg;m=[y]}r(m,s,A)}if(n.util.resetApiState.match(i))for(let[m,y]of Object.entries(E))y&&clearTimeout(y),delete E[m];if(g.hasRehydrationInfo(i)){let{queries:m}=g.extractRehydrationInfo(i);r(Object.keys(m),s,A)}};function r(i,s,d){let Q=s.getState();for(let A of i){let m=R(Q,A);m?.endpointName&&f(A,m.endpointName,s,d)}}function f(i,s,d,Q){let m=g.endpointDefinitions[s]?.keepUnusedDataFor??Q.keepUnusedDataFor;if(m===1/0)return;let y=Math.max(0,Math.min(m,an));if(!M(i)){let u=E[i];u&&clearTimeout(u),E[i]=setTimeout(()=>{if(!M(i)){let t=R(d.getState(),i);t?.endpointName&&d.dispatch(B(t.endpointName,t.originalArgs))?.abort(),d.dispatch(S({queryCacheKey:i}))}delete E[i]},y*1e3)}}return D};var It=new Error("Promise never resolved before cacheEntryRemoved."),kt=({api:e,reducerPath:n,context:p,queryThunk:g,mutationThunk:l,internalState:R,selectors:{selectQueryEntry:T,selectApiState:B}})=>{let P=ze(g),S=ze(l),b=W(g,l),x={};function h(r,f,i){let s=x[r];s?.valueResolved&&(s.valueResolved({data:f,meta:i}),delete s.valueResolved)}function I(r){let f=x[r];f&&(delete x[r],f.cacheEntryRemoved())}let M=(r,f,i)=>{let s=E(r);function d(Q,A,m,y){let u=T(i,A),t=T(f.getState(),A);!u&&t&&D(Q,y,A,f,m)}if(g.pending.match(r))d(r.meta.arg.endpointName,s,r.meta.requestId,r.meta.arg.originalArgs);else if(e.internalActions.cacheEntriesUpserted.match(r))for(let{queryDescription:Q,value:A}of r.payload){let{endpointName:m,originalArgs:y,queryCacheKey:u}=Q;d(m,u,r.meta.requestId,y),h(u,A,{})}else if(l.pending.match(r))f.getState()[n].mutations[s]&&D(r.meta.arg.endpointName,r.meta.arg.originalArgs,s,f,r.meta.requestId);else if(b(r))h(s,r.payload,r.meta.baseQueryMeta);else if(e.internalActions.removeQueryResult.match(r)||e.internalActions.removeMutationResult.match(r))I(s);else if(e.util.resetApiState.match(r))for(let Q of Object.keys(x))I(Q)};function E(r){return P(r)?r.meta.arg.queryCacheKey:S(r)?r.meta.arg.fixedCacheKey??r.meta.requestId:e.internalActions.removeQueryResult.match(r)?r.payload.queryCacheKey:e.internalActions.removeMutationResult.match(r)?pe(r.payload):""}function D(r,f,i,s,d){let Q=p.endpointDefinitions[r],A=Q?.onCacheEntryAdded;if(!A)return;let m={},y=new Promise(k=>{m.cacheEntryRemoved=k}),u=Promise.race([new Promise(k=>{m.valueResolved=k}),y.then(()=>{throw It})]);u.catch(()=>{}),x[i]=m;let t=e.endpoints[r].select(he(Q)?f:i),a=s.dispatch((k,F,K)=>K),o={...s,getCacheEntry:()=>t(s.getState()),requestId:d,extra:a,updateCachedData:he(Q)?k=>s.dispatch(e.util.updateQueryData(r,f,k)):void 0,cacheDataLoaded:u,cacheEntryRemoved:y},c=A(f,o);Promise.resolve(c).catch(k=>{if(k!==It)throw k})}return M};var Bt=({api:e,context:{apiUid:n},reducerPath:p})=>(g,l)=>{e.util.resetApiState.match(g)&&l.dispatch(e.internalActions.middlewareRegistered(n))};var wt=({reducerPath:e,context:n,context:{endpointDefinitions:p},mutationThunk:g,queryThunk:l,api:R,assertTagType:T,refetchQuery:B,internalState:P})=>{let{removeQueryResult:S}=R.internalActions,b=oe(W(g),me(g)),x=oe(W(g,l),fe(g,l)),h=[],I=(D,r)=>{b(D)?E(Ne(D,"invalidatesTags",p,T),r):x(D)?E([],r):R.util.invalidateTags.match(D)&&E(be(D.payload,void 0,void 0,void 0,void 0,T),r)};function M(D){let{queries:r,mutations:f}=D;for(let i of[r,f])for(let s in i)if(i[s]?.status==="pending")return!0;return!1}function E(D,r){let f=r.getState(),i=f[e];if(h.push(...D),i.config.invalidationBehavior==="delayed"&&M(i))return;let s=h;if(h=[],s.length===0)return;let d=R.util.selectInvalidatedBy(f,s);n.batch(()=>{let Q=Array.from(d.values());for(let{queryCacheKey:A}of Q){let m=i.queries[A],y=xe(P.currentSubscriptions,A,De);m&&(y.size===0?r.dispatch(S({queryCacheKey:A})):m.status!=="uninitialized"&&r.dispatch(B(m)))}})}return I};var Mt=({reducerPath:e,queryThunk:n,api:p,refetchQuery:g,internalState:l})=>{let{currentPolls:R,currentSubscriptions:T}=l,B=new Set,P=null,S=(r,f)=>{(p.internalActions.updateSubscriptionOptions.match(r)||p.internalActions.unsubscribeQueryResult.match(r))&&b(r.payload.queryCacheKey,f),(n.pending.match(r)||n.rejected.match(r)&&r.meta.condition)&&b(r.meta.arg.queryCacheKey,f),(n.fulfilled.match(r)||n.rejected.match(r)&&!r.meta.condition)&&h(r.meta.arg,f),p.util.resetApiState.match(r)&&(E(),P&&(clearTimeout(P),P=null),B.clear())};function b(r,f){B.add(r),P||(P=setTimeout(()=>{for(let i of B)I({queryCacheKey:i},f);B.clear(),P=null},0))}function x(r,f){let s=f.getState()[e].queries[r],d=T.get(r);if(!(!s||s.status==="uninitialized"))return d}function h({queryCacheKey:r},f){let i=f.getState()[e],s=i.queries[r],d=T.get(r);if(!s||s.status==="uninitialized")return;let{lowestPollingInterval:Q,skipPollingIfUnfocused:A}=D(d);if(!Number.isFinite(Q))return;let m=R.get(r);m?.timeout&&(clearTimeout(m.timeout),m.timeout=void 0);let y=Date.now()+Q;R.set(r,{nextPollTimestamp:y,pollingInterval:Q,timeout:setTimeout(()=>{(i.config.focused||!A)&&f.dispatch(g(s)),h({queryCacheKey:r},f)},Q)})}function I({queryCacheKey:r},f){let s=f.getState()[e].queries[r],d=T.get(r);if(!s||s.status==="uninitialized")return;let{lowestPollingInterval:Q}=D(d);if(!Number.isFinite(Q)){M(r);return}let A=R.get(r),m=Date.now()+Q;(!A||m<A.nextPollTimestamp)&&h({queryCacheKey:r},f)}function M(r){let f=R.get(r);f?.timeout&&clearTimeout(f.timeout),R.delete(r)}function E(){for(let r of R.keys())M(r)}function D(r=new Map){let f=!1,i=Number.POSITIVE_INFINITY;for(let s of r.values())s.pollingInterval&&(i=Math.min(s.pollingInterval,i),f=s.skipPollingIfUnfocused||f);return{lowestPollingInterval:i,skipPollingIfUnfocused:f}}return S};var Ct=({api:e,context:n,queryThunk:p,mutationThunk:g})=>{let l=Be(p,g),R=fe(p,g),T=W(p,g),B={};return(S,b)=>{if(l(S)){let{requestId:x,arg:{endpointName:h,originalArgs:I}}=S.meta,M=n.endpointDefinitions[h],E=M?.onQueryStarted;if(E){let D={},r=new Promise((d,Q)=>{D.resolve=d,D.reject=Q});r.catch(()=>{}),B[x]=D;let f=e.endpoints[h].select(he(M)?I:x),i=b.dispatch((d,Q,A)=>A),s={...b,getCacheEntry:()=>f(b.getState()),requestId:x,extra:i,updateCachedData:he(M)?d=>b.dispatch(e.util.updateQueryData(h,I,d)):void 0,queryFulfilled:r};E(I,s)}}else if(T(S)){let{requestId:x,baseQueryMeta:h}=S.meta;B[x]?.resolve({data:S.payload,meta:h}),delete B[x]}else if(R(S)){let{requestId:x,rejectedWithValue:h,baseQueryMeta:I}=S.meta;B[x]?.reject({error:S.payload??S.error,isUnhandledError:!h,meta:I}),delete B[x]}}};var Ft=({reducerPath:e,context:n,api:p,refetchQuery:g,internalState:l})=>{let{removeQueryResult:R}=p.internalActions,T=(P,S)=>{te.match(P)&&B(S,"refetchOnFocus"),ne.match(P)&&B(S,"refetchOnReconnect")};function B(P,S){let b=P.getState()[e],x=b.queries,h=l.currentSubscriptions;n.batch(()=>{for(let I of h.keys()){let M=x[I],E=h.get(I);if(!E||!M)continue;let D=[...E.values()];(D.some(f=>f[S]===!0)||D.every(f=>f[S]===void 0)&&b.config[S])&&(E.size===0?P.dispatch(R({queryCacheKey:I})):M.status!=="uninitialized"&&P.dispatch(g(M)))}})}return T};function vt(e){let{reducerPath:n,queryThunk:p,api:g,context:l,internalState:R}=e,{apiUid:T}=l,B={invalidateTags:Y(`${n}/invalidateTags`)},P=h=>h.type.startsWith(`${n}/`),S=[Bt,Pt,wt,Mt,kt,Ct];return{middleware:h=>{let I=!1,M={...e,internalState:R,refetchQuery:x,isThisApiSliceAction:P,mwApi:h},E=S.map(f=>f(M)),D=Et(M),r=Ft(M);return f=>i=>{if(!nt(i))return f(i);I||(I=!0,h.dispatch(g.internalActions.middlewareRegistered(T)));let s={...h,next:f},d=h.getState(),[Q,A]=D(i,s,d),m;if(Q?m=f(i):m=A,h.getState()[n]&&(r(i,s,d),P(i)||l.hasRehydrationInfo(i)))for(let y of E)y(i,s,d);return m}},actions:B};function x(h){return e.api.endpoints[h.endpointName].initiate(h.originalArgs,{subscribe:!1,forceRefetch:!0})}}var je=Symbol(),Ye=({createSelector:e=et}={})=>({name:je,init(n,{baseQuery:p,tagTypes:g,reducerPath:l,serializeQueryArgs:R,keepUnusedDataFor:T,refetchOnMountOrArgChange:B,refetchOnFocus:P,refetchOnReconnect:S,invalidationBehavior:b,onSchemaFailure:x,catchSchemaFailure:h,skipSchemaValidation:I},M){on();let E=j=>j;Object.assign(n,{reducerPath:l,endpoints:{},internalActions:{onOnline:ne,onOffline:Te,onFocus:te,onFocusLost:Qe},util:{}});let D=xt({serializeQueryArgs:R,reducerPath:l,createSelector:e}),{selectInvalidatedBy:r,selectCachedArgsForQuery:f,buildQuerySelector:i,buildInfiniteQuerySelector:s,buildMutationSelector:d}=D;G(n.util,{selectInvalidatedBy:r,selectCachedArgsForQuery:f});let{queryThunk:Q,infiniteQueryThunk:A,mutationThunk:m,patchQueryData:y,updateQueryData:u,upsertQueryData:t,prefetch:a,buildMatchThunkActions:o}=gt({baseQuery:p,reducerPath:l,context:M,api:n,serializeQueryArgs:R,assertTagType:E,selectors:D,onSchemaFailure:x,catchSchemaFailure:h,skipSchemaValidation:I}),{reducer:c,actions:k}=ht({context:M,queryThunk:Q,infiniteQueryThunk:A,mutationThunk:m,serializeQueryArgs:R,reducerPath:l,assertTagType:E,config:{refetchOnFocus:P,refetchOnReconnect:S,refetchOnMountOrArgChange:B,keepUnusedDataFor:T,reducerPath:l,invalidationBehavior:b}});G(n.util,{patchQueryData:y,updateQueryData:u,upsertQueryData:t,prefetch:a,resetApiState:k.resetApiState,upsertQueryEntries:k.cacheEntriesUpserted}),G(n.internalActions,k);let F={currentSubscriptions:new Map,currentPolls:new Map,runningQueries:new Map,runningMutations:new Map},{buildInitiateQuery:K,buildInitiateInfiniteQuery:C,buildInitiateMutation:w,getRunningMutationThunk:O,getRunningMutationsThunk:H,getRunningQueriesThunk:_,getRunningQueryThunk:L}=ft({queryThunk:Q,mutationThunk:m,infiniteQueryThunk:A,api:n,serializeQueryArgs:R,context:M,internalState:F});G(n.util,{getRunningMutationThunk:O,getRunningMutationsThunk:H,getRunningQueryThunk:L,getRunningQueriesThunk:_});let{middleware:q,actions:U}=vt({reducerPath:l,context:M,queryThunk:Q,mutationThunk:m,infiniteQueryThunk:A,api:n,assertTagType:E,selectors:D,getRunningQueryThunk:L,internalState:F});return G(n.util,U),G(n,{reducer:c,middleware:q}),{name:je,injectEndpoint(j,V){let v=n,N=v.endpoints[j]??={};ue(V)&&G(N,{name:j,select:i(j,V),initiate:K(j,V)},o(Q,j)),ct(V)&&G(N,{name:j,select:d(),initiate:w(j)},o(m,j)),ye(V)&&G(N,{name:j,select:s(j,V),initiate:C(j,V)},o(Q,j))}}}});var sn=Ge(Ye());export{Ae as NamedSchemaError,He as QueryStatus,tn as _NEVER,Ge as buildCreateApi,Ce as copyWithStructuralSharing,Ye as coreModule,je as coreModuleName,sn as createApi,Le as defaultSerializeQueryArgs,nn as fakeBaseQuery,Lt as fetchBaseQuery,_t as retry,zt as setupListeners,Ue as skipToken};
//# sourceMappingURL=rtk-query.browser.mjs.map
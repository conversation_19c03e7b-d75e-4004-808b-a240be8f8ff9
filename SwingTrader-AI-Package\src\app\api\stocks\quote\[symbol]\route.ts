import { NextRequest, NextResponse } from 'next/server'
import { FMPAPI } from '@/lib/fmp'
import { PolygonAPI } from '@/lib/polygon'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ symbol: string }> }
) {
  try {
    const { symbol } = await params

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      )
    }

    // Use FMP API for stock quotes
    const fmpAPI = new FMPAPI(process.env.FMP_API_KEY)
    const quote = await fmpAPI.getStockQuote(symbol.toUpperCase())

    return NextResponse.json(quote)
  } catch (error) {
    console.error('Error in quote API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch stock quote' },
      { status: 500 }
    )
  }
}

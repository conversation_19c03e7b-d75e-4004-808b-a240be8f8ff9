import { CandlestickData, StockData, SwingTradingAnalysis } from '@/types/trading'
import { TechnicalIndicators } from './indicators'

export interface StrategySetup {
  strategy: 'overnight_momentum' | 'technical_breakout'
  confidence: number
  entryPrice: number
  stopLoss: number
  targets: number[]
  positionSize: number
  riskAmount: number
  holdingPeriod: 'overnight' | 'days_to_weeks'
  keyLevel: number
  invalidation: string
  notes: string[]
}

export interface SwingSetupCriteria {
  // Basic filters
  minPrice: number
  minVolume: number
  minMarketCap: number
  minATRPercent: number
  
  // Technical requirements
  above200SMA: boolean
  maxDistanceFrom8EMA: number // in ATR units
  minRoomToResistance: number // in ATR units
  
  // Timing
  scanTimeStart: string // "12:00"
  scanTimeEnd: string   // "16:00"
  
  // Risk management
  maxRiskPerTrade: number // percentage of account
  maxConcurrentPositions: number
}

export class SwingTradingStrategies {
  private static readonly DEFAULT_CRITERIA: SwingSetupCriteria = {
    minPrice: 5.0,
    minVolume: 100000, // Relaxed from 500k for testing
    minMarketCap: *********, // $500M - relaxed from 800M
    minATRPercent: 1.0, // Relaxed from 1.5
    above200SMA: false, // Disabled for testing
    maxDistanceFrom8EMA: 5.0, // Very relaxed from 3.0
    minRoomToResistance: 0.2, // Very relaxed from 0.5
    scanTimeStart: "09:00", // Extended hours for testing
    scanTimeEnd: "20:00", // Extended hours for testing
    maxRiskPerTrade: 2.0, // Increased from 1.0
    maxConcurrentPositions: 5 // Increased from 3
  }

  // Strategy #1: Overnight Momentum Continuation
  static analyzeOvernightMomentum(
    symbol: string,
    candles: CandlestickData[],
    quote: StockData,
    accountSize: number = 100000
  ): StrategySetup | null {
    if (candles.length < 50) return null

    const closes = candles.map(c => c.close)
    const highs = candles.map(c => c.high)
    const lows = candles.map(c => c.low)
    const volumes = candles.map(c => c.volume)
    
    const currentPrice = quote.price
    const currentVolume = quote.volume
    const changePercent = quote.changePercent

    // Calculate technical indicators (adjusted for shorter history)
    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1)) // Use 50-day instead of 200-day
    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))
    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))

    const current50SMA = sma50[sma50.length - 1]
    const current8EMA = ema8[ema8.length - 1]
    const currentATR = atr[atr.length - 1]

    // Basic qualification filters (using 50-day SMA instead of 200-day)
    if (!this.passesBasicFilters(quote, currentVolume, current50SMA, currentPrice)) {
      return null
    }

    // Check if it's a top intraday gainer (relaxed for testing)
    console.log(`📊 ${symbol} momentum check: ${changePercent}% (need ≥0.5%)`)
    if (changePercent < 0.5) {
      console.log(`❌ ${symbol} failed momentum: ${changePercent}% < 0.5%`)
      return null // Minimum 0.5% gain for momentum (relaxed for testing)
    }
    console.log(`✅ ${symbol} passed momentum check: ${changePercent}%`)

    // Check distance from 8-EMA (not wildly extended)
    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA) / currentATR
    if (distanceFrom8EMA > this.DEFAULT_CRITERIA.maxDistanceFrom8EMA) return null

    // Look for defended intraday level (simplified - using VWAP proxy)
    const vwap = this.calculateVWAP(candles.slice(-1)[0])
    const keyLevel = Math.max(vwap, currentPrice * 0.98) // Approximate defended level

    // Check if holding gains (>20% of day's range)
    const todayHigh = highs[highs.length - 1]
    const todayLow = lows[lows.length - 1]
    const dayRange = todayHigh - todayLow
    const currentFromLow = currentPrice - todayLow
    const holdingGainsPercent = currentFromLow / dayRange

    console.log(`📊 ${symbol} holding gains: ${(holdingGainsPercent * 100).toFixed(1)}% of range (need ≥20%)`)
    if (holdingGainsPercent < 0.2) {
      console.log(`❌ ${symbol} failed holding gains: ${(holdingGainsPercent * 100).toFixed(1)}% < 20%`)
      return null // Must hold >20% of range (relaxed for testing)
    }
    console.log(`✅ ${symbol} passed holding gains check: ${(holdingGainsPercent * 100).toFixed(1)}%`)

    // Calculate room to next resistance
    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)
    if (roomToResistance < this.DEFAULT_CRITERIA.minRoomToResistance) return null

    // Position sizing (risk 0.5-1% of account)
    const riskPercent = 0.75 // 0.75% risk for overnight holds
    const stopDistance = currentPrice - keyLevel
    const riskAmount = accountSize * (riskPercent / 100)
    const positionSize = Math.floor(riskAmount / stopDistance)

    // Targets: Pre-market scale at 3-5%, opening hour at 5-8%
    const targets = [
      currentPrice * 1.03, // 3% pre-market target
      currentPrice * 1.05, // 5% opening hour target
      currentPrice * 1.08  // 8% extended target
    ]

    const confidence = this.calculateOvernightConfidence(
      changePercent, holdingGainsPercent, currentVolume, roomToResistance
    )

    return {
      strategy: 'overnight_momentum',
      confidence,
      entryPrice: currentPrice,
      stopLoss: keyLevel,
      targets,
      positionSize,
      riskAmount,
      holdingPeriod: 'overnight',
      keyLevel,
      invalidation: `Daily close below ${keyLevel.toFixed(2)} or gap down below level`,
      notes: [
        'Enter final 30-60 min before close',
        'Exit pre-market on strength or first 45min',
        'Hard stop if gaps below defended level',
        'Scale out aggressively if gaps >1 ATR up'
      ]
    }
  }

  // Strategy #2: Technical Breakout Trend-Follow (8-EMA + 200-SMA)
  static analyzeTechnicalBreakout(
    symbol: string,
    candles: CandlestickData[],
    quote: StockData,
    accountSize: number = 100000
  ): StrategySetup | null {
    if (candles.length < 50) return null

    const closes = candles.map(c => c.close)
    const volumes = candles.map(c => c.volume)
    const currentPrice = quote.price

    // Calculate technical indicators (adjusted for shorter history)
    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1))
    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))
    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))

    const current50SMA = sma50[sma50.length - 1]
    const current8EMA = ema8[ema8.length - 1]
    const currentATR = atr[atr.length - 1]

    // Basic qualification filters (using 50-day SMA)
    if (!this.passesBasicFilters(quote, quote.volume, current50SMA, currentPrice)) {
      return null
    }

    // Check if in clear uptrend above 50-SMA (adjusted from 200-SMA)
    if (currentPrice <= current50SMA) return null

    // Check 8-EMA behavior - should be "hugging" the 8-EMA
    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA)
    const emaDistancePercent = (distanceFrom8EMA / currentPrice) * 100
    
    // Should be close to 8-EMA (within 2-3% for quality trend-follow)
    if (emaDistancePercent > 3.0) return null

    // Check for recent breakout or EMA reclaim
    const recentEMAReclaim = this.checkEMAReclaim(closes, ema8, 5) // Last 5 days
    if (!recentEMAReclaim) return null

    // Volume expansion check
    const avgVolume = TechnicalIndicators.sma(volumes, 20)
    const currentAvgVolume = avgVolume[avgVolume.length - 1]
    const volumeExpansion = quote.volume / currentAvgVolume
    
    if (volumeExpansion < 1.2) return null // Need some volume expansion

    // Calculate room to next resistance
    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)
    if (roomToResistance < 1.5) return null // Need more room for trend-follow

    // Position sizing (risk 1% of account)
    const riskPercent = 1.0
    const stopDistance = currentPrice - current8EMA // Stop at 8-EMA break
    const riskAmount = accountSize * (riskPercent / 100)
    const positionSize = Math.floor(riskAmount / stopDistance)

    // Targets: Scale at resistance levels
    const targets = [
      currentPrice * 1.05, // 5% first target
      currentPrice * 1.10, // 10% second target
      currentPrice * 1.15  // 15% extended target
    ]

    const confidence = this.calculateBreakoutConfidence(
      emaDistancePercent, volumeExpansion, roomToResistance, quote.changePercent
    )

    return {
      strategy: 'technical_breakout',
      confidence,
      entryPrice: currentPrice,
      stopLoss: current8EMA,
      targets,
      positionSize,
      riskAmount,
      holdingPeriod: 'days_to_weeks',
      keyLevel: current8EMA,
      invalidation: `Daily close below 8-EMA (${current8EMA.toFixed(2)})`,
      notes: [
        'Enter on afternoon reclaim of 8-EMA',
        'Add only on higher-low pullbacks to 8-EMA',
        'Scale partials at resistance levels',
        'Exit on daily close below 8-EMA'
      ]
    }
  }

  // Helper methods
  private static passesBasicFilters(
    quote: StockData,
    volume: number,
    sma50: number,
    price: number
  ): boolean {
    const priceCheck = price >= this.DEFAULT_CRITERIA.minPrice
    const volumeCheck = volume >= this.DEFAULT_CRITERIA.minVolume
    const marketCapCheck = (quote.marketCap || 1000000000) >= this.DEFAULT_CRITERIA.minMarketCap // Default to 1B for large caps
    const smaCheck = price > sma50

    console.log(`🔍 Basic filters for ${quote.symbol}:`)
    console.log(`  💰 Price: ${price} >= ${this.DEFAULT_CRITERIA.minPrice} = ${priceCheck}`)
    console.log(`  📊 Volume: ${volume} >= ${this.DEFAULT_CRITERIA.minVolume} = ${volumeCheck}`)
    console.log(`  🏢 Market Cap: ${quote.marketCap || 'default 1B'} >= ${this.DEFAULT_CRITERIA.minMarketCap} = ${marketCapCheck}`)
    console.log(`  📈 Above SMA50: ${price.toFixed(2)} > ${sma50.toFixed(2)} = ${smaCheck}`)

    const passes = priceCheck && volumeCheck && marketCapCheck && smaCheck
    console.log(`  ✅ Overall: ${passes}`)

    return passes
  }

  private static calculateATR(candles: CandlestickData[], period: number): number[] {
    const trueRanges: number[] = []
    
    for (let i = 1; i < candles.length; i++) {
      const high = candles[i].high
      const low = candles[i].low
      const prevClose = candles[i - 1].close
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      )
      
      trueRanges.push(tr)
    }
    
    return TechnicalIndicators.sma(trueRanges, period)
  }

  private static calculateVWAP(candle: CandlestickData): number {
    // Simplified VWAP calculation using typical price
    return (candle.high + candle.low + candle.close) / 3
  }

  private static calculateRoomToResistance(
    candles: CandlestickData[], 
    currentPrice: number, 
    atr: number
  ): number {
    // Find recent highs as resistance levels
    const recentHighs = candles.slice(-20).map(c => c.high)
    const maxHigh = Math.max(...recentHighs)
    const roomToHigh = maxHigh - currentPrice
    return roomToHigh / atr
  }

  private static checkEMAReclaim(closes: number[], ema8: number[], lookback: number): boolean {
    // Check if price recently reclaimed 8-EMA
    for (let i = Math.max(0, closes.length - lookback); i < closes.length - 1; i++) {
      if (closes[i] < ema8[i] && closes[i + 1] > ema8[i + 1]) {
        return true // Found a reclaim
      }
    }
    return false
  }

  private static calculateOvernightConfidence(
    changePercent: number,
    holdingGains: number,
    volume: number,
    roomToResistance: number
  ): number {
    let confidence = 50

    // Change percent bonus
    if (changePercent > 5) confidence += 15
    else if (changePercent > 3) confidence += 10
    else if (changePercent > 2) confidence += 5

    // Holding gains bonus
    if (holdingGains > 0.8) confidence += 15
    else if (holdingGains > 0.6) confidence += 10
    else if (holdingGains > 0.5) confidence += 5

    // Volume bonus
    if (volume > 2000000) confidence += 10
    else if (volume > 1000000) confidence += 5

    // Room to resistance
    if (roomToResistance > 2) confidence += 10
    else if (roomToResistance > 1.5) confidence += 5

    return Math.min(95, Math.max(30, confidence))
  }

  private static calculateBreakoutConfidence(
    emaDistance: number,
    volumeExpansion: number,
    roomToResistance: number,
    changePercent: number
  ): number {
    let confidence = 60

    // EMA proximity bonus (closer is better for trend-follow)
    if (emaDistance < 1) confidence += 15
    else if (emaDistance < 2) confidence += 10
    else if (emaDistance < 3) confidence += 5

    // Volume expansion bonus
    if (volumeExpansion > 2) confidence += 15
    else if (volumeExpansion > 1.5) confidence += 10
    else if (volumeExpansion > 1.2) confidence += 5

    // Room to resistance
    if (roomToResistance > 3) confidence += 15
    else if (roomToResistance > 2) confidence += 10
    else if (roomToResistance > 1.5) confidence += 5

    // Positive momentum
    if (changePercent > 2) confidence += 5

    return Math.min(95, Math.max(40, confidence))
  }
}

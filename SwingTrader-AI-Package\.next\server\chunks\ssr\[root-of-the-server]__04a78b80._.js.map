{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(value)\n}\n\nexport function formatPercentage(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'percent',\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(value / 100)\n}\n\nexport function calculateRiskReward(\n  entryPrice: number,\n  stopLoss: number,\n  takeProfit: number\n): number {\n  const risk = Math.abs(entryPrice - stopLoss)\n  const reward = Math.abs(takeProfit - entryPrice)\n  return reward / risk\n}\n\nexport function calculatePositionSize(\n  accountSize: number,\n  riskPercentage: number,\n  entryPrice: number,\n  stopLoss: number\n): number {\n  const riskAmount = accountSize * (riskPercentage / 100)\n  const riskPerShare = Math.abs(entryPrice - stopLoss)\n  return Math.floor(riskAmount / riskPerShare)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sMAAO,EAAC,IAAA,6KAAI,EAAC;AACtB;AAEO,SAAS,eAAe,KAAa;IAC1C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,QAAQ;AACpB;AAEO,SAAS,oBACd,UAAkB,EAClB,QAAgB,EAChB,UAAkB;IAElB,MAAM,OAAO,KAAK,GAAG,CAAC,aAAa;IACnC,MAAM,SAAS,KAAK,GAAG,CAAC,aAAa;IACrC,OAAO,SAAS;AAClB;AAEO,SAAS,sBACd,WAAmB,EACnB,cAAsB,EACtB,UAAkB,EAClB,QAAgB;IAEhB,MAAM,aAAa,cAAc,CAAC,iBAAiB,GAAG;IACtD,MAAM,eAAe,KAAK,GAAG,CAAC,aAAa;IAC3C,OAAO,KAAK,KAAK,CAAC,aAAa;AACjC", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uMAAG,EACxB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,mPAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,wMAAI,GAAG;IAC9B,qBACE,8QAAC;QACC,WAAW,IAAA,yJAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,mPAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QACC,KAAK;QACL,WAAW,IAAA,yJAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,mPAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QACC,KAAK;QACL,WAAW,IAAA,yJAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,mPAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QACC,KAAK;QACL,WAAW,IAAA,yJAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,mPAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QACC,KAAK;QACL,WAAW,IAAA,yJAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,mPAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QAAI,KAAK;QAAK,WAAW,IAAA,yJAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,mPAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8QAAC;QACC,KAAK;QACL,WAAW,IAAA,yJAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,IAAA,uMAAG,EACvB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8QAAC;QAAI,WAAW,IAAA,yJAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/SwingScanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, Search, TrendingUp, TrendingDown, Minus, Target, Shield, BarChart3 } from 'lucide-react'\nimport { ScanResult, ScanSummary } from '@/lib/swingScanner'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface ScannerProps {\n  autoScan?: boolean\n}\n\nexport function SwingScanner({ autoScan = false }: ScannerProps) {\n  const [isScanning, setIsScanning] = useState(false)\n  const [scanResults, setScanResults] = useState<ScanSummary | null>(null)\n  const [selectedScan, setSelectedScan] = useState<'quick' | 'full' | 'sector'>('quick')\n  const [selectedSector, setSelectedSector] = useState<string>('Technology')\n  const [error, setError] = useState<string | null>(null)\n\n  const sectors = [\n    'Technology', 'Financial Services', 'Healthcare', 'Industrial', \n    'Materials', 'Consumer', 'Communication Services', 'Energy'\n  ]\n\n  // Auto-scan on component mount if enabled\n  useEffect(() => {\n    if (autoScan) {\n      handleQuickScan()\n    }\n  }, [autoScan])\n\n  const handleQuickScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch('/api/scanner/quick?limit=15')\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      \n      // Convert to ScanSummary format\n      const summary: ScanSummary = {\n        totalScanned: data.totalScanned,\n        successfulScans: data.results.length,\n        failedScans: data.totalScanned - data.results.length,\n        topOpportunities: data.results,\n        sectorBreakdown: {},\n        scanDuration: 0\n      }\n      \n      setScanResults(summary)\n    } catch (err) {\n      setError('Failed to perform quick scan. Please try again.')\n      console.error('Quick scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const handleFullScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch('/api/scanner/full?limit=25&concurrent=3')\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform full scan. Please try again.')\n      console.error('Full scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const handleSectorScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch(`/api/scanner/sector/${encodeURIComponent(selectedSector)}?limit=15`)\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform sector scan. Please try again.')\n      console.error('Sector scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const getTrendIcon = (trend: string) => {\n    switch (trend) {\n      case 'BULLISH':\n        return <TrendingUp className=\"h-4 w-4 text-green-400\" />\n      case 'BEARISH':\n        return <TrendingDown className=\"h-4 w-4 text-red-400\" />\n      default:\n        return <Minus className=\"h-4 w-4 text-yellow-400\" />\n    }\n  }\n\n  const getRecommendationColor = (recommendation: string) => {\n    if (recommendation.includes('BUY')) return 'bg-green-500/20 text-green-400'\n    if (recommendation.includes('SELL')) return 'bg-red-500/20 text-red-400'\n    return 'bg-yellow-500/20 text-yellow-400'\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Scanner Controls */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center\">\n            <Search className=\"mr-2 h-5 w-5 text-blue-400\" />\n            Swing Trading Scanner\n          </CardTitle>\n          <CardDescription className=\"text-slate-300\">\n            Automatically scan stocks for the best swing trading opportunities\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex flex-wrap gap-4 mb-4\">\n            <Button\n              onClick={handleQuickScan}\n              disabled={isScanning}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              {isScanning && selectedScan === 'quick' ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : null}\n              Quick Scan (Top 16)\n            </Button>\n            \n            <Button\n              onClick={handleFullScan}\n              disabled={isScanning}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n            >\n              {isScanning && selectedScan === 'full' ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : null}\n              Full Scan (All 70+ Stocks)\n            </Button>\n\n            <div className=\"flex gap-2\">\n              <select\n                value={selectedSector}\n                onChange={(e) => setSelectedSector(e.target.value)}\n                className=\"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n                disabled={isScanning}\n              >\n                {sectors.map(sector => (\n                  <option key={sector} value={sector}>{sector}</option>\n                ))}\n              </select>\n              <Button\n                onClick={handleSectorScan}\n                disabled={isScanning}\n                variant=\"outline\"\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n              >\n                {isScanning && selectedScan === 'sector' ? (\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                ) : null}\n                Scan Sector\n              </Button>\n            </div>\n          </div>\n\n          {isScanning && (\n            <div className=\"text-center py-4\">\n              <Loader2 className=\"mx-auto h-8 w-8 animate-spin text-blue-400\" />\n              <p className=\"text-slate-300 mt-2\">\n                Scanning stocks for swing trading opportunities...\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Error Display */}\n      {error && (\n        <Card className=\"bg-red-900/20 border-red-500/50\">\n          <CardContent className=\"p-6\">\n            <p className=\"text-red-300 text-center\">{error}</p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Scan Results */}\n      {scanResults && (\n        <div className=\"space-y-6\">\n          {/* Scan Summary */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white\">Scan Summary</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-white\">{scanResults.totalScanned}</div>\n                  <div className=\"text-sm text-slate-300\">Total Scanned</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">{scanResults.successfulScans}</div>\n                  <div className=\"text-sm text-slate-300\">Successful</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-400\">{scanResults.failedScans}</div>\n                  <div className=\"text-sm text-slate-300\">Failed</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">\n                    {scanResults.scanDuration ? `${(scanResults.scanDuration / 1000).toFixed(1)}s` : 'N/A'}\n                  </div>\n                  <div className=\"text-sm text-slate-300\">Duration</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Top Opportunities */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white flex items-center\">\n                <TrendingUp className=\"mr-2 h-5 w-5 text-green-400\" />\n                Top Swing Trading Opportunities\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {scanResults.topOpportunities.map((result, index) => (\n                  <div key={result.symbol} className=\"p-4 bg-slate-700/50 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"text-lg font-bold text-white\">#{result.rank}</div>\n                        <div>\n                          <div className=\"flex items-center space-x-2\">\n                            <span className=\"text-lg font-semibold text-white\">{result.symbol}</span>\n                            {getTrendIcon(result.analysis.trend)}\n                            <Badge className={getRecommendationColor(result.analysis.recommendation)}>\n                              {result.analysis.recommendation.replace('_', ' ')}\n                            </Badge>\n                          </div>\n                          <div className=\"text-sm text-slate-300\">{result.name}</div>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-xl font-bold text-white\">\n                          {result.score.toFixed(1)}/100\n                        </div>\n                        <div className=\"text-sm text-slate-300\">Score</div>\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                      <div>\n                        <div className=\"text-slate-300\">Price</div>\n                        <div className=\"text-white font-semibold\">\n                          {formatCurrency(result.quote.price)}\n                        </div>\n                        <div className={result.quote.change >= 0 ? \"text-green-400\" : \"text-red-400\"}>\n                          {formatPercentage(result.quote.changePercent)}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">Entry</div>\n                        <div className=\"text-blue-400 font-semibold\">\n                          {formatCurrency(result.analysis.entryPrice)}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">R/R Ratio</div>\n                        <div className=\"text-green-400 font-semibold\">\n                          {result.analysis.riskRewardRatio.toFixed(2)}:1\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">Confidence</div>\n                        <div className=\"text-white font-semibold\">\n                          {result.analysis.confidence.toFixed(1)}%\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AARA;;;;;;;;AAcO,SAAS,aAAa,EAAE,WAAW,KAAK,EAAgB;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iPAAQ,EAAC;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iPAAQ,EAAqB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iPAAQ,EAA8B;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iPAAQ,EAAS;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iPAAQ,EAAgB;IAElD,MAAM,UAAU;QACd;QAAc;QAAsB;QAAc;QAClD;QAAa;QAAY;QAA0B;KACpD;IAED,0CAA0C;IAC1C,IAAA,kPAAS,EAAC;QACR,IAAI,UAAU;YACZ;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB;QACtB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,gCAAgC;YAChC,MAAM,UAAuB;gBAC3B,cAAc,KAAK,YAAY;gBAC/B,iBAAiB,KAAK,OAAO,CAAC,MAAM;gBACpC,aAAa,KAAK,YAAY,GAAG,KAAK,OAAO,CAAC,MAAM;gBACpD,kBAAkB,KAAK,OAAO;gBAC9B,iBAAiB,CAAC;gBAClB,cAAc;YAChB;YAEA,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,oBAAoB;QACpC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB;QACvB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,mBAAmB,gBAAgB,SAAS,CAAC;YACjG,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8QAAC,gQAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,8QAAC,sQAAY;oBAAC,WAAU;;;;;;YACjC;gBACE,qBAAO,8QAAC,6OAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,IAAI,eAAe,QAAQ,CAAC,QAAQ,OAAO;QAC3C,IAAI,eAAe,QAAQ,CAAC,SAAS,OAAO;QAC5C,OAAO;IACT;IAEA,qBACE,8QAAC;QAAI,WAAU;;0BAEb,8QAAC,wKAAI;gBAAC,WAAU;;kCACd,8QAAC,8KAAU;;0CACT,8QAAC,6KAAS;gCAAC,WAAU;;kDACnB,8QAAC,gPAAM;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGnD,8QAAC,mLAAe;gCAAC,WAAU;0CAAiB;;;;;;;;;;;;kCAI9C,8QAAC,+KAAW;;0CACV,8QAAC;gCAAI,WAAU;;kDACb,8QAAC,4KAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;;4CAET,cAAc,iBAAiB,wBAC9B,8QAAC,4PAAO;gDAAC,WAAU;;;;;uDACjB;4CAAK;;;;;;;kDAIX,8QAAC,4KAAM;wCACL,SAAS;wCACT,UAAU;wCACV,SAAQ;wCACR,WAAU;;4CAET,cAAc,iBAAiB,uBAC9B,8QAAC,4PAAO;gDAAC,WAAU;;;;;uDACjB;4CAAK;;;;;;;kDAIX,8QAAC;wCAAI,WAAU;;0DACb,8QAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,WAAU;gDACV,UAAU;0DAET,QAAQ,GAAG,CAAC,CAAA,uBACX,8QAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;0DAGjB,8QAAC,4KAAM;gDACL,SAAS;gDACT,UAAU;gDACV,SAAQ;gDACR,WAAU;;oDAET,cAAc,iBAAiB,yBAC9B,8QAAC,4PAAO;wDAAC,WAAU;;;;;+DACjB;oDAAK;;;;;;;;;;;;;;;;;;;4BAMd,4BACC,8QAAC;gCAAI,WAAU;;kDACb,8QAAC,4PAAO;wCAAC,WAAU;;;;;;kDACnB,8QAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,uBACC,8QAAC,wKAAI;gBAAC,WAAU;0BACd,cAAA,8QAAC,+KAAW;oBAAC,WAAU;8BACrB,cAAA,8QAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;YAM9C,6BACC,8QAAC;gBAAI,WAAU;;kCAEb,8QAAC,wKAAI;wBAAC,WAAU;;0CACd,8QAAC,8KAAU;0CACT,cAAA,8QAAC,6KAAS;oCAAC,WAAU;8CAAa;;;;;;;;;;;0CAEpC,8QAAC,+KAAW;0CACV,cAAA,8QAAC;oCAAI,WAAU;;sDACb,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAiC,YAAY,YAAY;;;;;;8DACxE,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAqC,YAAY,eAAe;;;;;;8DAC/E,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAmC,YAAY,WAAW;;;;;;8DACzE,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DACZ,YAAY,YAAY,GAAG,GAAG,CAAC,YAAY,YAAY,GAAG,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;8DAEnF,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhD,8QAAC,wKAAI;wBAAC,WAAU;;0CACd,8QAAC,8KAAU;0CACT,cAAA,8QAAC,6KAAS;oCAAC,WAAU;;sDACnB,8QAAC,gQAAU;4CAAC,WAAU;;;;;;wCAAgC;;;;;;;;;;;;0CAI1D,8QAAC,+KAAW;0CACV,cAAA,8QAAC;oCAAI,WAAU;8CACZ,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzC,8QAAC;4CAAwB,WAAU;;8DACjC,8QAAC;oDAAI,WAAU;;sEACb,8QAAC;4DAAI,WAAU;;8EACb,8QAAC;oEAAI,WAAU;;wEAA+B;wEAAE,OAAO,IAAI;;;;;;;8EAC3D,8QAAC;;sFACC,8QAAC;4EAAI,WAAU;;8FACb,8QAAC;oFAAK,WAAU;8FAAoC,OAAO,MAAM;;;;;;gFAChE,aAAa,OAAO,QAAQ,CAAC,KAAK;8FACnC,8QAAC,0KAAK;oFAAC,WAAW,uBAAuB,OAAO,QAAQ,CAAC,cAAc;8FACpE,OAAO,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sFAGjD,8QAAC;4EAAI,WAAU;sFAA0B,OAAO,IAAI;;;;;;;;;;;;;;;;;;sEAGxD,8QAAC;4DAAI,WAAU;;8EACb,8QAAC;oEAAI,WAAU;;wEACZ,OAAO,KAAK,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAE3B,8QAAC;oEAAI,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;8DAI5C,8QAAC;oDAAI,WAAU;;sEACb,8QAAC;;8EACC,8QAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,8QAAC;oEAAI,WAAU;8EACZ,IAAA,qKAAc,EAAC,OAAO,KAAK,CAAC,KAAK;;;;;;8EAEpC,8QAAC;oEAAI,WAAW,OAAO,KAAK,CAAC,MAAM,IAAI,IAAI,mBAAmB;8EAC3D,IAAA,uKAAgB,EAAC,OAAO,KAAK,CAAC,aAAa;;;;;;;;;;;;sEAGhD,8QAAC;;8EACC,8QAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,8QAAC;oEAAI,WAAU;8EACZ,IAAA,qKAAc,EAAC,OAAO,QAAQ,CAAC,UAAU;;;;;;;;;;;;sEAG9C,8QAAC;;8EACC,8QAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,8QAAC;oEAAI,WAAU;;wEACZ,OAAO,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAGhD,8QAAC;;8EACC,8QAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,8QAAC;oEAAI,WAAU;;wEACZ,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;2CAhDrC,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DzC", "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/TradingCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Card, CardContent } from '@/components/ui/card'\nimport {\n  <PERSON>, TrendingUp, AlertTriangle, Brain, Play, CheckCircle,\n  ChevronDown, ChevronUp, Target, DollarSign, Clock, BarChart3,\n  Loader2, Zap, Cpu\n} from 'lucide-react'\nimport { EnhancedScanResult } from '@/lib/enhancedSwingScanner'\nimport { PaperTrade, AIAnalysis, TradingCardState } from '@/types/paperTrading'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface TradingCardProps {\n  result: EnhancedScanResult\n  accountSize: number\n  onPaperTradeExecuted?: (trade: PaperTrade) => void\n}\n\n// Helper function to determine AI exposure level\nfunction getAIExposureLevel(symbol: string): { level: 'Primary' | 'Secondary' | 'Adjacent' | null, icon: any, color: string } {\n  // Core AI/Tech Giants - Direct AI/ML focus\n  const primaryAI = ['MSFT', 'NVDA', 'GOOG', 'GOOGL', 'META', 'AVGO', 'ORCL', 'NFLX', 'AMD', 'TSM']\n\n  // AI Infrastructure & Semiconductors\n  const secondaryAI = ['ANET', 'MU', 'LRCX', 'DELL', 'WDC', 'VRT', 'ZS', 'NET', 'SHOP']\n\n  // Quantum Computing & Advanced AI\n  const quantumAI = ['IONQ', 'RGTI', 'SOUN', 'QBTS']\n\n  // AI-Adjacent Growth Stocks\n  const adjacentAI = [\n    'AAPL', 'AMZN', 'JPM', 'GE', 'CAT', 'BA', 'C', 'SCHW', 'UAL', 'DAL',\n    'CCJ', 'CEG', 'VST', 'FSLR', 'RUN', 'GILD', 'RBLX', 'RDDT',\n    'HOOD', 'SOFI', 'AFRM', 'FUTU', 'TIGR', 'RKLB', 'ASTS',\n    'RIOT', 'HUT', 'CELH', 'CVNA', 'ELF', 'W', 'ETSY',\n    'KGC', 'AEM', 'NEM', 'HL', 'AG', 'CLF', 'MP', 'BTU', 'CG'\n  ]\n\n  if (primaryAI.includes(symbol)) {\n    return { level: 'Primary', icon: Brain, color: 'bg-purple-500/20 text-purple-400 border-purple-500/30' }\n  }\n  if (secondaryAI.includes(symbol)) {\n    return { level: 'Secondary', icon: Cpu, color: 'bg-blue-500/20 text-blue-400 border-blue-500/30' }\n  }\n  if (quantumAI.includes(symbol)) {\n    return { level: 'Quantum', icon: Zap, color: 'bg-pink-500/20 text-pink-400 border-pink-500/30' }\n  }\n  if (adjacentAI.includes(symbol)) {\n    return { level: 'Adjacent', icon: Target, color: 'bg-cyan-500/20 text-cyan-400 border-cyan-500/30' }\n  }\n  return { level: null, icon: null, color: '' }\n}\n\nexport function TradingCard({ result, accountSize, onPaperTradeExecuted }: TradingCardProps) {\n  const [state, setState] = useState<TradingCardState>({\n    isExecuting: false,\n    isAnalyzing: false,\n    showAnalysis: false\n  })\n\n  const setup = result.overnightSetup || result.breakoutSetup\n  const strategyType = result.overnightSetup ? 'overnight_momentum' : 'technical_breakout'\n  const strategyColor = result.overnightSetup ? 'purple' : 'green'\n  const StrategyIcon = result.overnightSetup ? Moon : TrendingUp\n  const aiExposure = getAIExposureLevel(result.symbol)\n\n  const handleExecutePaperTrade = async () => {\n    setState(prev => ({ ...prev, isExecuting: true }))\n    \n    try {\n      const response = await fetch('/api/paper-trading/execute', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ scanResult: result, accountSize })\n      })\n\n      if (!response.ok) {\n        const error = await response.json()\n        throw new Error(error.error || 'Failed to execute paper trade')\n      }\n\n      const { paperTrade, executionDetails } = await response.json()\n      \n      setState(prev => ({ \n        ...prev, \n        paperTrade,\n        isExecuting: false \n      }))\n\n      onPaperTradeExecuted?.(paperTrade)\n      \n      // Show success notification\n      alert(`Paper trade executed successfully!\\n\\nSymbol: ${paperTrade.symbol}\\nPosition: ${paperTrade.positionSize} shares\\nEntry: ${formatCurrency(paperTrade.entryPrice)}\\nRisk: ${formatCurrency(paperTrade.riskAmount)}`)\n      \n    } catch (error) {\n      console.error('Paper trade execution failed:', error)\n      alert(`Failed to execute paper trade: ${error instanceof Error ? error.message : 'Unknown error'}`)\n      setState(prev => ({ ...prev, isExecuting: false }))\n    }\n  }\n\n  const handleGetAIAnalysis = async () => {\n    setState(prev => ({ ...prev, isAnalyzing: true }))\n    \n    try {\n      const response = await fetch('/api/analysis/ai-setup', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ scanResult: result })\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to get AI analysis')\n      }\n\n      const aiAnalysis: AIAnalysis = await response.json()\n      \n      setState(prev => ({ \n        ...prev, \n        aiAnalysis,\n        isAnalyzing: false,\n        showAnalysis: true \n      }))\n      \n    } catch (error) {\n      console.error('AI analysis failed:', error)\n      alert('Failed to get AI analysis. Please try again.')\n      setState(prev => ({ ...prev, isAnalyzing: false }))\n    }\n  }\n\n  if (!setup) return null\n\n  return (\n    <Card className=\"bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-colors\">\n      <CardContent className=\"p-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center\">\n            <div className=\"flex items-center\">\n              <span className=\"text-2xl font-bold text-white mr-3\">{result.symbol}</span>\n              <Badge className={`${strategyColor === 'purple' ? 'bg-purple-500/20 text-purple-400' : 'bg-green-500/20 text-green-400'}`}>\n                #{result.rank}\n              </Badge>\n              {aiExposure.level && (\n                <Badge variant=\"outline\" className={`ml-2 ${aiExposure.color}`}>\n                  <aiExposure.icon className=\"mr-1 h-3 w-3\" />\n                  {aiExposure.level} AI\n                </Badge>\n              )}\n            </div>\n            <div className=\"text-sm text-slate-300 ml-3\">{result.name}</div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-xl font-bold text-white\">\n              {result.overallScore.toFixed(1)}/100\n            </div>\n            <div className=\"text-sm text-slate-300\">Confidence</div>\n          </div>\n        </div>\n\n        {/* Strategy Details */}\n        <div className={`mb-4 p-4 bg-${strategyColor}-900/20 rounded border border-${strategyColor}-500/30`}>\n          <div className=\"flex items-center justify-between mb-3\">\n            <div className=\"flex items-center\">\n              <StrategyIcon className={`mr-2 h-4 w-4 text-${strategyColor}-400`} />\n              <span className={`text-${strategyColor}-400 font-semibold`}>\n                {result.overnightSetup ? 'Overnight Momentum Strategy' : 'Technical Breakout Strategy'}\n              </span>\n              <Badge className={`ml-2 bg-${strategyColor}-500/20 text-${strategyColor}-400`}>\n                {setup.confidence.toFixed(1)}% confidence\n              </Badge>\n            </div>\n          </div>\n          \n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm mb-4\">\n            <div>\n              <div className=\"text-slate-300\">Entry</div>\n              <div className=\"text-white font-semibold\">{formatCurrency(setup.entryPrice)}</div>\n            </div>\n            <div>\n              <div className=\"text-slate-300\">Stop</div>\n              <div className=\"text-red-400 font-semibold\">{formatCurrency(setup.stopLoss)}</div>\n            </div>\n            <div>\n              <div className=\"text-slate-300\">Target</div>\n              <div className=\"text-green-400 font-semibold\">{formatCurrency(setup.targets[0])}</div>\n            </div>\n            <div>\n              <div className=\"text-slate-300\">Position Size</div>\n              <div className=\"text-white font-semibold\">{setup.positionSize.toLocaleString()} shares</div>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex gap-3\">\n            {!state.paperTrade ? (\n              <Button\n                onClick={handleExecutePaperTrade}\n                disabled={state.isExecuting}\n                className=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white\"\n              >\n                {state.isExecuting ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Executing...\n                  </>\n                ) : (\n                  <>\n                    <Play className=\"mr-2 h-4 w-4\" />\n                    Execute Paper Trade\n                  </>\n                )}\n              </Button>\n            ) : (\n              <Button disabled className=\"flex-1 bg-green-600 text-white\">\n                <CheckCircle className=\"mr-2 h-4 w-4\" />\n                Trade Executed\n              </Button>\n            )}\n\n            <Button\n              onClick={handleGetAIAnalysis}\n              disabled={state.isAnalyzing}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n            >\n              {state.isAnalyzing ? (\n                <>\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                  Analyzing...\n                </>\n              ) : (\n                <>\n                  <Brain className=\"mr-2 h-4 w-4\" />\n                  AI Analysis\n                </>\n              )}\n            </Button>\n\n            {state.aiAnalysis && (\n              <Button\n                onClick={() => setState(prev => ({ ...prev, showAnalysis: !prev.showAnalysis }))}\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"border-slate-600 text-slate-300\"\n              >\n                {state.showAnalysis ? <ChevronUp className=\"h-4 w-4\" /> : <ChevronDown className=\"h-4 w-4\" />}\n              </Button>\n            )}\n          </div>\n        </div>\n\n        {/* Paper Trade Confirmation */}\n        {state.paperTrade && (\n          <div className=\"mb-4 p-3 bg-green-900/20 rounded border border-green-500/30\">\n            <div className=\"flex items-center mb-2\">\n              <CheckCircle className=\"mr-2 h-4 w-4 text-green-400\" />\n              <span className=\"text-green-400 font-semibold\">Paper Trade Executed</span>\n            </div>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2 text-sm\">\n              <div>\n                <span className=\"text-slate-300\">Position Value: </span>\n                <span className=\"text-white\">{formatCurrency(state.paperTrade.entryPrice * state.paperTrade.positionSize)}</span>\n              </div>\n              <div>\n                <span className=\"text-slate-300\">Risk Amount: </span>\n                <span className=\"text-red-400\">{formatCurrency(state.paperTrade.riskAmount)}</span>\n              </div>\n              <div>\n                <span className=\"text-slate-300\">Risk %: </span>\n                <span className=\"text-yellow-400\">{state.paperTrade.riskPercentage.toFixed(1)}%</span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* AI Analysis Section */}\n        {state.aiAnalysis && state.showAnalysis && (\n          <div className=\"mb-4 p-4 bg-blue-900/20 rounded border border-blue-500/30\">\n            <div className=\"flex items-center mb-3\">\n              <Brain className=\"mr-2 h-4 w-4 text-blue-400\" />\n              <span className=\"text-blue-400 font-semibold\">AI Setup Analysis</span>\n              <Badge className=\"ml-2 bg-blue-500/20 text-blue-400\">\n                {state.aiAnalysis.confidence}% AI Confidence\n              </Badge>\n            </div>\n            \n            <div className=\"space-y-3 text-sm\">\n              <div>\n                <div className=\"text-slate-300 font-medium mb-1\">Setup Explanation:</div>\n                <div className=\"text-white\">{state.aiAnalysis.setupExplanation}</div>\n              </div>\n              \n              {state.aiAnalysis.catalysts && state.aiAnalysis.catalysts.length > 0 && (\n                <div>\n                  <div className=\"text-slate-300 font-medium mb-1\">Market Catalysts:</div>\n                  <ul className=\"text-white list-disc list-inside\">\n                    {state.aiAnalysis.catalysts.map((catalyst, i) => (\n                      <li key={i}>{catalyst}</li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n              \n              {state.aiAnalysis.riskAssessment && (\n                <div>\n                  <div className=\"text-slate-300 font-medium mb-1\">Risk Assessment:</div>\n                  <div className=\"text-white\">{state.aiAnalysis.riskAssessment}</div>\n                </div>\n              )}\n              \n              {state.aiAnalysis.keyLevels && state.aiAnalysis.keyLevels.length > 0 && (\n                <div>\n                  <div className=\"text-slate-300 font-medium mb-1\">Key Levels to Watch:</div>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {state.aiAnalysis.keyLevels.map((level, i) => (\n                      <Badge key={i} className=\"bg-slate-700 text-slate-300\">\n                        {level}\n                      </Badge>\n                    ))}\n                  </div>\n                </div>\n              )}\n              \n              <div className=\"flex justify-between items-center pt-2 border-t border-slate-600\">\n                <div>\n                  <span className=\"text-slate-300\">Expected Timeframe: </span>\n                  <span className=\"text-white\">{state.aiAnalysis.timeframe}</span>\n                </div>\n                {state.aiAnalysis.lastUpdated && (\n                  <div className=\"text-xs text-slate-400\">\n                    Updated: {new Date(state.aiAnalysis.lastUpdated).toLocaleTimeString()}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Alerts and Warnings */}\n        {result.alerts.length > 0 && (\n          <div className=\"mb-2\">\n            <div className=\"text-sm text-slate-300 mb-1\">Trading Alerts:</div>\n            {result.alerts.map((alert, i) => (\n              <div key={i} className=\"text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded mb-1\">\n                {alert}\n              </div>\n            ))}\n          </div>\n        )}\n\n        {result.riskWarnings.length > 0 && (\n          <div>\n            <div className=\"text-sm text-slate-300 mb-1\">Risk Warnings:</div>\n            {result.riskWarnings.map((warning, i) => (\n              <div key={i} className=\"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded mb-1 flex items-center\">\n                <AlertTriangle className=\"mr-1 h-3 w-3\" />\n                {warning}\n              </div>\n            ))}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA;AAbA;;;;;;;;AAqBA,iDAAiD;AACjD,SAAS,mBAAmB,MAAc;IACxC,2CAA2C;IAC3C,MAAM,YAAY;QAAC;QAAQ;QAAQ;QAAQ;QAAS;QAAQ;QAAQ;QAAQ;QAAQ;QAAO;KAAM;IAEjG,qCAAqC;IACrC,MAAM,cAAc;QAAC;QAAQ;QAAM;QAAQ;QAAQ;QAAO;QAAO;QAAM;QAAO;KAAO;IAErF,kCAAkC;IAClC,MAAM,YAAY;QAAC;QAAQ;QAAQ;QAAQ;KAAO;IAElD,4BAA4B;IAC5B,MAAM,aAAa;QACjB;QAAQ;QAAQ;QAAO;QAAM;QAAO;QAAM;QAAK;QAAQ;QAAO;QAC9D;QAAO;QAAO;QAAO;QAAQ;QAAO;QAAQ;QAAQ;QACpD;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAChD;QAAQ;QAAO;QAAQ;QAAQ;QAAO;QAAK;QAC3C;QAAO;QAAO;QAAO;QAAM;QAAM;QAAO;QAAM;QAAO;KACtD;IAED,IAAI,UAAU,QAAQ,CAAC,SAAS;QAC9B,OAAO;YAAE,OAAO;YAAW,MAAM,6OAAK;YAAE,OAAO;QAAwD;IACzG;IACA,IAAI,YAAY,QAAQ,CAAC,SAAS;QAChC,OAAO;YAAE,OAAO;YAAa,MAAM,uOAAG;YAAE,OAAO;QAAkD;IACnG;IACA,IAAI,UAAU,QAAQ,CAAC,SAAS;QAC9B,OAAO;YAAE,OAAO;YAAW,MAAM,uOAAG;YAAE,OAAO;QAAkD;IACjG;IACA,IAAI,WAAW,QAAQ,CAAC,SAAS;QAC/B,OAAO;YAAE,OAAO;YAAY,MAAM,gPAAM;YAAE,OAAO;QAAkD;IACrG;IACA,OAAO;QAAE,OAAO;QAAM,MAAM;QAAM,OAAO;IAAG;AAC9C;AAEO,SAAS,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,oBAAoB,EAAoB;IACzF,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iPAAQ,EAAmB;QACnD,aAAa;QACb,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,QAAQ,OAAO,cAAc,IAAI,OAAO,aAAa;IAC3D,MAAM,eAAe,OAAO,cAAc,GAAG,uBAAuB;IACpE,MAAM,gBAAgB,OAAO,cAAc,GAAG,WAAW;IACzD,MAAM,eAAe,OAAO,cAAc,GAAG,0OAAI,GAAG,gQAAU;IAC9D,MAAM,aAAa,mBAAmB,OAAO,MAAM;IAEnD,MAAM,0BAA0B;QAC9B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAK,CAAC;QAEhD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,YAAY;oBAAQ;gBAAY;YACzD;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;YACjC;YAEA,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG,MAAM,SAAS,IAAI;YAE5D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP;oBACA,aAAa;gBACf,CAAC;YAED,uBAAuB;YAEvB,4BAA4B;YAC5B,MAAM,CAAC,8CAA8C,EAAE,WAAW,MAAM,CAAC,YAAY,EAAE,WAAW,YAAY,CAAC,gBAAgB,EAAE,IAAA,qKAAc,EAAC,WAAW,UAAU,EAAE,QAAQ,EAAE,IAAA,qKAAc,EAAC,WAAW,UAAU,GAAG;QAE1N,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,CAAC,+BAA+B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YAClG,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,aAAa;gBAAM,CAAC;QACnD;IACF;IAEA,MAAM,sBAAsB;QAC1B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAK,CAAC;QAEhD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,YAAY;gBAAO;YAC5C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,aAAyB,MAAM,SAAS,IAAI;YAElD,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP;oBACA,aAAa;oBACb,cAAc;gBAChB,CAAC;QAEH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;YACN,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,aAAa;gBAAM,CAAC;QACnD;IACF;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,qBACE,8QAAC,wKAAI;QAAC,WAAU;kBACd,cAAA,8QAAC,+KAAW;YAAC,WAAU;;8BAErB,8QAAC;oBAAI,WAAU;;sCACb,8QAAC;4BAAI,WAAU;;8CACb,8QAAC;oCAAI,WAAU;;sDACb,8QAAC;4CAAK,WAAU;sDAAsC,OAAO,MAAM;;;;;;sDACnE,8QAAC,0KAAK;4CAAC,WAAW,GAAG,kBAAkB,WAAW,qCAAqC,kCAAkC;;gDAAE;gDACvH,OAAO,IAAI;;;;;;;wCAEd,WAAW,KAAK,kBACf,8QAAC,0KAAK;4CAAC,SAAQ;4CAAU,WAAW,CAAC,KAAK,EAAE,WAAW,KAAK,EAAE;;8DAC5D,8QAAC,WAAW,IAAI;oDAAC,WAAU;;;;;;gDAC1B,WAAW,KAAK;gDAAC;;;;;;;;;;;;;8CAIxB,8QAAC;oCAAI,WAAU;8CAA+B,OAAO,IAAI;;;;;;;;;;;;sCAE3D,8QAAC;4BAAI,WAAU;;8CACb,8QAAC;oCAAI,WAAU;;wCACZ,OAAO,YAAY,CAAC,OAAO,CAAC;wCAAG;;;;;;;8CAElC,8QAAC;oCAAI,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;8BAK5C,8QAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,8BAA8B,EAAE,cAAc,OAAO,CAAC;;sCACjG,8QAAC;4BAAI,WAAU;sCACb,cAAA,8QAAC;gCAAI,WAAU;;kDACb,8QAAC;wCAAa,WAAW,CAAC,kBAAkB,EAAE,cAAc,IAAI,CAAC;;;;;;kDACjE,8QAAC;wCAAK,WAAW,CAAC,KAAK,EAAE,cAAc,kBAAkB,CAAC;kDACvD,OAAO,cAAc,GAAG,gCAAgC;;;;;;kDAE3D,8QAAC,0KAAK;wCAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,aAAa,EAAE,cAAc,IAAI,CAAC;;4CAC1E,MAAM,UAAU,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;sCAKnC,8QAAC;4BAAI,WAAU;;8CACb,8QAAC;;sDACC,8QAAC;4CAAI,WAAU;sDAAiB;;;;;;sDAChC,8QAAC;4CAAI,WAAU;sDAA4B,IAAA,qKAAc,EAAC,MAAM,UAAU;;;;;;;;;;;;8CAE5E,8QAAC;;sDACC,8QAAC;4CAAI,WAAU;sDAAiB;;;;;;sDAChC,8QAAC;4CAAI,WAAU;sDAA8B,IAAA,qKAAc,EAAC,MAAM,QAAQ;;;;;;;;;;;;8CAE5E,8QAAC;;sDACC,8QAAC;4CAAI,WAAU;sDAAiB;;;;;;sDAChC,8QAAC;4CAAI,WAAU;sDAAgC,IAAA,qKAAc,EAAC,MAAM,OAAO,CAAC,EAAE;;;;;;;;;;;;8CAEhF,8QAAC;;sDACC,8QAAC;4CAAI,WAAU;sDAAiB;;;;;;sDAChC,8QAAC;4CAAI,WAAU;;gDAA4B,MAAM,YAAY,CAAC,cAAc;gDAAG;;;;;;;;;;;;;;;;;;;sCAKnF,8QAAC;4BAAI,WAAU;;gCACZ,CAAC,MAAM,UAAU,iBAChB,8QAAC,4KAAM;oCACL,SAAS;oCACT,UAAU,MAAM,WAAW;oCAC3B,WAAU;8CAET,MAAM,WAAW,iBAChB;;0DACE,8QAAC,4PAAO;gDAAC,WAAU;;;;;;4CAA8B;;qEAInD;;0DACE,8QAAC,0OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;yDAMvC,8QAAC,4KAAM;oCAAC,QAAQ;oCAAC,WAAU;;sDACzB,8QAAC,0QAAW;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAK5C,8QAAC,4KAAM;oCACL,SAAS;oCACT,UAAU,MAAM,WAAW;oCAC3B,SAAQ;oCACR,WAAU;8CAET,MAAM,WAAW,iBAChB;;0DACE,8QAAC,4PAAO;gDAAC,WAAU;;;;;;4CAA8B;;qEAInD;;0DACE,8QAAC,6OAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;gCAMvC,MAAM,UAAU,kBACf,8QAAC,4KAAM;oCACL,SAAS,IAAM,SAAS,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc,CAAC,KAAK,YAAY;4CAAC,CAAC;oCAC9E,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAET,MAAM,YAAY,iBAAG,8QAAC,6PAAS;wCAAC,WAAU;;;;;6DAAe,8QAAC,mQAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOxF,MAAM,UAAU,kBACf,8QAAC;oBAAI,WAAU;;sCACb,8QAAC;4BAAI,WAAU;;8CACb,8QAAC,0QAAW;oCAAC,WAAU;;;;;;8CACvB,8QAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;;sCAEjD,8QAAC;4BAAI,WAAU;;8CACb,8QAAC;;sDACC,8QAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,8QAAC;4CAAK,WAAU;sDAAc,IAAA,qKAAc,EAAC,MAAM,UAAU,CAAC,UAAU,GAAG,MAAM,UAAU,CAAC,YAAY;;;;;;;;;;;;8CAE1G,8QAAC;;sDACC,8QAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,8QAAC;4CAAK,WAAU;sDAAgB,IAAA,qKAAc,EAAC,MAAM,UAAU,CAAC,UAAU;;;;;;;;;;;;8CAE5E,8QAAC;;sDACC,8QAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,8QAAC;4CAAK,WAAU;;gDAAmB,MAAM,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;gBAOrF,MAAM,UAAU,IAAI,MAAM,YAAY,kBACrC,8QAAC;oBAAI,WAAU;;sCACb,8QAAC;4BAAI,WAAU;;8CACb,8QAAC,6OAAK;oCAAC,WAAU;;;;;;8CACjB,8QAAC;oCAAK,WAAU;8CAA8B;;;;;;8CAC9C,8QAAC,0KAAK;oCAAC,WAAU;;wCACd,MAAM,UAAU,CAAC,UAAU;wCAAC;;;;;;;;;;;;;sCAIjC,8QAAC;4BAAI,WAAU;;8CACb,8QAAC;;sDACC,8QAAC;4CAAI,WAAU;sDAAkC;;;;;;sDACjD,8QAAC;4CAAI,WAAU;sDAAc,MAAM,UAAU,CAAC,gBAAgB;;;;;;;;;;;;gCAG/D,MAAM,UAAU,CAAC,SAAS,IAAI,MAAM,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,mBACjE,8QAAC;;sDACC,8QAAC;4CAAI,WAAU;sDAAkC;;;;;;sDACjD,8QAAC;4CAAG,WAAU;sDACX,MAAM,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,kBACzC,8QAAC;8DAAY;mDAAJ;;;;;;;;;;;;;;;;gCAMhB,MAAM,UAAU,CAAC,cAAc,kBAC9B,8QAAC;;sDACC,8QAAC;4CAAI,WAAU;sDAAkC;;;;;;sDACjD,8QAAC;4CAAI,WAAU;sDAAc,MAAM,UAAU,CAAC,cAAc;;;;;;;;;;;;gCAI/D,MAAM,UAAU,CAAC,SAAS,IAAI,MAAM,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,mBACjE,8QAAC;;sDACC,8QAAC;4CAAI,WAAU;sDAAkC;;;;;;sDACjD,8QAAC;4CAAI,WAAU;sDACZ,MAAM,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,kBACtC,8QAAC,0KAAK;oDAAS,WAAU;8DACtB;mDADS;;;;;;;;;;;;;;;;8CAQpB,8QAAC;oCAAI,WAAU;;sDACb,8QAAC;;8DACC,8QAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8QAAC;oDAAK,WAAU;8DAAc,MAAM,UAAU,CAAC,SAAS;;;;;;;;;;;;wCAEzD,MAAM,UAAU,CAAC,WAAW,kBAC3B,8QAAC;4CAAI,WAAU;;gDAAyB;gDAC5B,IAAI,KAAK,MAAM,UAAU,CAAC,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;gBAS9E,OAAO,MAAM,CAAC,MAAM,GAAG,mBACtB,8QAAC;oBAAI,WAAU;;sCACb,8QAAC;4BAAI,WAAU;sCAA8B;;;;;;wBAC5C,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,QAAO,kBACzB,8QAAC;gCAAY,WAAU;0CACpB;+BADO;;;;;;;;;;;gBAOf,OAAO,YAAY,CAAC,MAAM,GAAG,mBAC5B,8QAAC;;sCACC,8QAAC;4BAAI,WAAU;sCAA8B;;;;;;wBAC5C,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,SAAS,kBACjC,8QAAC;gCAAY,WAAU;;kDACrB,8QAAC,yQAAa;wCAAC,WAAU;;;;;;oCACxB;;+BAFO;;;;;;;;;;;;;;;;;;;;;;AAUxB", "debugId": null}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/components/StrategyScanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, Zap, Target, Clock, AlertTriangle, TrendingUp, Moon, BarChart3, Briefcase, Brain } from 'lucide-react'\nimport { EnhancedScanResult, StrategyScanSummary } from '@/lib/enhancedSwingScanner'\nimport { PaperTrade } from '@/types/paperTrading'\nimport { TradingCard } from '@/components/TradingCard'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface StrategyScannerProps {\n  autoScan?: boolean\n  accountSize?: number\n}\n\nexport function StrategyScanner({ autoScan = false, accountSize = 100000 }: StrategyScannerProps) {\n  const [isScanning, setIsScanning] = useState(false)\n  const [scanResults, setScanResults] = useState<StrategyScanSummary | null>(null)\n  const [selectedStrategy, setSelectedStrategy] = useState<'both' | 'overnight' | 'breakout'>('both')\n  const [error, setError] = useState<string | null>(null)\n  const [userAccountSize, setUserAccountSize] = useState(accountSize)\n  const [paperTrades, setPaperTrades] = useState<PaperTrade[]>([])\n  const [showPaperTrades, setShowPaperTrades] = useState(false)\n\n  // Auto-scan on component mount if enabled\n  useEffect(() => {\n    if (autoScan) {\n      handleStrategyScan('quick')\n    }\n  }, [autoScan])\n\n  const handleStrategyScan = async (scanType: 'quick' | 'full' | 'test') => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      let response\n      if (scanType === 'test') {\n        response = await fetch('/api/scanner/test')\n      } else {\n        response = await fetch(\n          `/api/scanner/strategies?type=${scanType}&accountSize=${userAccountSize}&limit=20`\n        )\n      }\n\n      if (!response.ok) throw new Error('Failed to fetch strategy scan results')\n\n      const data = await response.json()\n      console.log('📊 API Response:', data) // Debug logging\n      console.log('📊 topSetups:', data?.topSetups) // Debug logging\n      console.log('📊 Setting scan results...') // Debug logging\n      setScanResults(data)\n      console.log('📊 Scan results set!') // Debug logging\n    } catch (err) {\n      setError('Failed to perform strategy scan. Please try again.')\n      console.error('Strategy scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const getStrategyIcon = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return <Moon className=\"h-4 w-4 text-purple-400\" />\n      case 'technical_breakout':\n        return <TrendingUp className=\"h-4 w-4 text-green-400\" />\n      default:\n        return <BarChart3 className=\"h-4 w-4 text-blue-400\" />\n    }\n  }\n\n  const getStrategyName = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return 'Overnight Momentum'\n      case 'technical_breakout':\n        return 'Technical Breakout'\n      default:\n        return 'Mixed Strategy'\n    }\n  }\n\n  const getStrategyColor = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return 'bg-purple-500/20 text-purple-400'\n      case 'technical_breakout':\n        return 'bg-green-500/20 text-green-400'\n      default:\n        return 'bg-blue-500/20 text-blue-400'\n    }\n  }\n\n  const filteredResults = scanResults?.topSetups?.filter(result => {\n    if (selectedStrategy === 'both') return true\n    if (selectedStrategy === 'overnight') return result.overnightSetup\n    if (selectedStrategy === 'breakout') return result.breakoutSetup\n    return true\n  }) || []\n\n  const handlePaperTradeExecuted = (trade: PaperTrade) => {\n    setPaperTrades(prev => [...prev, trade])\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Strategy Scanner Controls */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center\">\n            <Zap className=\"mr-2 h-5 w-5 text-yellow-400\" />\n            Professional Swing Trading Strategies\n          </CardTitle>\n          <CardDescription className=\"text-slate-300\">\n            Automated scanner implementing proven swing trading methodologies with precise entry/exit rules\n            <br />\n            <span className=\"text-xs text-blue-400\">\n              🚀 New: AI & Tech Giants scan - 60+ AI, tech, fintech, and high-growth stocks\n            </span>\n            <br />\n            <span className=\"text-xs text-yellow-400\">\n              Status: {isScanning ? 'Scanning...' : scanResults ? `Found ${scanResults.totalScanned} stocks, ${scanResults.overnightSetups} setups` : 'Ready to scan'}\n            </span>\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {/* Account Size Input */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm text-slate-300 mb-2\">Account Size (for position sizing)</label>\n            <input\n              type=\"number\"\n              value={userAccountSize}\n              onChange={(e) => setUserAccountSize(parseInt(e.target.value) || 100000)}\n              className=\"w-32 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n              min=\"10000\"\n              step=\"10000\"\n              disabled={isScanning}\n            />\n          </div>\n\n          {/* Strategy Filter */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm text-slate-300 mb-2\">Strategy Filter</label>\n            <div className=\"flex gap-2\">\n              <Button\n                variant={selectedStrategy === 'both' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('both')}\n                className={selectedStrategy === 'both' ? 'bg-blue-600' : 'border-slate-600 text-slate-300'}\n              >\n                All Strategies\n              </Button>\n              <Button\n                variant={selectedStrategy === 'overnight' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('overnight')}\n                className={selectedStrategy === 'overnight' ? 'bg-purple-600' : 'border-slate-600 text-slate-300'}\n              >\n                <Moon className=\"mr-1 h-3 w-3\" />\n                Overnight\n              </Button>\n              <Button\n                variant={selectedStrategy === 'breakout' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('breakout')}\n                className={selectedStrategy === 'breakout' ? 'bg-green-600' : 'border-slate-600 text-slate-300'}\n              >\n                <TrendingUp className=\"mr-1 h-3 w-3\" />\n                Breakout\n              </Button>\n            </div>\n          </div>\n\n          {/* Scan Buttons */}\n          <div className=\"flex flex-wrap gap-4 mb-4\">\n            <Button\n              onClick={() => handleStrategyScan('quick')}\n              disabled={isScanning}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              {isScanning ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : (\n                <Zap className=\"mr-2 h-4 w-4\" />\n              )}\n              Quick Strategy Scan\n            </Button>\n            \n            <Button\n              onClick={() => handleStrategyScan('full')}\n              disabled={isScanning}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n            >\n              {isScanning ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : (\n                <Target className=\"mr-2 h-4 w-4\" />\n              )}\n              Full Strategy Scan\n            </Button>\n\n            <Button\n              onClick={() => handleStrategyScan('ai-tech')}\n              disabled={isScanning}\n              className=\"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700\"\n            >\n              {isScanning ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : (\n                <Brain className=\"mr-2 h-4 w-4\" />\n              )}\n              AI & Tech Giants\n            </Button>\n          </div>\n\n          {/* Market Conditions */}\n          {scanResults?.marketConditions && (\n            <div className=\"p-3 bg-slate-700/50 rounded-lg\">\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-slate-300\">Market Conditions:</span>\n                <div className=\"flex items-center gap-4\">\n                  <span className=\"text-white\">{scanResults.marketConditions.timeOfDay}</span>\n                  <Badge className={scanResults.marketConditions.isOptimalScanTime \n                    ? 'bg-green-500/20 text-green-400' \n                    : 'bg-yellow-500/20 text-yellow-400'\n                  }>\n                    {scanResults.marketConditions.isOptimalScanTime ? 'Optimal Scan Time' : 'Outside Optimal Hours'}\n                  </Badge>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {isScanning && (\n            <div className=\"text-center py-4\">\n              <Loader2 className=\"mx-auto h-8 w-8 animate-spin text-blue-400\" />\n              <p className=\"text-slate-300 mt-2\">\n                Analyzing stocks for professional swing trading setups...\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Error Display */}\n      {error && (\n        <Card className=\"bg-red-900/20 border-red-500/50\">\n          <CardContent className=\"p-6\">\n            <p className=\"text-red-300 text-center\">{error}</p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Paper Trades Summary */}\n      {paperTrades.length > 0 && (\n        <Card className=\"bg-slate-800/50 border-slate-700\">\n          <CardHeader>\n            <CardTitle className=\"text-white flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <Briefcase className=\"mr-2 h-5 w-5 text-green-400\" />\n                Paper Trading Portfolio\n              </div>\n              <Button\n                onClick={() => setShowPaperTrades(!showPaperTrades)}\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"border-slate-600 text-slate-300\"\n              >\n                {showPaperTrades ? 'Hide' : 'Show'} ({paperTrades.length})\n              </Button>\n            </CardTitle>\n          </CardHeader>\n          {showPaperTrades && (\n            <CardContent>\n              <div className=\"space-y-3\">\n                {paperTrades.map((trade, index) => (\n                  <div key={trade.id} className=\"p-3 bg-slate-700/50 rounded border border-slate-600\">\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <div className=\"flex items-center\">\n                        <span className=\"font-semibold text-white\">{trade.symbol}</span>\n                        <Badge className=\"ml-2 bg-blue-500/20 text-blue-400\">\n                          {trade.strategy.replace('_', ' ')}\n                        </Badge>\n                      </div>\n                      <div className=\"text-sm text-slate-300\">\n                        {new Date(trade.entryTime).toLocaleDateString()}\n                      </div>\n                    </div>\n                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2 text-sm\">\n                      <div>\n                        <span className=\"text-slate-300\">Position: </span>\n                        <span className=\"text-white\">{trade.positionSize} shares</span>\n                      </div>\n                      <div>\n                        <span className=\"text-slate-300\">Entry: </span>\n                        <span className=\"text-white\">{formatCurrency(trade.entryPrice)}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-slate-300\">Risk: </span>\n                        <span className=\"text-red-400\">{formatCurrency(trade.riskAmount)}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-slate-300\">Status: </span>\n                        <Badge className={trade.status === 'open' ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'}>\n                          {trade.status}\n                        </Badge>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          )}\n        </Card>\n      )}\n\n      {/* Strategy Scan Results */}\n      {scanResults && (\n        <div className=\"space-y-6\">\n          {console.log('📊 Rendering results:', scanResults)}\n          {/* Scan Summary */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white\">Strategy Scan Summary</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-white\">{scanResults.totalScanned || 0}</div>\n                  <div className=\"text-sm text-slate-300\">Stocks Scanned</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-400\">{scanResults.overnightSetups || 0}</div>\n                  <div className=\"text-sm text-slate-300\">Overnight Setups</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">{scanResults.breakoutSetups || 0}</div>\n                  <div className=\"text-sm text-slate-300\">Breakout Setups</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">{scanResults.bothStrategies || 0}</div>\n                  <div className=\"text-sm text-slate-300\">Both Strategies</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Enhanced Trading Cards */}\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-xl font-bold text-white flex items-center\">\n                {scanResults?.category === 'AI & Tech Giants' ? (\n                  <Brain className=\"mr-2 h-5 w-5 text-purple-400\" />\n                ) : (\n                  <Target className=\"mr-2 h-5 w-5 text-green-400\" />\n                )}\n                {scanResults?.category === 'AI & Tech Giants' ? 'AI & Tech Giants' : 'Professional Trading Setups'} ({filteredResults.length})\n              </h2>\n              {paperTrades.length > 0 && (\n                <Badge className=\"bg-green-500/20 text-green-400\">\n                  {paperTrades.length} Paper Trades Active\n                </Badge>\n              )}\n            </div>\n\n            <div className=\"space-y-4\">\n              {filteredResults.map((result) => (\n                <TradingCard\n                  key={result.symbol}\n                  result={result}\n                  accountSize={userAccountSize}\n                  onPaperTradeExecuted={handlePaperTradeExecuted}\n                />\n              ))}\n            </div>\n          </div>\n\n\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AAVA;;;;;;;;;AAiBO,SAAS,gBAAgB,EAAE,WAAW,KAAK,EAAE,cAAc,MAAM,EAAwB;IAC9F,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iPAAQ,EAAC;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iPAAQ,EAA6B;IAC3E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,iPAAQ,EAAoC;IAC5F,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iPAAQ,EAAgB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iPAAQ,EAAC;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iPAAQ,EAAe,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iPAAQ,EAAC;IAEvD,0CAA0C;IAC1C,IAAA,kPAAS,EAAC;QACR,IAAI,UAAU;YACZ,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,qBAAqB,OAAO;QAChC,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,IAAI;YACJ,IAAI,aAAa,QAAQ;gBACvB,WAAW,MAAM,MAAM;YACzB,OAAO;gBACL,WAAW,MAAM,MACf,CAAC,6BAA6B,EAAE,SAAS,aAAa,EAAE,gBAAgB,SAAS,CAAC;YAEtF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,oBAAoB,OAAM,gBAAgB;YACtD,QAAQ,GAAG,CAAC,iBAAiB,MAAM,YAAW,gBAAgB;YAC9D,QAAQ,GAAG,CAAC,+BAA8B,gBAAgB;YAC1D,eAAe;YACf,QAAQ,GAAG,CAAC,yBAAwB,gBAAgB;QACtD,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,8QAAC,0OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8QAAC,gQAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,8QAAC,+PAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,aAAa,WAAW,OAAO,CAAA;QACrD,IAAI,qBAAqB,QAAQ,OAAO;QACxC,IAAI,qBAAqB,aAAa,OAAO,OAAO,cAAc;QAClE,IAAI,qBAAqB,YAAY,OAAO,OAAO,aAAa;QAChE,OAAO;IACT,MAAM,EAAE;IAER,MAAM,2BAA2B,CAAC;QAChC,eAAe,CAAA,OAAQ;mBAAI;gBAAM;aAAM;IACzC;IAEA,qBACE,8QAAC;QAAI,WAAU;;0BAEb,8QAAC,wKAAI;gBAAC,WAAU;;kCACd,8QAAC,8KAAU;;0CACT,8QAAC,6KAAS;gCAAC,WAAU;;kDACnB,8QAAC,uOAAG;wCAAC,WAAU;;;;;;oCAAiC;;;;;;;0CAGlD,8QAAC,mLAAe;gCAAC,WAAU;;oCAAiB;kDAE1C,8QAAC;;;;;kDACD,8QAAC;wCAAK,WAAU;kDAAwB;;;;;;kDAGxC,8QAAC;;;;;kDACD,8QAAC;wCAAK,WAAU;;4CAA0B;4CAC/B,aAAa,gBAAgB,cAAc,CAAC,MAAM,EAAE,YAAY,YAAY,CAAC,SAAS,EAAE,YAAY,eAAe,CAAC,OAAO,CAAC,GAAG;;;;;;;;;;;;;;;;;;;kCAI9I,8QAAC,+KAAW;;0CAEV,8QAAC;gCAAI,WAAU;;kDACb,8QAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8QAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wCAChE,WAAU;wCACV,KAAI;wCACJ,MAAK;wCACL,UAAU;;;;;;;;;;;;0CAKd,8QAAC;gCAAI,WAAU;;kDACb,8QAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8QAAC;wCAAI,WAAU;;0DACb,8QAAC,4KAAM;gDACL,SAAS,qBAAqB,SAAS,YAAY;gDACnD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,SAAS,gBAAgB;0DAC1D;;;;;;0DAGD,8QAAC,4KAAM;gDACL,SAAS,qBAAqB,cAAc,YAAY;gDACxD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,cAAc,kBAAkB;;kEAEhE,8QAAC,0OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,8QAAC,4KAAM;gDACL,SAAS,qBAAqB,aAAa,YAAY;gDACvD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,aAAa,iBAAiB;;kEAE9D,8QAAC,gQAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAO7C,8QAAC;gCAAI,WAAU;;kDACb,8QAAC,4KAAM;wCACL,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,WAAU;;4CAET,2BACC,8QAAC,4PAAO;gDAAC,WAAU;;;;;qEAEnB,8QAAC,uOAAG;gDAAC,WAAU;;;;;;4CACf;;;;;;;kDAIJ,8QAAC,4KAAM;wCACL,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,SAAQ;wCACR,WAAU;;4CAET,2BACC,8QAAC,4PAAO;gDAAC,WAAU;;;;;qEAEnB,8QAAC,gPAAM;gDAAC,WAAU;;;;;;4CAClB;;;;;;;kDAIJ,8QAAC,4KAAM;wCACL,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,WAAU;;4CAET,2BACC,8QAAC,4PAAO;gDAAC,WAAU;;;;;qEAEnB,8QAAC,6OAAK;gDAAC,WAAU;;;;;;4CACjB;;;;;;;;;;;;;4BAML,aAAa,kCACZ,8QAAC;gCAAI,WAAU;0CACb,cAAA,8QAAC;oCAAI,WAAU;;sDACb,8QAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAK,WAAU;8DAAc,YAAY,gBAAgB,CAAC,SAAS;;;;;;8DACpE,8QAAC,0KAAK;oDAAC,WAAW,YAAY,gBAAgB,CAAC,iBAAiB,GAC5D,mCACA;8DAED,YAAY,gBAAgB,CAAC,iBAAiB,GAAG,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;4BAOjF,4BACC,8QAAC;gCAAI,WAAU;;kDACb,8QAAC,4PAAO;wCAAC,WAAU;;;;;;kDACnB,8QAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,uBACC,8QAAC,wKAAI;gBAAC,WAAU;0BACd,cAAA,8QAAC,+KAAW;oBAAC,WAAU;8BACrB,cAAA,8QAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;YAM9C,YAAY,MAAM,GAAG,mBACpB,8QAAC,wKAAI;gBAAC,WAAU;;kCACd,8QAAC,8KAAU;kCACT,cAAA,8QAAC,6KAAS;4BAAC,WAAU;;8CACnB,8QAAC;oCAAI,WAAU;;sDACb,8QAAC,yPAAS;4CAAC,WAAU;;;;;;wCAAgC;;;;;;;8CAGvD,8QAAC,4KAAM;oCACL,SAAS,IAAM,mBAAmB,CAAC;oCACnC,SAAQ;oCACR,MAAK;oCACL,WAAU;;wCAET,kBAAkB,SAAS;wCAAO;wCAAG,YAAY,MAAM;wCAAC;;;;;;;;;;;;;;;;;;oBAI9D,iCACC,8QAAC,+KAAW;kCACV,cAAA,8QAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8QAAC;oCAAmB,WAAU;;sDAC5B,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;;sEACb,8QAAC;4DAAK,WAAU;sEAA4B,MAAM,MAAM;;;;;;sEACxD,8QAAC,0KAAK;4DAAC,WAAU;sEACd,MAAM,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;8DAGjC,8QAAC;oDAAI,WAAU;8DACZ,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;;;;;;;sDAGjD,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;;sEACC,8QAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,8QAAC;4DAAK,WAAU;;gEAAc,MAAM,YAAY;gEAAC;;;;;;;;;;;;;8DAEnD,8QAAC;;sEACC,8QAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,8QAAC;4DAAK,WAAU;sEAAc,IAAA,qKAAc,EAAC,MAAM,UAAU;;;;;;;;;;;;8DAE/D,8QAAC;;sEACC,8QAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,8QAAC;4DAAK,WAAU;sEAAgB,IAAA,qKAAc,EAAC,MAAM,UAAU;;;;;;;;;;;;8DAEjE,8QAAC;;sEACC,8QAAC;4DAAK,WAAU;sEAAiB;;;;;;sEACjC,8QAAC,0KAAK;4DAAC,WAAW,MAAM,MAAM,KAAK,SAAS,mCAAmC;sEAC5E,MAAM,MAAM;;;;;;;;;;;;;;;;;;;mCA5BX,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;YAyC7B,6BACC,8QAAC;gBAAI,WAAU;;oBACZ,QAAQ,GAAG,CAAC,yBAAyB;kCAEtC,8QAAC,wKAAI;wBAAC,WAAU;;0CACd,8QAAC,8KAAU;0CACT,cAAA,8QAAC,6KAAS;oCAAC,WAAU;8CAAa;;;;;;;;;;;0CAEpC,8QAAC,+KAAW;0CACV,cAAA,8QAAC;oCAAI,WAAU;;sDACb,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAiC,YAAY,YAAY,IAAI;;;;;;8DAC5E,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAsC,YAAY,eAAe,IAAI;;;;;;8DACpF,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAqC,YAAY,cAAc,IAAI;;;;;;8DAClF,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8QAAC;4CAAI,WAAU;;8DACb,8QAAC;oDAAI,WAAU;8DAAoC,YAAY,cAAc,IAAI;;;;;;8DACjF,8QAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhD,8QAAC;wBAAI,WAAU;;0CACb,8QAAC;gCAAI,WAAU;;kDACb,8QAAC;wCAAG,WAAU;;4CACX,aAAa,aAAa,mCACzB,8QAAC,6OAAK;gDAAC,WAAU;;;;;qEAEjB,8QAAC,gPAAM;gDAAC,WAAU;;;;;;4CAEnB,aAAa,aAAa,qBAAqB,qBAAqB;4CAA8B;4CAAG,gBAAgB,MAAM;4CAAC;;;;;;;oCAE9H,YAAY,MAAM,GAAG,mBACpB,8QAAC,0KAAK;wCAAC,WAAU;;4CACd,YAAY,MAAM;4CAAC;;;;;;;;;;;;;0CAK1B,8QAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8QAAC,gLAAW;wCAEV,QAAQ;wCACR,aAAa;wCACb,sBAAsB;uCAHjB,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcpC", "debugId": null}}, {"offset": {"line": 2968, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON><PERSON>ding<PERSON>p, BarChart3, Target, Shield, Brain, Zap, Search, Loader2, Scan } from 'lucide-react'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { SwingTradingAnalysis, StockData } from '@/types/trading'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\nimport { SwingScanner } from '@/components/SwingScanner'\nimport { StrategyScanner } from '@/components/StrategyScanner'\n\nexport default function Home() {\n  const [selectedSymbol, setSelectedSymbol] = useState('SPY')\n  const [customSymbol, setCustomSymbol] = useState('')\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [analysis, setAnalysis] = useState<SwingTradingAnalysis | null>(null)\n  const [stockData, setStockData] = useState<StockData | null>(null)\n  const [error, setError] = useState<string | null>(null)\n  const [activeTab, setActiveTab] = useState<'individual' | 'scanner' | 'strategies'>('strategies')\n\n  const popularSymbols = ['SPY', 'QQQ', 'AAPL', 'TSLA', 'NVDA', 'MSFT', 'AMZN', 'GOOGL']\n\n  const handleAnalysis = async (symbol: string) => {\n    setIsAnalyzing(true)\n    setError(null)\n    setAnalysis(null)\n    setStockData(null)\n\n    try {\n      // Fetch stock quote and analysis in parallel\n      const [quoteResponse, analysisResponse] = await Promise.all([\n        fetch(`/api/stocks/quote/${symbol}`),\n        fetch(`/api/analysis/swing/${symbol}`)\n      ])\n\n      if (!quoteResponse.ok) {\n        const quoteError = await quoteResponse.text()\n        console.error('Quote API error:', quoteError)\n        throw new Error(`Failed to fetch quote data: ${quoteResponse.status}`)\n      }\n\n      if (!analysisResponse.ok) {\n        const analysisError = await analysisResponse.text()\n        console.error('Analysis API error:', analysisError)\n        throw new Error(`Failed to fetch analysis data: ${analysisResponse.status}`)\n      }\n\n      const [quoteData, analysisData] = await Promise.all([\n        quoteResponse.json(),\n        analysisResponse.json()\n      ])\n\n      setStockData(quoteData)\n      setAnalysis(analysisData)\n    } catch (err: any) {\n      const errorMessage = err.message?.includes('rate limit') || err.message?.includes('429')\n        ? 'API rate limit reached. Please wait a moment and try again.'\n        : 'Failed to analyze stock. Please try again.'\n      setError(errorMessage)\n      console.error('Analysis error:', err)\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  const handleCustomSymbolSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (customSymbol.trim()) {\n      handleAnalysis(customSymbol.toUpperCase())\n      setSelectedSymbol(customSymbol.toUpperCase())\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900\">\n      {/* Header */}\n      <header className=\"border-b border-slate-800 bg-slate-900/50 backdrop-blur-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <Brain className=\"h-8 w-8 text-blue-400\" />\n              <h1 className=\"text-2xl font-bold text-white\">SwingTrader AI</h1>\n            </div>\n            <nav className=\"hidden md:flex items-center space-x-6\">\n              <button\n                onClick={() => setActiveTab('strategies')}\n                className={`transition-colors ${activeTab === 'strategies' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Pro Strategies\n              </button>\n              <button\n                onClick={() => setActiveTab('scanner')}\n                className={`transition-colors ${activeTab === 'scanner' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Basic Scanner\n              </button>\n              <button\n                onClick={() => setActiveTab('individual')}\n                className={`transition-colors ${activeTab === 'individual' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Individual Analysis\n              </button>\n              <Button variant=\"outline\" className=\"border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white\">\n                Sign In\n              </Button>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20 px-4\">\n        <div className=\"container mx-auto text-center\">\n          <h2 className=\"text-5xl font-bold text-white mb-6\">\n            AI-Powered Swing Trading Analysis\n          </h2>\n          <p className=\"text-xl text-slate-300 mb-8 max-w-3xl mx-auto\">\n            Professional swing trading strategies with automated scanning, precise entry/exit rules,\n            and risk management based on proven methodologies.\n          </p>\n\n          {/* Tab Navigation */}\n          <div className=\"flex justify-center mb-8\">\n            <div className=\"bg-slate-800/50 rounded-lg p-1 flex\">\n              <button\n                onClick={() => setActiveTab('strategies')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'strategies'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Zap className=\"inline mr-2 h-4 w-4\" />\n                Pro Strategies\n              </button>\n              <button\n                onClick={() => setActiveTab('scanner')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'scanner'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Scan className=\"inline mr-2 h-4 w-4\" />\n                Basic Scanner\n              </button>\n              <button\n                onClick={() => setActiveTab('individual')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'individual'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Search className=\"inline mr-2 h-4 w-4\" />\n                Individual Analysis\n              </button>\n            </div>\n          </div>\n\n          {/* Content based on active tab */}\n          {activeTab === 'strategies' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Professional Swing Trading Strategies</h3>\n              <p className=\"text-slate-400 mb-6\">\n                Overnight Momentum & Technical Breakout strategies with precise entry/exit rules and position sizing\n              </p>\n            </div>\n          ) : activeTab === 'scanner' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Basic Swing Trading Scanner</h3>\n              <p className=\"text-slate-400 mb-6\">\n                General swing trading analysis with technical indicators and trend detection\n              </p>\n            </div>\n          ) : (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Individual Stock Analysis</h3>\n              <div className=\"flex flex-wrap justify-center gap-2 mb-6\">\n                {popularSymbols.map((symbol) => (\n                  <Button\n                    key={symbol}\n                    variant={selectedSymbol === symbol ? \"default\" : \"outline\"}\n                    onClick={() => {\n                      setSelectedSymbol(symbol)\n                      handleAnalysis(symbol)\n                    }}\n                    disabled={isAnalyzing}\n                    className={selectedSymbol === symbol\n                      ? \"bg-blue-600 hover:bg-blue-700\"\n                      : \"border-slate-600 text-slate-300 hover:bg-slate-800\"\n                    }\n                  >\n                    {symbol}\n                  </Button>\n                ))}\n              </div>\n\n              {/* Custom Symbol Input */}\n              <form onSubmit={handleCustomSymbolSubmit} className=\"flex justify-center gap-2 mb-6\">\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Enter symbol (e.g., AAPL)\"\n                    value={customSymbol}\n                    onChange={(e) => setCustomSymbol(e.target.value)}\n                    className=\"pl-10 pr-4 py-2 bg-slate-800 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    disabled={isAnalyzing}\n                  />\n                </div>\n                <Button\n                  type=\"submit\"\n                  disabled={isAnalyzing || !customSymbol.trim()}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  Analyze\n                </Button>\n              </form>\n\n              <Button\n                size=\"lg\"\n                onClick={() => handleAnalysis(selectedSymbol)}\n                disabled={isAnalyzing}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3\"\n              >\n                {isAnalyzing ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\n                    Analyzing {selectedSymbol}...\n                  </>\n                ) : (\n                  <>\n                    <Zap className=\"mr-2 h-5 w-5\" />\n                    Get AI Analysis for {selectedSymbol}\n                  </>\n                )}\n              </Button>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* Strategy Scanner Section */}\n      {activeTab === 'strategies' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <StrategyScanner autoScan={true} accountSize={100000} />\n          </div>\n        </section>\n      )}\n\n      {/* Basic Scanner Section */}\n      {activeTab === 'scanner' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <SwingScanner autoScan={false} />\n          </div>\n        </section>\n      )}\n\n      {/* Error Display */}\n      {error && (\n        <section className=\"py-8 px-4\">\n          <div className=\"container mx-auto\">\n            <Card className=\"bg-red-900/20 border-red-500/50\">\n              <CardContent className=\"p-6\">\n                <p className=\"text-red-300 text-center\">{error}</p>\n              </CardContent>\n            </Card>\n          </div>\n        </section>\n      )}\n\n      {/* Analysis Results */}\n      {activeTab === 'individual' && (stockData || analysis) && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n\n              {/* Stock Quote Card */}\n              {stockData && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <BarChart3 className=\"mr-2 h-5 w-5 text-blue-400\" />\n                      {stockData.symbol} Quote\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Price:</span>\n                        <span className=\"text-white font-semibold\">{formatCurrency(stockData.price)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Change:</span>\n                        <span className={stockData.change >= 0 ? \"text-green-400\" : \"text-red-400\"}>\n                          {formatCurrency(stockData.change)} ({formatPercentage(stockData.changePercent)})\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Volume:</span>\n                        <span className=\"text-white\">{stockData.volume.toLocaleString()}</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Trading Levels Card */}\n              {analysis && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <Target className=\"mr-2 h-5 w-5 text-green-400\" />\n                      Trading Levels\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Entry:</span>\n                        <span className=\"text-white font-semibold\">{formatCurrency(analysis.entryPrice)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Stop Loss:</span>\n                        <span className=\"text-red-400\">{formatCurrency(analysis.stopLoss)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Take Profit:</span>\n                        <span className=\"text-green-400\">{formatCurrency(analysis.takeProfit)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Risk/Reward:</span>\n                        <span className=\"text-blue-400 font-semibold\">{analysis.riskRewardRatio.toFixed(2)}:1</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Analysis Summary Card */}\n              {analysis && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <Brain className=\"mr-2 h-5 w-5 text-purple-400\" />\n                      AI Analysis\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Trend:</span>\n                        <span className={`font-semibold ${\n                          analysis.trend === 'BULLISH' ? 'text-green-400' :\n                          analysis.trend === 'BEARISH' ? 'text-red-400' : 'text-yellow-400'\n                        }`}>\n                          {analysis.trend}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Confidence:</span>\n                        <span className=\"text-white font-semibold\">{analysis.confidence.toFixed(1)}%</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Recommendation:</span>\n                        <span className={`font-semibold ${\n                          analysis.recommendation.includes('BUY') ? 'text-green-400' :\n                          analysis.recommendation.includes('SELL') ? 'text-red-400' : 'text-yellow-400'\n                        }`}>\n                          {analysis.recommendation.replace('_', ' ')}\n                        </span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n\n            {/* Technical Indicators */}\n            {analysis && (\n              <Card className=\"mt-6 bg-slate-800/50 border-slate-700\">\n                <CardHeader>\n                  <CardTitle className=\"text-white flex items-center\">\n                    <TrendingUp className=\"mr-2 h-5 w-5 text-orange-400\" />\n                    Technical Indicators\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                    {analysis.indicators.map((indicator, index) => (\n                      <div key={index} className=\"p-4 bg-slate-700/50 rounded-lg\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"text-white font-medium\">{indicator.name}</h4>\n                          <span className={`px-2 py-1 rounded text-xs font-semibold ${\n                            indicator.signal === 'BUY' ? 'bg-green-500/20 text-green-400' :\n                            indicator.signal === 'SELL' ? 'bg-red-500/20 text-red-400' :\n                            'bg-yellow-500/20 text-yellow-400'\n                          }`}>\n                            {indicator.signal}\n                          </span>\n                        </div>\n                        <p className=\"text-slate-300 text-sm\">{indicator.description}</p>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Support and Resistance Levels */}\n            {analysis && (analysis.supportLevels.length > 0 || analysis.resistanceLevels.length > 0) && (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\">\n                {analysis.supportLevels.length > 0 && (\n                  <Card className=\"bg-slate-800/50 border-slate-700\">\n                    <CardHeader>\n                      <CardTitle className=\"text-white flex items-center\">\n                        <Shield className=\"mr-2 h-5 w-5 text-green-400\" />\n                        Support Levels\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-2\">\n                        {analysis.supportLevels.map((level, index) => (\n                          <div key={index} className=\"flex justify-between\">\n                            <span className=\"text-slate-300\">Support {index + 1}:</span>\n                            <span className=\"text-green-400 font-semibold\">{formatCurrency(level)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n\n                {analysis.resistanceLevels.length > 0 && (\n                  <Card className=\"bg-slate-800/50 border-slate-700\">\n                    <CardHeader>\n                      <CardTitle className=\"text-white flex items-center\">\n                        <Shield className=\"mr-2 h-5 w-5 text-red-400\" />\n                        Resistance Levels\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-2\">\n                        {analysis.resistanceLevels.map((level, index) => (\n                          <div key={index} className=\"flex justify-between\">\n                            <span className=\"text-slate-300\">Resistance {index + 1}:</span>\n                            <span className=\"text-red-400 font-semibold\">{formatCurrency(level)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n              </div>\n            )}\n          </div>\n        </section>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iPAAQ,EAAC;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iPAAQ,EAAC;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iPAAQ,EAAC;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iPAAQ,EAA8B;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iPAAQ,EAAmB;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iPAAQ,EAAgB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iPAAQ,EAA0C;IAEpF,MAAM,iBAAiB;QAAC;QAAO;QAAO;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAQ;IAEtF,MAAM,iBAAiB,OAAO;QAC5B,eAAe;QACf,SAAS;QACT,YAAY;QACZ,aAAa;QAEb,IAAI;YACF,6CAA6C;YAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC1D,MAAM,CAAC,kBAAkB,EAAE,QAAQ;gBACnC,MAAM,CAAC,oBAAoB,EAAE,QAAQ;aACtC;YAED,IAAI,CAAC,cAAc,EAAE,EAAE;gBACrB,MAAM,aAAa,MAAM,cAAc,IAAI;gBAC3C,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,cAAc,MAAM,EAAE;YACvE;YAEA,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBACxB,MAAM,gBAAgB,MAAM,iBAAiB,IAAI;gBACjD,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,iBAAiB,MAAM,EAAE;YAC7E;YAEA,MAAM,CAAC,WAAW,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClD,cAAc,IAAI;gBAClB,iBAAiB,IAAI;aACtB;YAED,aAAa;YACb,YAAY;QACd,EAAE,OAAO,KAAU;YACjB,MAAM,eAAe,IAAI,OAAO,EAAE,SAAS,iBAAiB,IAAI,OAAO,EAAE,SAAS,SAC9E,gEACA;YACJ,SAAS;YACT,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,EAAE,cAAc;QAChB,IAAI,aAAa,IAAI,IAAI;YACvB,eAAe,aAAa,WAAW;YACvC,kBAAkB,aAAa,WAAW;QAC5C;IACF;IAEA,qBACE,8QAAC;QAAI,WAAU;;0BAEb,8QAAC;gBAAO,WAAU;0BAChB,cAAA,8QAAC;oBAAI,WAAU;8BACb,cAAA,8QAAC;wBAAI,WAAU;;0CACb,8QAAC;gCAAI,WAAU;;kDACb,8QAAC,6OAAK;wCAAC,WAAU;;;;;;kDACjB,8QAAC;wCAAG,WAAU;kDAAgC;;;;;;;;;;;;0CAEhD,8QAAC;gCAAI,WAAU;;kDACb,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,kBAAkB,EAAE,cAAc,eAAe,eAAe,mCAAmC;kDAChH;;;;;;kDAGD,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,kBAAkB,EAAE,cAAc,YAAY,eAAe,mCAAmC;kDAC7G;;;;;;kDAGD,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,kBAAkB,EAAE,cAAc,eAAe,eAAe,mCAAmC;kDAChH;;;;;;kDAGD,8QAAC,4KAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAAmE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/G,8QAAC;gBAAQ,WAAU;0BACjB,cAAA,8QAAC;oBAAI,WAAU;;sCACb,8QAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8QAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAM7D,8QAAC;4BAAI,WAAU;sCACb,cAAA,8QAAC;gCAAI,WAAU;;kDACb,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,eACV,2BACA,yDACJ;;0DAEF,8QAAC,uOAAG;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGzC,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,YACV,2BACA,yDACJ;;0DAEF,8QAAC,0OAAI;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG1C,8QAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,eACV,2BACA,yDACJ;;0DAEF,8QAAC,gPAAM;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;;;;;;;;;;;;wBAO/C,cAAc,6BACb,8QAAC;4BAAI,WAAU;;8CACb,8QAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8QAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;mCAInC,cAAc,0BAChB,8QAAC;4BAAI,WAAU;;8CACb,8QAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8QAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;iDAKrC,8QAAC;4BAAI,WAAU;;8CACb,8QAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8QAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,8QAAC,4KAAM;4CAEL,SAAS,mBAAmB,SAAS,YAAY;4CACjD,SAAS;gDACP,kBAAkB;gDAClB,eAAe;4CACjB;4CACA,UAAU;4CACV,WAAW,mBAAmB,SAC1B,kCACA;sDAGH;2CAZI;;;;;;;;;;8CAkBX,8QAAC;oCAAK,UAAU;oCAA0B,WAAU;;sDAClD,8QAAC;4CAAI,WAAU;;8DACb,8QAAC,gPAAM;oDAAC,WAAU;;;;;;8DAClB,8QAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,WAAU;oDACV,UAAU;;;;;;;;;;;;sDAGd,8QAAC,4KAAM;4CACL,MAAK;4CACL,UAAU,eAAe,CAAC,aAAa,IAAI;4CAC3C,WAAU;sDACX;;;;;;;;;;;;8CAKH,8QAAC,4KAAM;oCACL,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,UAAU;oCACV,WAAU;8CAET,4BACC;;0DACE,8QAAC,4PAAO;gDAAC,WAAU;;;;;;4CAA8B;4CACtC;4CAAe;;qEAG5B;;0DACE,8QAAC,uOAAG;gDAAC,WAAU;;;;;;4CAAiB;4CACX;;;;;;;;;;;;;;;;;;;;;;;;;YAUlC,cAAc,8BACb,8QAAC;gBAAQ,WAAU;0BACjB,cAAA,8QAAC;oBAAI,WAAU;8BACb,cAAA,8QAAC,wLAAe;wBAAC,UAAU;wBAAM,aAAa;;;;;;;;;;;;;;;;YAMnD,cAAc,2BACb,8QAAC;gBAAQ,WAAU;0BACjB,cAAA,8QAAC;oBAAI,WAAU;8BACb,cAAA,8QAAC,kLAAY;wBAAC,UAAU;;;;;;;;;;;;;;;;YAM7B,uBACC,8QAAC;gBAAQ,WAAU;0BACjB,cAAA,8QAAC;oBAAI,WAAU;8BACb,cAAA,8QAAC,wKAAI;wBAAC,WAAU;kCACd,cAAA,8QAAC,+KAAW;4BAAC,WAAU;sCACrB,cAAA,8QAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;YAQlD,cAAc,gBAAgB,CAAC,aAAa,QAAQ,mBACnD,8QAAC;gBAAQ,WAAU;0BACjB,cAAA,8QAAC;oBAAI,WAAU;;sCACb,8QAAC;4BAAI,WAAU;;gCAGZ,2BACC,8QAAC,wKAAI;oCAAC,WAAU;;sDACd,8QAAC,8KAAU;sDACT,cAAA,8QAAC,6KAAS;gDAAC,WAAU;;kEACnB,8QAAC,+PAAS;wDAAC,WAAU;;;;;;oDACpB,UAAU,MAAM;oDAAC;;;;;;;;;;;;sDAGtB,8QAAC,+KAAW;sDACV,cAAA,8QAAC;gDAAI,WAAU;;kEACb,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;0EAA4B,IAAA,qKAAc,EAAC,UAAU,KAAK;;;;;;;;;;;;kEAE5E,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAW,UAAU,MAAM,IAAI,IAAI,mBAAmB;;oEACzD,IAAA,qKAAc,EAAC,UAAU,MAAM;oEAAE;oEAAG,IAAA,uKAAgB,EAAC,UAAU,aAAa;oEAAE;;;;;;;;;;;;;kEAGnF,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;0EAAc,UAAU,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQtE,0BACC,8QAAC,wKAAI;oCAAC,WAAU;;sDACd,8QAAC,8KAAU;sDACT,cAAA,8QAAC,6KAAS;gDAAC,WAAU;;kEACnB,8QAAC,gPAAM;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;;;;;;sDAItD,8QAAC,+KAAW;sDACV,cAAA,8QAAC;gDAAI,WAAU;;kEACb,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;0EAA4B,IAAA,qKAAc,EAAC,SAAS,UAAU;;;;;;;;;;;;kEAEhF,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;0EAAgB,IAAA,qKAAc,EAAC,SAAS,QAAQ;;;;;;;;;;;;kEAElE,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;0EAAkB,IAAA,qKAAc,EAAC,SAAS,UAAU;;;;;;;;;;;;kEAEtE,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;;oEAA+B,SAAS,eAAe,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQ5F,0BACC,8QAAC,wKAAI;oCAAC,WAAU;;sDACd,8QAAC,8KAAU;sDACT,cAAA,8QAAC,6KAAS;gDAAC,WAAU;;kEACnB,8QAAC,6OAAK;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;;;;;;sDAItD,8QAAC,+KAAW;sDACV,cAAA,8QAAC;gDAAI,WAAU;;kEACb,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAW,CAAC,cAAc,EAC9B,SAAS,KAAK,KAAK,YAAY,mBAC/B,SAAS,KAAK,KAAK,YAAY,iBAAiB,mBAChD;0EACC,SAAS,KAAK;;;;;;;;;;;;kEAGnB,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAU;;oEAA4B,SAAS,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE7E,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,8QAAC;gEAAK,WAAW,CAAC,cAAc,EAC9B,SAAS,cAAc,CAAC,QAAQ,CAAC,SAAS,mBAC1C,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU,iBAAiB,mBAC5D;0EACC,SAAS,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAUnD,0BACC,8QAAC,wKAAI;4BAAC,WAAU;;8CACd,8QAAC,8KAAU;8CACT,cAAA,8QAAC,6KAAS;wCAAC,WAAU;;0DACnB,8QAAC,gQAAU;gDAAC,WAAU;;;;;;4CAAiC;;;;;;;;;;;;8CAI3D,8QAAC,+KAAW;8CACV,cAAA,8QAAC;wCAAI,WAAU;kDACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACnC,8QAAC;gDAAgB,WAAU;;kEACzB,8QAAC;wDAAI,WAAU;;0EACb,8QAAC;gEAAG,WAAU;0EAA0B,UAAU,IAAI;;;;;;0EACtD,8QAAC;gEAAK,WAAW,CAAC,wCAAwC,EACxD,UAAU,MAAM,KAAK,QAAQ,mCAC7B,UAAU,MAAM,KAAK,SAAS,+BAC9B,oCACA;0EACC,UAAU,MAAM;;;;;;;;;;;;kEAGrB,8QAAC;wDAAE,WAAU;kEAA0B,UAAU,WAAW;;;;;;;+CAXpD;;;;;;;;;;;;;;;;;;;;;wBAoBnB,YAAY,CAAC,SAAS,aAAa,CAAC,MAAM,GAAG,KAAK,SAAS,gBAAgB,CAAC,MAAM,GAAG,CAAC,mBACrF,8QAAC;4BAAI,WAAU;;gCACZ,SAAS,aAAa,CAAC,MAAM,GAAG,mBAC/B,8QAAC,wKAAI;oCAAC,WAAU;;sDACd,8QAAC,8KAAU;sDACT,cAAA,8QAAC,6KAAS;gDAAC,WAAU;;kEACnB,8QAAC,gPAAM;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;;;;;;sDAItD,8QAAC,+KAAW;sDACV,cAAA,8QAAC;gDAAI,WAAU;0DACZ,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,sBAClC,8QAAC;wDAAgB,WAAU;;0EACzB,8QAAC;gEAAK,WAAU;;oEAAiB;oEAAS,QAAQ;oEAAE;;;;;;;0EACpD,8QAAC;gEAAK,WAAU;0EAAgC,IAAA,qKAAc,EAAC;;;;;;;uDAFvD;;;;;;;;;;;;;;;;;;;;;gCAUnB,SAAS,gBAAgB,CAAC,MAAM,GAAG,mBAClC,8QAAC,wKAAI;oCAAC,WAAU;;sDACd,8QAAC,8KAAU;sDACT,cAAA,8QAAC,6KAAS;gDAAC,WAAU;;kEACnB,8QAAC,gPAAM;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;;;;;;sDAIpD,8QAAC,+KAAW;sDACV,cAAA,8QAAC;gDAAI,WAAU;0DACZ,SAAS,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACrC,8QAAC;wDAAgB,WAAU;;0EACzB,8QAAC;gEAAK,WAAU;;oEAAiB;oEAAY,QAAQ;oEAAE;;;;;;;0EACvD,8QAAC;gEAAK,WAAU;0EAA8B,IAAA,qKAAc,EAAC;;;;;;;uDAFrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBpC", "debugId": null}}]}
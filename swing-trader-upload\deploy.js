#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 SwingTrader AI Deployment Script');
console.log('=====================================');

// Check if Vercel CLI is installed
try {
  execSync('vercel --version', { stdio: 'ignore' });
  console.log('✅ Vercel CLI found');
} catch (error) {
  console.log('📦 Installing Vercel CLI...');
  execSync('npm install -g vercel', { stdio: 'inherit' });
}

// Check for environment variables
const envFile = path.join(__dirname, '.env.local');
if (!fs.existsSync(envFile)) {
  console.log('❌ .env.local file not found!');
  console.log('Please create .env.local with your API keys:');
  console.log('');
  console.log('POLYGON_API_KEY=your_polygon_api_key');
  console.log('FMP_API_KEY=your_fmp_api_key');
  console.log('ALPACA_API_KEY=your_alpaca_api_key');
  console.log('ALPACA_SECRET_KEY=your_alpaca_secret_key');
  process.exit(1);
}

console.log('✅ Environment file found');

// Build the project
console.log('🔨 Building project...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build successful');
} catch (error) {
  console.log('❌ Build failed');
  process.exit(1);
}

// Deploy to Vercel
console.log('🚀 Deploying to Vercel...');
try {
  const result = execSync('vercel --prod', { encoding: 'utf8' });
  console.log('✅ Deployment successful!');
  console.log('🌐 Your app is live at:', result.trim());
} catch (error) {
  console.log('❌ Deployment failed');
  console.log('Please run: vercel login');
  console.log('Then run this script again');
}

console.log('');
console.log('🎉 SwingTrader AI is now live!');
console.log('Share the URL with anyone to use the app.');

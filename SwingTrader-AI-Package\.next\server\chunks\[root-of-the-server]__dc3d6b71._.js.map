{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/data/watchlist.ts"], "sourcesContent": ["export interface WatchlistStock {\n  symbol: string\n  name: string\n  sector: string\n  marketCap: 'large' | 'mid' | 'small'\n}\n\nexport const SWING_TRADING_WATCHLIST: WatchlistStock[] = [\n  // Large Cap Tech Giants\n  { symbol: 'MSFT', name: 'Microsoft Corp', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'NVDA', name: 'NVIDIA Corp', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'AMZN', name: 'Amazon.com Inc', sector: 'Consumer Discretionary', marketCap: 'large' },\n  { symbol: 'GOOG', name: 'Alphabet Inc Class C', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'GOOGL', name: 'Alphabet Inc Class A', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'META', name: 'Meta Platforms Inc', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'AVGO', name: 'Broadcom Inc', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'TSM', name: 'Taiwan Semiconductor', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'ORCL', name: 'Oracle Corp', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'NFLX', name: 'Netflix Inc', sector: 'Communication Services', marketCap: 'large' },\n  { symbol: 'CSCO', name: 'Cisco Systems Inc', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'AMD', name: 'Advanced Micro Devices Inc', sector: 'Technology', marketCap: 'large' },\n\n  // Financial Services\n  { symbol: 'JPM', name: 'JPMorgan Chase & Co', sector: 'Financial Services', marketCap: 'large' },\n  { symbol: 'BAC', name: 'Bank of America Corp', sector: 'Financial Services', marketCap: 'large' },\n  { symbol: 'MS', name: 'Morgan Stanley', sector: 'Financial Services', marketCap: 'large' },\n  { symbol: 'SCHW', name: 'Charles Schwab Corp', sector: 'Financial Services', marketCap: 'large' },\n  { symbol: 'C', name: 'Citigroup Inc', sector: 'Financial Services', marketCap: 'large' },\n\n  // Healthcare & Pharmaceuticals\n  { symbol: 'JNJ', name: 'Johnson & Johnson', sector: 'Healthcare', marketCap: 'large' },\n  { symbol: 'ABBV', name: 'AbbVie Inc', sector: 'Healthcare', marketCap: 'large' },\n  { symbol: 'MRK', name: 'Merck & Co Inc', sector: 'Healthcare', marketCap: 'large' },\n  { symbol: 'GILD', name: 'Gilead Sciences Inc', sector: 'Healthcare', marketCap: 'large' },\n\n  // Industrial & Manufacturing\n  { symbol: 'GE', name: 'General Electric Co', sector: 'Industrial', marketCap: 'large' },\n  { symbol: 'CAT', name: 'Caterpillar Inc', sector: 'Industrial', marketCap: 'large' },\n  { symbol: 'BA', name: 'Boeing Co', sector: 'Industrial', marketCap: 'large' },\n  { symbol: 'GEV', name: 'GE Vernova Inc', sector: 'Industrial', marketCap: 'large' },\n\n  // Semiconductors\n  { symbol: 'ASML', name: 'ASML Holding NV', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'MU', name: 'Micron Technology Inc', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'LRCX', name: 'Lam Research Corp', sector: 'Technology', marketCap: 'large' },\n\n  // Consumer & Retail\n  { symbol: 'DIS', name: 'Walt Disney Co', sector: 'Communication Services', marketCap: 'large' },\n  { symbol: 'SBUX', name: 'Starbucks Corp', sector: 'Consumer Discretionary', marketCap: 'large' },\n  { symbol: 'MO', name: 'Altria Group Inc', sector: 'Consumer Staples', marketCap: 'large' },\n\n  // Energy & Utilities\n  { symbol: 'CEG', name: 'Constellation Energy Corp', sector: 'Utilities', marketCap: 'large' },\n  { symbol: 'VST', name: 'Vistra Corp', sector: 'Utilities', marketCap: 'mid' },\n\n  // Automotive\n  { symbol: 'GM', name: 'General Motors Co', sector: 'Consumer Discretionary', marketCap: 'large' },\n\n  // Growth & Tech Mid-Caps\n  { symbol: 'PLTR', name: 'Palantir Technologies Inc', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'APP', name: 'Applovin Corp', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'DASH', name: 'DoorDash Inc', sector: 'Consumer Discretionary', marketCap: 'mid' },\n  { symbol: 'NET', name: 'Cloudflare Inc', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'DDOG', name: 'Datadog Inc', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'ZS', name: 'Zscaler Inc', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'SHOP', name: 'Shopify Inc', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'RBLX', name: 'Roblox Corp', sector: 'Communication Services', marketCap: 'mid' },\n\n  // Mining & Commodities\n  { symbol: 'AEM', name: 'Agnico Eagle Mines Ltd', sector: 'Materials', marketCap: 'mid' },\n  { symbol: 'NEM', name: 'Newmont Corp', sector: 'Materials', marketCap: 'large' },\n  { symbol: 'CCJ', name: 'Cameco Corp', sector: 'Energy', marketCap: 'mid' },\n  { symbol: 'PAAS', name: 'Pan American Silver Corp', sector: 'Materials', marketCap: 'small' },\n  { symbol: 'BTG', name: 'B2Gold Corp', sector: 'Materials', marketCap: 'small' },\n  { symbol: 'HL', name: 'Hecla Mining Co', sector: 'Materials', marketCap: 'small' },\n  { symbol: 'MP', name: 'MP Materials Corp', sector: 'Materials', marketCap: 'small' },\n  { symbol: 'AG', name: 'First Majestic Silver Corp', sector: 'Materials', marketCap: 'small' },\n\n  // Transportation & Travel\n  { symbol: 'UAL', name: 'United Airlines Holdings Inc', sector: 'Industrial', marketCap: 'mid' },\n  { symbol: 'NCLH', name: 'Norwegian Cruise Line', sector: 'Consumer Discretionary', marketCap: 'mid' },\n\n  // Fintech & Trading\n  { symbol: 'HOOD', name: 'Robinhood Markets Inc', sector: 'Financial Services', marketCap: 'mid' },\n  { symbol: 'SOFI', name: 'SoFi Technologies Inc', sector: 'Financial Services', marketCap: 'small' },\n\n  // Consumer Brands\n  { symbol: 'CELH', name: 'Celsius Holdings Inc', sector: 'Consumer Staples', marketCap: 'small' },\n  { symbol: 'LEVI', name: 'Levi Strauss & Co', sector: 'Consumer Discretionary', marketCap: 'small' },\n  { symbol: 'ELF', name: 'e.l.f. Beauty Inc', sector: 'Consumer Discretionary', marketCap: 'small' },\n  { symbol: 'ETSY', name: 'Etsy Inc', sector: 'Consumer Discretionary', marketCap: 'mid' },\n  { symbol: 'W', name: 'Wayfair Inc', sector: 'Consumer Discretionary', marketCap: 'mid' },\n\n  // Crypto & Blockchain\n  { symbol: 'RIOT', name: 'Riot Platforms Inc', sector: 'Technology', marketCap: 'small' },\n  { symbol: 'HUT', name: 'Hut 8 Corp', sector: 'Technology', marketCap: 'small' },\n  { symbol: 'IREN', name: 'IREN Ltd', sector: 'Technology', marketCap: 'small' },\n\n  // International & Emerging\n  { symbol: 'BILI', name: 'Bilibili Inc', sector: 'Communication Services', marketCap: 'small' },\n  { symbol: 'TIGR', name: 'UP Fintech Holding Ltd', sector: 'Financial Services', marketCap: 'small' },\n  { symbol: 'FUTU', name: 'Futu Holdings Ltd', sector: 'Financial Services', marketCap: 'small' },\n  { symbol: 'NBIS', name: 'Nebius Group NV', sector: 'Technology', marketCap: 'small' },\n\n  // Emerging Tech & AI\n  { symbol: 'SOUN', name: 'SoundHound AI Inc', sector: 'Technology', marketCap: 'small' },\n  { symbol: 'IONQ', name: 'IonQ Inc', sector: 'Technology', marketCap: 'small' },\n  { symbol: 'RGTI', name: 'Rigetti Computing Inc', sector: 'Technology', marketCap: 'small' },\n\n  // Aerospace & Defense\n  { symbol: 'RKLB', name: 'Rocket Lab Corp', sector: 'Industrial', marketCap: 'small' },\n  { symbol: 'ASTS', name: 'AST SpaceMobile Inc', sector: 'Technology', marketCap: 'small' },\n\n  // Infrastructure & Utilities\n  { symbol: 'VRT', name: 'Vertiv Holdings Co', sector: 'Industrial', marketCap: 'mid' },\n]\n\n// Group stocks by sector for analysis\nexport const STOCKS_BY_SECTOR = SWING_TRADING_WATCHLIST.reduce((acc, stock) => {\n  if (!acc[stock.sector]) {\n    acc[stock.sector] = []\n  }\n  acc[stock.sector].push(stock)\n  return acc\n}, {} as Record<string, WatchlistStock[]>)\n\n// Get all symbols as array\nexport const ALL_SYMBOLS = SWING_TRADING_WATCHLIST.map(stock => stock.symbol)\n\n// Priority symbols for faster scanning (top liquid stocks)\nexport const PRIORITY_SYMBOLS = [\n  'MSFT', 'NVDA', 'AMZN', 'GOOG', 'GOOGL', 'META', 'AVGO', 'TSM', \n  'JPM', 'NFLX', 'ORCL', 'JNJ', 'BAC', 'ABBV', 'ASML', 'PLTR'\n]\n"], "names": [], "mappings": ";;;;;;;;;;AAOO,MAAM,0BAA4C;IACvD,wBAAwB;IACxB;QAAE,QAAQ;QAAQ,MAAM;QAAkB,QAAQ;QAAc,WAAW;IAAQ;IACnF;QAAE,QAAQ;QAAQ,MAAM;QAAe,QAAQ;QAAc,WAAW;IAAQ;IAChF;QAAE,QAAQ;QAAQ,MAAM;QAAkB,QAAQ;QAA0B,WAAW;IAAQ;IAC/F;QAAE,QAAQ;QAAQ,MAAM;QAAwB,QAAQ;QAAc,WAAW;IAAQ;IACzF;QAAE,QAAQ;QAAS,MAAM;QAAwB,QAAQ;QAAc,WAAW;IAAQ;IAC1F;QAAE,QAAQ;QAAQ,MAAM;QAAsB,QAAQ;QAAc,WAAW;IAAQ;IACvF;QAAE,QAAQ;QAAQ,MAAM;QAAgB,QAAQ;QAAc,WAAW;IAAQ;IACjF;QAAE,QAAQ;QAAO,MAAM;QAAwB,QAAQ;QAAc,WAAW;IAAQ;IACxF;QAAE,QAAQ;QAAQ,MAAM;QAAe,QAAQ;QAAc,WAAW;IAAQ;IAChF;QAAE,QAAQ;QAAQ,MAAM;QAAe,QAAQ;QAA0B,WAAW;IAAQ;IAC5F;QAAE,QAAQ;QAAQ,MAAM;QAAqB,QAAQ;QAAc,WAAW;IAAQ;IACtF;QAAE,QAAQ;QAAO,MAAM;QAA8B,QAAQ;QAAc,WAAW;IAAQ;IAE9F,qBAAqB;IACrB;QAAE,QAAQ;QAAO,MAAM;QAAuB,QAAQ;QAAsB,WAAW;IAAQ;IAC/F;QAAE,QAAQ;QAAO,MAAM;QAAwB,QAAQ;QAAsB,WAAW;IAAQ;IAChG;QAAE,QAAQ;QAAM,MAAM;QAAkB,QAAQ;QAAsB,WAAW;IAAQ;IACzF;QAAE,QAAQ;QAAQ,MAAM;QAAuB,QAAQ;QAAsB,WAAW;IAAQ;IAChG;QAAE,QAAQ;QAAK,MAAM;QAAiB,QAAQ;QAAsB,WAAW;IAAQ;IAEvF,+BAA+B;IAC/B;QAAE,QAAQ;QAAO,MAAM;QAAqB,QAAQ;QAAc,WAAW;IAAQ;IACrF;QAAE,QAAQ;QAAQ,MAAM;QAAc,QAAQ;QAAc,WAAW;IAAQ;IAC/E;QAAE,QAAQ;QAAO,MAAM;QAAkB,QAAQ;QAAc,WAAW;IAAQ;IAClF;QAAE,QAAQ;QAAQ,MAAM;QAAuB,QAAQ;QAAc,WAAW;IAAQ;IAExF,6BAA6B;IAC7B;QAAE,QAAQ;QAAM,MAAM;QAAuB,QAAQ;QAAc,WAAW;IAAQ;IACtF;QAAE,QAAQ;QAAO,MAAM;QAAmB,QAAQ;QAAc,WAAW;IAAQ;IACnF;QAAE,QAAQ;QAAM,MAAM;QAAa,QAAQ;QAAc,WAAW;IAAQ;IAC5E;QAAE,QAAQ;QAAO,MAAM;QAAkB,QAAQ;QAAc,WAAW;IAAQ;IAElF,iBAAiB;IACjB;QAAE,QAAQ;QAAQ,MAAM;QAAmB,QAAQ;QAAc,WAAW;IAAQ;IACpF;QAAE,QAAQ;QAAM,MAAM;QAAyB,QAAQ;QAAc,WAAW;IAAQ;IACxF;QAAE,QAAQ;QAAQ,MAAM;QAAqB,QAAQ;QAAc,WAAW;IAAQ;IAEtF,oBAAoB;IACpB;QAAE,QAAQ;QAAO,MAAM;QAAkB,QAAQ;QAA0B,WAAW;IAAQ;IAC9F;QAAE,QAAQ;QAAQ,MAAM;QAAkB,QAAQ;QAA0B,WAAW;IAAQ;IAC/F;QAAE,QAAQ;QAAM,MAAM;QAAoB,QAAQ;QAAoB,WAAW;IAAQ;IAEzF,qBAAqB;IACrB;QAAE,QAAQ;QAAO,MAAM;QAA6B,QAAQ;QAAa,WAAW;IAAQ;IAC5F;QAAE,QAAQ;QAAO,MAAM;QAAe,QAAQ;QAAa,WAAW;IAAM;IAE5E,aAAa;IACb;QAAE,QAAQ;QAAM,MAAM;QAAqB,QAAQ;QAA0B,WAAW;IAAQ;IAEhG,yBAAyB;IACzB;QAAE,QAAQ;QAAQ,MAAM;QAA6B,QAAQ;QAAc,WAAW;IAAM;IAC5F;QAAE,QAAQ;QAAO,MAAM;QAAiB,QAAQ;QAAc,WAAW;IAAM;IAC/E;QAAE,QAAQ;QAAQ,MAAM;QAAgB,QAAQ;QAA0B,WAAW;IAAM;IAC3F;QAAE,QAAQ;QAAO,MAAM;QAAkB,QAAQ;QAAc,WAAW;IAAM;IAChF;QAAE,QAAQ;QAAQ,MAAM;QAAe,QAAQ;QAAc,WAAW;IAAM;IAC9E;QAAE,QAAQ;QAAM,MAAM;QAAe,QAAQ;QAAc,WAAW;IAAM;IAC5E;QAAE,QAAQ;QAAQ,MAAM;QAAe,QAAQ;QAAc,WAAW;IAAM;IAC9E;QAAE,QAAQ;QAAQ,MAAM;QAAe,QAAQ;QAA0B,WAAW;IAAM;IAE1F,uBAAuB;IACvB;QAAE,QAAQ;QAAO,MAAM;QAA0B,QAAQ;QAAa,WAAW;IAAM;IACvF;QAAE,QAAQ;QAAO,MAAM;QAAgB,QAAQ;QAAa,WAAW;IAAQ;IAC/E;QAAE,QAAQ;QAAO,MAAM;QAAe,QAAQ;QAAU,WAAW;IAAM;IACzE;QAAE,QAAQ;QAAQ,MAAM;QAA4B,QAAQ;QAAa,WAAW;IAAQ;IAC5F;QAAE,QAAQ;QAAO,MAAM;QAAe,QAAQ;QAAa,WAAW;IAAQ;IAC9E;QAAE,QAAQ;QAAM,MAAM;QAAmB,QAAQ;QAAa,WAAW;IAAQ;IACjF;QAAE,QAAQ;QAAM,MAAM;QAAqB,QAAQ;QAAa,WAAW;IAAQ;IACnF;QAAE,QAAQ;QAAM,MAAM;QAA8B,QAAQ;QAAa,WAAW;IAAQ;IAE5F,0BAA0B;IAC1B;QAAE,QAAQ;QAAO,MAAM;QAAgC,QAAQ;QAAc,WAAW;IAAM;IAC9F;QAAE,QAAQ;QAAQ,MAAM;QAAyB,QAAQ;QAA0B,WAAW;IAAM;IAEpG,oBAAoB;IACpB;QAAE,QAAQ;QAAQ,MAAM;QAAyB,QAAQ;QAAsB,WAAW;IAAM;IAChG;QAAE,QAAQ;QAAQ,MAAM;QAAyB,QAAQ;QAAsB,WAAW;IAAQ;IAElG,kBAAkB;IAClB;QAAE,QAAQ;QAAQ,MAAM;QAAwB,QAAQ;QAAoB,WAAW;IAAQ;IAC/F;QAAE,QAAQ;QAAQ,MAAM;QAAqB,QAAQ;QAA0B,WAAW;IAAQ;IAClG;QAAE,QAAQ;QAAO,MAAM;QAAqB,QAAQ;QAA0B,WAAW;IAAQ;IACjG;QAAE,QAAQ;QAAQ,MAAM;QAAY,QAAQ;QAA0B,WAAW;IAAM;IACvF;QAAE,QAAQ;QAAK,MAAM;QAAe,QAAQ;QAA0B,WAAW;IAAM;IAEvF,sBAAsB;IACtB;QAAE,QAAQ;QAAQ,MAAM;QAAsB,QAAQ;QAAc,WAAW;IAAQ;IACvF;QAAE,QAAQ;QAAO,MAAM;QAAc,QAAQ;QAAc,WAAW;IAAQ;IAC9E;QAAE,QAAQ;QAAQ,MAAM;QAAY,QAAQ;QAAc,WAAW;IAAQ;IAE7E,2BAA2B;IAC3B;QAAE,QAAQ;QAAQ,MAAM;QAAgB,QAAQ;QAA0B,WAAW;IAAQ;IAC7F;QAAE,QAAQ;QAAQ,MAAM;QAA0B,QAAQ;QAAsB,WAAW;IAAQ;IACnG;QAAE,QAAQ;QAAQ,MAAM;QAAqB,QAAQ;QAAsB,WAAW;IAAQ;IAC9F;QAAE,QAAQ;QAAQ,MAAM;QAAmB,QAAQ;QAAc,WAAW;IAAQ;IAEpF,qBAAqB;IACrB;QAAE,QAAQ;QAAQ,MAAM;QAAqB,QAAQ;QAAc,WAAW;IAAQ;IACtF;QAAE,QAAQ;QAAQ,MAAM;QAAY,QAAQ;QAAc,WAAW;IAAQ;IAC7E;QAAE,QAAQ;QAAQ,MAAM;QAAyB,QAAQ;QAAc,WAAW;IAAQ;IAE1F,sBAAsB;IACtB;QAAE,QAAQ;QAAQ,MAAM;QAAmB,QAAQ;QAAc,WAAW;IAAQ;IACpF;QAAE,QAAQ;QAAQ,MAAM;QAAuB,QAAQ;QAAc,WAAW;IAAQ;IAExF,6BAA6B;IAC7B;QAAE,QAAQ;QAAO,MAAM;QAAsB,QAAQ;QAAc,WAAW;IAAM;CACrF;AAGM,MAAM,mBAAmB,wBAAwB,MAAM,CAAC,CAAC,KAAK;IACnE,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE;QACtB,GAAG,CAAC,MAAM,MAAM,CAAC,GAAG,EAAE;IACxB;IACA,GAAG,CAAC,MAAM,MAAM,CAAC,CAAC,IAAI,CAAC;IACvB,OAAO;AACT,GAAG,CAAC;AAGG,MAAM,cAAc,wBAAwB,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM;AAGrE,MAAM,mBAAmB;IAC9B;IAAQ;IAAQ;IAAQ;IAAQ;IAAS;IAAQ;IAAQ;IACzD;IAAO;IAAQ;IAAQ;IAAO;IAAO;IAAQ;IAAQ;CACtD", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/indicators.ts"], "sourcesContent": ["import { CandlestickData, TechnicalIndicator } from '@/types/trading'\n\nexport class TechnicalIndicators {\n  // Simple Moving Average\n  static sma(data: number[], period: number): number[] {\n    const result: number[] = []\n    for (let i = period - 1; i < data.length; i++) {\n      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)\n      result.push(sum / period)\n    }\n    return result\n  }\n\n  // Exponential Moving Average\n  static ema(data: number[], period: number): number[] {\n    const result: number[] = []\n    const multiplier = 2 / (period + 1)\n    \n    // Start with SMA for first value\n    let ema = data.slice(0, period).reduce((a, b) => a + b, 0) / period\n    result.push(ema)\n    \n    for (let i = period; i < data.length; i++) {\n      ema = (data[i] * multiplier) + (ema * (1 - multiplier))\n      result.push(ema)\n    }\n    \n    return result\n  }\n\n  // Relative Strength Index\n  static rsi(data: number[], period: number = 14): number[] {\n    const gains: number[] = []\n    const losses: number[] = []\n    \n    for (let i = 1; i < data.length; i++) {\n      const change = data[i] - data[i - 1]\n      gains.push(change > 0 ? change : 0)\n      losses.push(change < 0 ? Math.abs(change) : 0)\n    }\n    \n    const avgGains = this.sma(gains, period)\n    const avgLosses = this.sma(losses, period)\n    \n    return avgGains.map((gain, i) => {\n      const rs = gain / avgLosses[i]\n      return 100 - (100 / (1 + rs))\n    })\n  }\n\n  // MACD (Moving Average Convergence Divergence)\n  static macd(data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {\n    const fastEMA = this.ema(data, fastPeriod)\n    const slowEMA = this.ema(data, slowPeriod)\n    \n    // Align arrays (slowEMA starts later)\n    const startIndex = slowPeriod - fastPeriod\n    const macdLine = fastEMA.slice(startIndex).map((fast, i) => fast - slowEMA[i])\n    \n    const signalLine = this.ema(macdLine, signalPeriod)\n    const histogram = macdLine.slice(signalPeriod - 1).map((macd, i) => macd - signalLine[i])\n    \n    return {\n      macd: macdLine,\n      signal: signalLine,\n      histogram\n    }\n  }\n\n  // Bollinger Bands\n  static bollingerBands(data: number[], period: number = 20, stdDev: number = 2) {\n    const sma = this.sma(data, period)\n    const bands = sma.map((avg, i) => {\n      const slice = data.slice(i, i + period)\n      const variance = slice.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / period\n      const standardDeviation = Math.sqrt(variance)\n      \n      return {\n        upper: avg + (standardDeviation * stdDev),\n        middle: avg,\n        lower: avg - (standardDeviation * stdDev)\n      }\n    })\n    \n    return bands\n  }\n\n  // Support and Resistance Levels\n  static findSupportResistance(candles: CandlestickData[], lookback: number = 20): { support: number[], resistance: number[] } {\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    \n    const resistance: number[] = []\n    const support: number[] = []\n    \n    for (let i = lookback; i < candles.length - lookback; i++) {\n      const currentHigh = highs[i]\n      const currentLow = lows[i]\n      \n      // Check if current high is a local maximum\n      const isResistance = highs.slice(i - lookback, i).every(h => h <= currentHigh) &&\n                          highs.slice(i + 1, i + lookback + 1).every(h => h <= currentHigh)\n      \n      // Check if current low is a local minimum\n      const isSupport = lows.slice(i - lookback, i).every(l => l >= currentLow) &&\n                       lows.slice(i + 1, i + lookback + 1).every(l => l >= currentLow)\n      \n      if (isResistance) resistance.push(currentHigh)\n      if (isSupport) support.push(currentLow)\n    }\n    \n    return { support, resistance }\n  }\n\n  // Volume analysis\n  static volumeAnalysis(candles: CandlestickData[], period: number = 20) {\n    const volumes = candles.map(c => c.volume)\n    const avgVolume = this.sma(volumes, period)\n    const currentVolume = volumes[volumes.length - 1]\n    const currentAvgVolume = avgVolume[avgVolume.length - 1]\n\n    return {\n      currentVolume,\n      averageVolume: currentAvgVolume,\n      volumeRatio: currentVolume / currentAvgVolume,\n      isHighVolume: currentVolume > currentAvgVolume * 1.5,\n      isLowVolume: currentVolume < currentAvgVolume * 0.5\n    }\n  }\n\n  // Swing Trading Analysis\n  static analyzeSwingSetup(candles: CandlestickData[]): TechnicalIndicator[] {\n    const closes = candles.map(c => c.close)\n    const indicators: TechnicalIndicator[] = []\n\n    // RSI Analysis\n    const rsi = this.rsi(closes)\n    const currentRSI = rsi[rsi.length - 1]\n\n    let rsiSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`\n\n    if (currentRSI < 30) {\n      rsiSignal = 'BUY'\n      rsiDescription += ' - Oversold condition, potential bounce'\n    } else if (currentRSI > 70) {\n      rsiSignal = 'SELL'\n      rsiDescription += ' - Overbought condition, potential pullback'\n    } else {\n      rsiDescription += ' - Neutral zone'\n    }\n\n    indicators.push({\n      name: 'RSI',\n      value: currentRSI,\n      signal: rsiSignal,\n      description: rsiDescription\n    })\n\n    // Moving Average Analysis\n    const sma20 = this.sma(closes, 20)\n    const sma50 = this.sma(closes, 50)\n    const currentPrice = closes[closes.length - 1]\n    const currentSMA20 = sma20[sma20.length - 1]\n    const currentSMA50 = sma50[sma50.length - 1]\n\n    let maSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`\n\n    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n      maSignal = 'BUY'\n      maDescription += ' - Bullish trend'\n    } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n      maSignal = 'SELL'\n      maDescription += ' - Bearish trend'\n    } else {\n      maDescription += ' - Mixed signals'\n    }\n\n    indicators.push({\n      name: 'Moving Averages',\n      value: (currentPrice / currentSMA20 - 1) * 100,\n      signal: maSignal,\n      description: maDescription\n    })\n\n    // MACD Analysis\n    const macdData = this.macd(closes)\n    const currentMACD = macdData.macd[macdData.macd.length - 1]\n    const currentSignal = macdData.signal[macdData.signal.length - 1]\n    const currentHistogram = macdData.histogram[macdData.histogram.length - 1]\n\n    let macdSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`\n\n    if (currentMACD > currentSignal && currentHistogram > 0) {\n      macdSignal = 'BUY'\n      macdDescription += ' - Bullish momentum'\n    } else if (currentMACD < currentSignal && currentHistogram < 0) {\n      macdSignal = 'SELL'\n      macdDescription += ' - Bearish momentum'\n    } else {\n      macdDescription += ' - Momentum shifting'\n    }\n\n    indicators.push({\n      name: 'MACD',\n      value: currentHistogram,\n      signal: macdSignal,\n      description: macdDescription\n    })\n\n    // Volume Analysis\n    const volumeData = this.volumeAnalysis(candles)\n    let volumeSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`\n\n    if (volumeData.isHighVolume) {\n      volumeSignal = 'BUY'\n      volumeDescription += ' - High volume confirms move'\n    } else if (volumeData.isLowVolume) {\n      volumeSignal = 'SELL'\n      volumeDescription += ' - Low volume, weak conviction'\n    } else {\n      volumeDescription += ' - Normal volume'\n    }\n\n    indicators.push({\n      name: 'Volume',\n      value: volumeData.volumeRatio,\n      signal: volumeSignal,\n      description: volumeDescription\n    })\n\n    return indicators\n  }\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM;IACX,wBAAwB;IACxB,OAAO,IAAI,IAAc,EAAE,MAAc,EAAY;QACnD,MAAM,SAAmB,EAAE;QAC3B,IAAK,IAAI,IAAI,SAAS,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAC7C,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI,SAAS,GAAG,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;YACtE,OAAO,IAAI,CAAC,MAAM;QACpB;QACA,OAAO;IACT;IAEA,6BAA6B;IAC7B,OAAO,IAAI,IAAc,EAAE,MAAc,EAAY;QACnD,MAAM,SAAmB,EAAE;QAC3B,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC;QAElC,iCAAiC;QACjC,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,QAAQ,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK;QAC7D,OAAO,IAAI,CAAC;QAEZ,IAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,MAAM,EAAE,IAAK;YACzC,MAAM,AAAC,IAAI,CAAC,EAAE,GAAG,aAAe,MAAM,CAAC,IAAI,UAAU;YACrD,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;IACT;IAEA,0BAA0B;IAC1B,OAAO,IAAI,IAAc,EAAE,SAAiB,EAAE,EAAY;QACxD,MAAM,QAAkB,EAAE;QAC1B,MAAM,SAAmB,EAAE;QAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;YACpC,MAAM,IAAI,CAAC,SAAS,IAAI,SAAS;YACjC,OAAO,IAAI,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,UAAU;QAC9C;QAEA,MAAM,WAAW,IAAI,CAAC,GAAG,CAAC,OAAO;QACjC,MAAM,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ;QAEnC,OAAO,SAAS,GAAG,CAAC,CAAC,MAAM;YACzB,MAAM,KAAK,OAAO,SAAS,CAAC,EAAE;YAC9B,OAAO,MAAO,MAAM,CAAC,IAAI,EAAE;QAC7B;IACF;IAEA,+CAA+C;IAC/C,OAAO,KAAK,IAAc,EAAE,aAAqB,EAAE,EAAE,aAAqB,EAAE,EAAE,eAAuB,CAAC,EAAE;QACtG,MAAM,UAAU,IAAI,CAAC,GAAG,CAAC,MAAM;QAC/B,MAAM,UAAU,IAAI,CAAC,GAAG,CAAC,MAAM;QAE/B,sCAAsC;QACtC,MAAM,aAAa,aAAa;QAChC,MAAM,WAAW,QAAQ,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,MAAM,IAAM,OAAO,OAAO,CAAC,EAAE;QAE7E,MAAM,aAAa,IAAI,CAAC,GAAG,CAAC,UAAU;QACtC,MAAM,YAAY,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM,IAAM,OAAO,UAAU,CAAC,EAAE;QAExF,OAAO;YACL,MAAM;YACN,QAAQ;YACR;QACF;IACF;IAEA,kBAAkB;IAClB,OAAO,eAAe,IAAc,EAAE,SAAiB,EAAE,EAAE,SAAiB,CAAC,EAAE;QAC7E,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM;QAC3B,MAAM,QAAQ,IAAI,GAAG,CAAC,CAAC,KAAK;YAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,GAAG,IAAI;YAChC,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,IAAI,KAAK;YAC/E,MAAM,oBAAoB,KAAK,IAAI,CAAC;YAEpC,OAAO;gBACL,OAAO,MAAO,oBAAoB;gBAClC,QAAQ;gBACR,OAAO,MAAO,oBAAoB;YACpC;QACF;QAEA,OAAO;IACT;IAEA,gCAAgC;IAChC,OAAO,sBAAsB,OAA0B,EAAE,WAAmB,EAAE,EAA+C;QAC3H,MAAM,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACrC,MAAM,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QAEnC,MAAM,aAAuB,EAAE;QAC/B,MAAM,UAAoB,EAAE;QAE5B,IAAK,IAAI,IAAI,UAAU,IAAI,QAAQ,MAAM,GAAG,UAAU,IAAK;YACzD,MAAM,cAAc,KAAK,CAAC,EAAE;YAC5B,MAAM,aAAa,IAAI,CAAC,EAAE;YAE1B,2CAA2C;YAC3C,MAAM,eAAe,MAAM,KAAK,CAAC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK,gBAC9C,MAAM,KAAK,CAAC,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK;YAEzE,0CAA0C;YAC1C,MAAM,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK,eAC7C,KAAK,KAAK,CAAC,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK;YAErE,IAAI,cAAc,WAAW,IAAI,CAAC;YAClC,IAAI,WAAW,QAAQ,IAAI,CAAC;QAC9B;QAEA,OAAO;YAAE;YAAS;QAAW;IAC/B;IAEA,kBAAkB;IAClB,OAAO,eAAe,OAA0B,EAAE,SAAiB,EAAE,EAAE;QACrE,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QACzC,MAAM,YAAY,IAAI,CAAC,GAAG,CAAC,SAAS;QACpC,MAAM,gBAAgB,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QACjD,MAAM,mBAAmB,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;QAExD,OAAO;YACL;YACA,eAAe;YACf,aAAa,gBAAgB;YAC7B,cAAc,gBAAgB,mBAAmB;YACjD,aAAa,gBAAgB,mBAAmB;QAClD;IACF;IAEA,yBAAyB;IACzB,OAAO,kBAAkB,OAA0B,EAAwB;QACzE,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACvC,MAAM,aAAmC,EAAE;QAE3C,eAAe;QACf,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC;QACrB,MAAM,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAEtC,IAAI,YAAwC;QAC5C,IAAI,iBAAiB,CAAC,KAAK,EAAE,WAAW,OAAO,CAAC,IAAI;QAEpD,IAAI,aAAa,IAAI;YACnB,YAAY;YACZ,kBAAkB;QACpB,OAAO,IAAI,aAAa,IAAI;YAC1B,YAAY;YACZ,kBAAkB;QACpB,OAAO;YACL,kBAAkB;QACpB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;QACf;QAEA,0BAA0B;QAC1B,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ;QAC/B,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ;QAC/B,MAAM,eAAe,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QAC9C,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC5C,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAE5C,IAAI,WAAuC;QAC3C,IAAI,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,CAAC,eAAe,eAAe,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAE9F,IAAI,eAAe,gBAAgB,eAAe,cAAc;YAC9D,WAAW;YACX,iBAAiB;QACnB,OAAO,IAAI,eAAe,gBAAgB,eAAe,cAAc;YACrE,WAAW;YACX,iBAAiB;QACnB,OAAO;YACL,iBAAiB;QACnB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO,CAAC,eAAe,eAAe,CAAC,IAAI;YAC3C,QAAQ;YACR,aAAa;QACf;QAEA,gBAAgB;QAChB,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC;QAC3B,MAAM,cAAc,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,GAAG,EAAE;QAC3D,MAAM,gBAAgB,SAAS,MAAM,CAAC,SAAS,MAAM,CAAC,MAAM,GAAG,EAAE;QACjE,MAAM,mBAAmB,SAAS,SAAS,CAAC,SAAS,SAAS,CAAC,MAAM,GAAG,EAAE;QAE1E,IAAI,aAAyC;QAC7C,IAAI,kBAAkB,CAAC,MAAM,EAAE,YAAY,OAAO,CAAC,GAAG,UAAU,EAAE,cAAc,OAAO,CAAC,IAAI;QAE5F,IAAI,cAAc,iBAAiB,mBAAmB,GAAG;YACvD,aAAa;YACb,mBAAmB;QACrB,OAAO,IAAI,cAAc,iBAAiB,mBAAmB,GAAG;YAC9D,aAAa;YACb,mBAAmB;QACrB,OAAO;YACL,mBAAmB;QACrB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;QACf;QAEA,kBAAkB;QAClB,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC;QACvC,IAAI,eAA2C;QAC/C,IAAI,oBAAoB,CAAC,QAAQ,EAAE,CAAC,WAAW,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,YAAY,CAAC;QAE1F,IAAI,WAAW,YAAY,EAAE;YAC3B,eAAe;YACf,qBAAqB;QACvB,OAAO,IAAI,WAAW,WAAW,EAAE;YACjC,eAAe;YACf,qBAAqB;QACvB,OAAO;YACL,qBAAqB;QACvB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO,WAAW,WAAW;YAC7B,QAAQ;YACR,aAAa;QACf;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/swingStrategies.ts"], "sourcesContent": ["import { CandlestickData, StockData, SwingTradingAnalysis } from '@/types/trading'\nimport { TechnicalIndicators } from './indicators'\n\nexport interface StrategySetup {\n  strategy: 'overnight_momentum' | 'technical_breakout'\n  confidence: number\n  entryPrice: number\n  stopLoss: number\n  targets: number[]\n  positionSize: number\n  riskAmount: number\n  holdingPeriod: 'overnight' | 'days_to_weeks'\n  keyLevel: number\n  invalidation: string\n  notes: string[]\n}\n\nexport interface SwingSetupCriteria {\n  // Basic filters\n  minPrice: number\n  minVolume: number\n  minMarketCap: number\n  minATRPercent: number\n  \n  // Technical requirements\n  above200SMA: boolean\n  maxDistanceFrom8EMA: number // in ATR units\n  minRoomToResistance: number // in ATR units\n  \n  // Timing\n  scanTimeStart: string // \"12:00\"\n  scanTimeEnd: string   // \"16:00\"\n  \n  // Risk management\n  maxRiskPerTrade: number // percentage of account\n  maxConcurrentPositions: number\n}\n\nexport class SwingTradingStrategies {\n  private static readonly DEFAULT_CRITERIA: SwingSetupCriteria = {\n    minPrice: 5.0,\n    minVolume: 100000, // Relaxed from 500k for testing\n    minMarketCap: *********, // $500M - relaxed from 800M\n    minATRPercent: 1.0, // Relaxed from 1.5\n    above200SMA: false, // Disabled for testing\n    maxDistanceFrom8EMA: 5.0, // Very relaxed from 3.0\n    minRoomToResistance: 0.2, // Very relaxed from 0.5\n    scanTimeStart: \"09:00\", // Extended hours for testing\n    scanTimeEnd: \"20:00\", // Extended hours for testing\n    maxRiskPerTrade: 2.0, // Increased from 1.0\n    maxConcurrentPositions: 5 // Increased from 3\n  }\n\n  // Strategy #1: Overnight Momentum Continuation\n  static analyzeOvernightMomentum(\n    symbol: string,\n    candles: CandlestickData[],\n    quote: StockData,\n    accountSize: number = 100000\n  ): StrategySetup | null {\n    if (candles.length < 50) return null\n\n    const closes = candles.map(c => c.close)\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    const volumes = candles.map(c => c.volume)\n    \n    const currentPrice = quote.price\n    const currentVolume = quote.volume\n    const changePercent = quote.changePercent\n\n    // Calculate technical indicators (adjusted for shorter history)\n    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1)) // Use 50-day instead of 200-day\n    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))\n    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))\n\n    const current50SMA = sma50[sma50.length - 1]\n    const current8EMA = ema8[ema8.length - 1]\n    const currentATR = atr[atr.length - 1]\n\n    // Basic qualification filters (using 50-day SMA instead of 200-day)\n    if (!this.passesBasicFilters(quote, currentVolume, current50SMA, currentPrice)) {\n      return null\n    }\n\n    // Check if it's a top intraday gainer (relaxed for testing)\n    console.log(`📊 ${symbol} momentum check: ${changePercent}% (need ≥0.5%)`)\n    if (changePercent < 0.5) {\n      console.log(`❌ ${symbol} failed momentum: ${changePercent}% < 0.5%`)\n      return null // Minimum 0.5% gain for momentum (relaxed for testing)\n    }\n    console.log(`✅ ${symbol} passed momentum check: ${changePercent}%`)\n\n    // Check distance from 8-EMA (not wildly extended)\n    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA) / currentATR\n    if (distanceFrom8EMA > this.DEFAULT_CRITERIA.maxDistanceFrom8EMA) return null\n\n    // Look for defended intraday level (simplified - using VWAP proxy)\n    const vwap = this.calculateVWAP(candles.slice(-1)[0])\n    const keyLevel = Math.max(vwap, currentPrice * 0.98) // Approximate defended level\n\n    // Check if holding gains (>20% of day's range)\n    const todayHigh = highs[highs.length - 1]\n    const todayLow = lows[lows.length - 1]\n    const dayRange = todayHigh - todayLow\n    const currentFromLow = currentPrice - todayLow\n    const holdingGainsPercent = currentFromLow / dayRange\n\n    console.log(`📊 ${symbol} holding gains: ${(holdingGainsPercent * 100).toFixed(1)}% of range (need ≥20%)`)\n    if (holdingGainsPercent < 0.2) {\n      console.log(`❌ ${symbol} failed holding gains: ${(holdingGainsPercent * 100).toFixed(1)}% < 20%`)\n      return null // Must hold >20% of range (relaxed for testing)\n    }\n    console.log(`✅ ${symbol} passed holding gains check: ${(holdingGainsPercent * 100).toFixed(1)}%`)\n\n    // Calculate room to next resistance\n    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)\n    if (roomToResistance < this.DEFAULT_CRITERIA.minRoomToResistance) return null\n\n    // Position sizing (risk 0.5-1% of account)\n    const riskPercent = 0.75 // 0.75% risk for overnight holds\n    const stopDistance = currentPrice - keyLevel\n    const riskAmount = accountSize * (riskPercent / 100)\n    const positionSize = Math.floor(riskAmount / stopDistance)\n\n    // Targets: Pre-market scale at 3-5%, opening hour at 5-8%\n    const targets = [\n      currentPrice * 1.03, // 3% pre-market target\n      currentPrice * 1.05, // 5% opening hour target\n      currentPrice * 1.08  // 8% extended target\n    ]\n\n    const confidence = this.calculateOvernightConfidence(\n      changePercent, holdingGainsPercent, currentVolume, roomToResistance\n    )\n\n    return {\n      strategy: 'overnight_momentum',\n      confidence,\n      entryPrice: currentPrice,\n      stopLoss: keyLevel,\n      targets,\n      positionSize,\n      riskAmount,\n      holdingPeriod: 'overnight',\n      keyLevel,\n      invalidation: `Daily close below ${keyLevel.toFixed(2)} or gap down below level`,\n      notes: [\n        'Enter final 30-60 min before close',\n        'Exit pre-market on strength or first 45min',\n        'Hard stop if gaps below defended level',\n        'Scale out aggressively if gaps >1 ATR up'\n      ]\n    }\n  }\n\n  // Strategy #2: Technical Breakout Trend-Follow (8-EMA + 200-SMA)\n  static analyzeTechnicalBreakout(\n    symbol: string,\n    candles: CandlestickData[],\n    quote: StockData,\n    accountSize: number = 100000\n  ): StrategySetup | null {\n    if (candles.length < 50) return null\n\n    const closes = candles.map(c => c.close)\n    const volumes = candles.map(c => c.volume)\n    const currentPrice = quote.price\n\n    // Calculate technical indicators (adjusted for shorter history)\n    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1))\n    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))\n    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))\n\n    const current50SMA = sma50[sma50.length - 1]\n    const current8EMA = ema8[ema8.length - 1]\n    const currentATR = atr[atr.length - 1]\n\n    // Basic qualification filters (using 50-day SMA)\n    if (!this.passesBasicFilters(quote, quote.volume, current50SMA, currentPrice)) {\n      return null\n    }\n\n    // Check if in clear uptrend above 50-SMA (adjusted from 200-SMA)\n    if (currentPrice <= current50SMA) return null\n\n    // Check 8-EMA behavior - should be \"hugging\" the 8-EMA\n    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA)\n    const emaDistancePercent = (distanceFrom8EMA / currentPrice) * 100\n    \n    // Should be close to 8-EMA (within 2-3% for quality trend-follow)\n    if (emaDistancePercent > 3.0) return null\n\n    // Check for recent breakout or EMA reclaim\n    const recentEMAReclaim = this.checkEMAReclaim(closes, ema8, 5) // Last 5 days\n    if (!recentEMAReclaim) return null\n\n    // Volume expansion check\n    const avgVolume = TechnicalIndicators.sma(volumes, 20)\n    const currentAvgVolume = avgVolume[avgVolume.length - 1]\n    const volumeExpansion = quote.volume / currentAvgVolume\n    \n    if (volumeExpansion < 1.2) return null // Need some volume expansion\n\n    // Calculate room to next resistance\n    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)\n    if (roomToResistance < 1.5) return null // Need more room for trend-follow\n\n    // Position sizing (risk 1% of account)\n    const riskPercent = 1.0\n    const stopDistance = currentPrice - current8EMA // Stop at 8-EMA break\n    const riskAmount = accountSize * (riskPercent / 100)\n    const positionSize = Math.floor(riskAmount / stopDistance)\n\n    // Targets: Scale at resistance levels\n    const targets = [\n      currentPrice * 1.05, // 5% first target\n      currentPrice * 1.10, // 10% second target\n      currentPrice * 1.15  // 15% extended target\n    ]\n\n    const confidence = this.calculateBreakoutConfidence(\n      emaDistancePercent, volumeExpansion, roomToResistance, quote.changePercent\n    )\n\n    return {\n      strategy: 'technical_breakout',\n      confidence,\n      entryPrice: currentPrice,\n      stopLoss: current8EMA,\n      targets,\n      positionSize,\n      riskAmount,\n      holdingPeriod: 'days_to_weeks',\n      keyLevel: current8EMA,\n      invalidation: `Daily close below 8-EMA (${current8EMA.toFixed(2)})`,\n      notes: [\n        'Enter on afternoon reclaim of 8-EMA',\n        'Add only on higher-low pullbacks to 8-EMA',\n        'Scale partials at resistance levels',\n        'Exit on daily close below 8-EMA'\n      ]\n    }\n  }\n\n  // Helper methods\n  private static passesBasicFilters(\n    quote: StockData,\n    volume: number,\n    sma50: number,\n    price: number\n  ): boolean {\n    const priceCheck = price >= this.DEFAULT_CRITERIA.minPrice\n    const volumeCheck = volume >= this.DEFAULT_CRITERIA.minVolume\n    const marketCapCheck = (quote.marketCap || 1000000000) >= this.DEFAULT_CRITERIA.minMarketCap // Default to 1B for large caps\n    const smaCheck = price > sma50\n\n    console.log(`🔍 Basic filters for ${quote.symbol}:`)\n    console.log(`  💰 Price: ${price} >= ${this.DEFAULT_CRITERIA.minPrice} = ${priceCheck}`)\n    console.log(`  📊 Volume: ${volume} >= ${this.DEFAULT_CRITERIA.minVolume} = ${volumeCheck}`)\n    console.log(`  🏢 Market Cap: ${quote.marketCap || 'default 1B'} >= ${this.DEFAULT_CRITERIA.minMarketCap} = ${marketCapCheck}`)\n    console.log(`  📈 Above SMA50: ${price.toFixed(2)} > ${sma50.toFixed(2)} = ${smaCheck}`)\n\n    const passes = priceCheck && volumeCheck && marketCapCheck && smaCheck\n    console.log(`  ✅ Overall: ${passes}`)\n\n    return passes\n  }\n\n  private static calculateATR(candles: CandlestickData[], period: number): number[] {\n    const trueRanges: number[] = []\n    \n    for (let i = 1; i < candles.length; i++) {\n      const high = candles[i].high\n      const low = candles[i].low\n      const prevClose = candles[i - 1].close\n      \n      const tr = Math.max(\n        high - low,\n        Math.abs(high - prevClose),\n        Math.abs(low - prevClose)\n      )\n      \n      trueRanges.push(tr)\n    }\n    \n    return TechnicalIndicators.sma(trueRanges, period)\n  }\n\n  private static calculateVWAP(candle: CandlestickData): number {\n    // Simplified VWAP calculation using typical price\n    return (candle.high + candle.low + candle.close) / 3\n  }\n\n  private static calculateRoomToResistance(\n    candles: CandlestickData[], \n    currentPrice: number, \n    atr: number\n  ): number {\n    // Find recent highs as resistance levels\n    const recentHighs = candles.slice(-20).map(c => c.high)\n    const maxHigh = Math.max(...recentHighs)\n    const roomToHigh = maxHigh - currentPrice\n    return roomToHigh / atr\n  }\n\n  private static checkEMAReclaim(closes: number[], ema8: number[], lookback: number): boolean {\n    // Check if price recently reclaimed 8-EMA\n    for (let i = Math.max(0, closes.length - lookback); i < closes.length - 1; i++) {\n      if (closes[i] < ema8[i] && closes[i + 1] > ema8[i + 1]) {\n        return true // Found a reclaim\n      }\n    }\n    return false\n  }\n\n  private static calculateOvernightConfidence(\n    changePercent: number,\n    holdingGains: number,\n    volume: number,\n    roomToResistance: number\n  ): number {\n    let confidence = 50\n\n    // Change percent bonus\n    if (changePercent > 5) confidence += 15\n    else if (changePercent > 3) confidence += 10\n    else if (changePercent > 2) confidence += 5\n\n    // Holding gains bonus\n    if (holdingGains > 0.8) confidence += 15\n    else if (holdingGains > 0.6) confidence += 10\n    else if (holdingGains > 0.5) confidence += 5\n\n    // Volume bonus\n    if (volume > 2000000) confidence += 10\n    else if (volume > 1000000) confidence += 5\n\n    // Room to resistance\n    if (roomToResistance > 2) confidence += 10\n    else if (roomToResistance > 1.5) confidence += 5\n\n    return Math.min(95, Math.max(30, confidence))\n  }\n\n  private static calculateBreakoutConfidence(\n    emaDistance: number,\n    volumeExpansion: number,\n    roomToResistance: number,\n    changePercent: number\n  ): number {\n    let confidence = 60\n\n    // EMA proximity bonus (closer is better for trend-follow)\n    if (emaDistance < 1) confidence += 15\n    else if (emaDistance < 2) confidence += 10\n    else if (emaDistance < 3) confidence += 5\n\n    // Volume expansion bonus\n    if (volumeExpansion > 2) confidence += 15\n    else if (volumeExpansion > 1.5) confidence += 10\n    else if (volumeExpansion > 1.2) confidence += 5\n\n    // Room to resistance\n    if (roomToResistance > 3) confidence += 15\n    else if (roomToResistance > 2) confidence += 10\n    else if (roomToResistance > 1.5) confidence += 5\n\n    // Positive momentum\n    if (changePercent > 2) confidence += 5\n\n    return Math.min(95, Math.max(40, confidence))\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;;AAqCO,MAAM;IACX,OAAwB,mBAAuC;QAC7D,UAAU;QACV,WAAW;QACX,cAAc;QACd,eAAe;QACf,aAAa;QACb,qBAAqB;QACrB,qBAAqB;QACrB,eAAe;QACf,aAAa;QACb,iBAAiB;QACjB,wBAAwB,EAAE,mBAAmB;IAC/C,EAAC;IAED,+CAA+C;IAC/C,OAAO,yBACL,MAAc,EACd,OAA0B,EAC1B,KAAgB,EAChB,cAAsB,MAAM,EACN;QACtB,IAAI,QAAQ,MAAM,GAAG,IAAI,OAAO;QAEhC,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACvC,MAAM,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACrC,MAAM,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QACnC,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QAEzC,MAAM,eAAe,MAAM,KAAK;QAChC,MAAM,gBAAgB,MAAM,MAAM;QAClC,MAAM,gBAAgB,MAAM,aAAa;QAEzC,gEAAgE;QAChE,MAAM,QAAQ,iLAAmB,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,OAAO,MAAM,GAAG,IAAI,gCAAgC;;QAC/G,MAAM,OAAO,iLAAmB,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,OAAO,MAAM,GAAG;QACzE,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,GAAG,CAAC,IAAI,QAAQ,MAAM,GAAG;QAErE,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC5C,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,MAAM,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAEtC,oEAAoE;QACpE,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,eAAe,cAAc,eAAe;YAC9E,OAAO;QACT;QAEA,4DAA4D;QAC5D,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,iBAAiB,EAAE,cAAc,cAAc,CAAC;QACzE,IAAI,gBAAgB,KAAK;YACvB,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,kBAAkB,EAAE,cAAc,QAAQ,CAAC;YACnE,OAAO,KAAK,uDAAuD;;QACrE;QACA,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,wBAAwB,EAAE,cAAc,CAAC,CAAC;QAElE,kDAAkD;QAClD,MAAM,mBAAmB,KAAK,GAAG,CAAC,eAAe,eAAe;QAChE,IAAI,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,OAAO;QAEzE,mEAAmE;QACnE,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;QACpD,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM,eAAe,MAAM,6BAA6B;;QAElF,+CAA+C;QAC/C,MAAM,YAAY,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QACzC,MAAM,WAAW,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACtC,MAAM,WAAW,YAAY;QAC7B,MAAM,iBAAiB,eAAe;QACtC,MAAM,sBAAsB,iBAAiB;QAE7C,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,gBAAgB,EAAE,CAAC,sBAAsB,GAAG,EAAE,OAAO,CAAC,GAAG,sBAAsB,CAAC;QACzG,IAAI,sBAAsB,KAAK;YAC7B,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,uBAAuB,EAAE,CAAC,sBAAsB,GAAG,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;YAChG,OAAO,KAAK,gDAAgD;;QAC9D;QACA,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,6BAA6B,EAAE,CAAC,sBAAsB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAEhG,oCAAoC;QACpC,MAAM,mBAAmB,IAAI,CAAC,yBAAyB,CAAC,SAAS,cAAc;QAC/E,IAAI,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,OAAO;QAEzE,2CAA2C;QAC3C,MAAM,cAAc,KAAK,iCAAiC;;QAC1D,MAAM,eAAe,eAAe;QACpC,MAAM,aAAa,cAAc,CAAC,cAAc,GAAG;QACnD,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;QAE7C,0DAA0D;QAC1D,MAAM,UAAU;YACd,eAAe;YACf,eAAe;YACf,eAAe,KAAM,qBAAqB;SAC3C;QAED,MAAM,aAAa,IAAI,CAAC,4BAA4B,CAClD,eAAe,qBAAqB,eAAe;QAGrD,OAAO;YACL,UAAU;YACV;YACA,YAAY;YACZ,UAAU;YACV;YACA;YACA;YACA,eAAe;YACf;YACA,cAAc,CAAC,kBAAkB,EAAE,SAAS,OAAO,CAAC,GAAG,wBAAwB,CAAC;YAChF,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,iEAAiE;IACjE,OAAO,yBACL,MAAc,EACd,OAA0B,EAC1B,KAAgB,EAChB,cAAsB,MAAM,EACN;QACtB,IAAI,QAAQ,MAAM,GAAG,IAAI,OAAO;QAEhC,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACvC,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QACzC,MAAM,eAAe,MAAM,KAAK;QAEhC,gEAAgE;QAChE,MAAM,QAAQ,iLAAmB,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,OAAO,MAAM,GAAG;QAC3E,MAAM,OAAO,iLAAmB,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,OAAO,MAAM,GAAG;QACzE,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,GAAG,CAAC,IAAI,QAAQ,MAAM,GAAG;QAErE,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC5C,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,MAAM,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAEtC,iDAAiD;QACjD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,MAAM,MAAM,EAAE,cAAc,eAAe;YAC7E,OAAO;QACT;QAEA,iEAAiE;QACjE,IAAI,gBAAgB,cAAc,OAAO;QAEzC,uDAAuD;QACvD,MAAM,mBAAmB,KAAK,GAAG,CAAC,eAAe;QACjD,MAAM,qBAAqB,AAAC,mBAAmB,eAAgB;QAE/D,kEAAkE;QAClE,IAAI,qBAAqB,KAAK,OAAO;QAErC,2CAA2C;QAC3C,MAAM,mBAAmB,IAAI,CAAC,eAAe,CAAC,QAAQ,MAAM,GAAG,cAAc;;QAC7E,IAAI,CAAC,kBAAkB,OAAO;QAE9B,yBAAyB;QACzB,MAAM,YAAY,iLAAmB,CAAC,GAAG,CAAC,SAAS;QACnD,MAAM,mBAAmB,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;QACxD,MAAM,kBAAkB,MAAM,MAAM,GAAG;QAEvC,IAAI,kBAAkB,KAAK,OAAO,KAAK,6BAA6B;;QAEpE,oCAAoC;QACpC,MAAM,mBAAmB,IAAI,CAAC,yBAAyB,CAAC,SAAS,cAAc;QAC/E,IAAI,mBAAmB,KAAK,OAAO,KAAK,kCAAkC;;QAE1E,uCAAuC;QACvC,MAAM,cAAc;QACpB,MAAM,eAAe,eAAe,YAAY,sBAAsB;;QACtE,MAAM,aAAa,cAAc,CAAC,cAAc,GAAG;QACnD,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;QAE7C,sCAAsC;QACtC,MAAM,UAAU;YACd,eAAe;YACf,eAAe;YACf,eAAe,KAAM,sBAAsB;SAC5C;QAED,MAAM,aAAa,IAAI,CAAC,2BAA2B,CACjD,oBAAoB,iBAAiB,kBAAkB,MAAM,aAAa;QAG5E,OAAO;YACL,UAAU;YACV;YACA,YAAY;YACZ,UAAU;YACV;YACA;YACA;YACA,eAAe;YACf,UAAU;YACV,cAAc,CAAC,yBAAyB,EAAE,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC;YACnE,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,iBAAiB;IACjB,OAAe,mBACb,KAAgB,EAChB,MAAc,EACd,KAAa,EACb,KAAa,EACJ;QACT,MAAM,aAAa,SAAS,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QAC1D,MAAM,cAAc,UAAU,IAAI,CAAC,gBAAgB,CAAC,SAAS;QAC7D,MAAM,iBAAiB,CAAC,MAAM,SAAS,IAAI,UAAU,KAAK,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,+BAA+B;;QAC5H,MAAM,WAAW,QAAQ;QAEzB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,MAAM,MAAM,CAAC,CAAC,CAAC;QACnD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,MAAM,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,EAAE,YAAY;QACvF,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,OAAO,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,EAAE,aAAa;QAC3F,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,MAAM,SAAS,IAAI,aAAa,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,GAAG,EAAE,gBAAgB;QAC9H,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,MAAM,OAAO,CAAC,GAAG,GAAG,EAAE,MAAM,OAAO,CAAC,GAAG,GAAG,EAAE,UAAU;QAEvF,MAAM,SAAS,cAAc,eAAe,kBAAkB;QAC9D,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ;QAEpC,OAAO;IACT;IAEA,OAAe,aAAa,OAA0B,EAAE,MAAc,EAAY;QAChF,MAAM,aAAuB,EAAE;QAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,IAAI;YAC5B,MAAM,MAAM,OAAO,CAAC,EAAE,CAAC,GAAG;YAC1B,MAAM,YAAY,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK;YAEtC,MAAM,KAAK,KAAK,GAAG,CACjB,OAAO,KACP,KAAK,GAAG,CAAC,OAAO,YAChB,KAAK,GAAG,CAAC,MAAM;YAGjB,WAAW,IAAI,CAAC;QAClB;QAEA,OAAO,iLAAmB,CAAC,GAAG,CAAC,YAAY;IAC7C;IAEA,OAAe,cAAc,MAAuB,EAAU;QAC5D,kDAAkD;QAClD,OAAO,CAAC,OAAO,IAAI,GAAG,OAAO,GAAG,GAAG,OAAO,KAAK,IAAI;IACrD;IAEA,OAAe,0BACb,OAA0B,EAC1B,YAAoB,EACpB,GAAW,EACH;QACR,yCAAyC;QACzC,MAAM,cAAc,QAAQ,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACtD,MAAM,UAAU,KAAK,GAAG,IAAI;QAC5B,MAAM,aAAa,UAAU;QAC7B,OAAO,aAAa;IACtB;IAEA,OAAe,gBAAgB,MAAgB,EAAE,IAAc,EAAE,QAAgB,EAAW;QAC1F,0CAA0C;QAC1C,IAAK,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,OAAO,MAAM,GAAG,WAAW,IAAI,OAAO,MAAM,GAAG,GAAG,IAAK;YAC9E,IAAI,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE;gBACtD,OAAO,KAAK,kBAAkB;;YAChC;QACF;QACA,OAAO;IACT;IAEA,OAAe,6BACb,aAAqB,EACrB,YAAoB,EACpB,MAAc,EACd,gBAAwB,EAChB;QACR,IAAI,aAAa;QAEjB,uBAAuB;QACvB,IAAI,gBAAgB,GAAG,cAAc;aAChC,IAAI,gBAAgB,GAAG,cAAc;aACrC,IAAI,gBAAgB,GAAG,cAAc;QAE1C,sBAAsB;QACtB,IAAI,eAAe,KAAK,cAAc;aACjC,IAAI,eAAe,KAAK,cAAc;aACtC,IAAI,eAAe,KAAK,cAAc;QAE3C,eAAe;QACf,IAAI,SAAS,SAAS,cAAc;aAC/B,IAAI,SAAS,SAAS,cAAc;QAEzC,qBAAqB;QACrB,IAAI,mBAAmB,GAAG,cAAc;aACnC,IAAI,mBAAmB,KAAK,cAAc;QAE/C,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;IACnC;IAEA,OAAe,4BACb,WAAmB,EACnB,eAAuB,EACvB,gBAAwB,EACxB,aAAqB,EACb;QACR,IAAI,aAAa;QAEjB,0DAA0D;QAC1D,IAAI,cAAc,GAAG,cAAc;aAC9B,IAAI,cAAc,GAAG,cAAc;aACnC,IAAI,cAAc,GAAG,cAAc;QAExC,yBAAyB;QACzB,IAAI,kBAAkB,GAAG,cAAc;aAClC,IAAI,kBAAkB,KAAK,cAAc;aACzC,IAAI,kBAAkB,KAAK,cAAc;QAE9C,qBAAqB;QACrB,IAAI,mBAAmB,GAAG,cAAc;aACnC,IAAI,mBAAmB,GAAG,cAAc;aACxC,IAAI,mBAAmB,KAAK,cAAc;QAE/C,oBAAoB;QACpB,IAAI,gBAAgB,GAAG,cAAc;QAErC,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;IACnC;AACF", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/polygon.ts"], "sourcesContent": ["import axios from 'axios'\nimport { CandlestickData, StockData } from '@/types/trading'\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io'\nconst API_KEY = process.env.POLYGON_API_KEY\n\nexport class PolygonAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('Polygon API key is required')\n    }\n  }\n\n  // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      // Use snapshot endpoint for real-time data (available on paid plans)\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      console.log(`🔍 Polygon API response for ${symbol}:`, response.data)\n\n      // Handle different Polygon API response formats\n      let ticker\n      if (response.data.results && response.data.results.length > 0) {\n        // Standard format: { results: [{ ... }] }\n        ticker = response.data.results[0]\n      } else if (response.data.ticker) {\n        // Alternative format: { ticker: { ... } }\n        ticker = response.data.ticker\n      } else {\n        throw new Error(`No data found for ${symbol} - Polygon API returned unexpected format`)\n      }\n\n      if (!ticker) {\n        throw new Error(`No data found for ${symbol}`)\n      }\n      const dayData = ticker.day || {}\n      const prevDayData = ticker.prevDay || {}\n      const lastQuote = ticker.lastQuote || {}\n      const lastTrade = ticker.lastTrade || {}\n\n      // Use the most recent price available\n      const currentPrice = lastTrade.p || dayData.c || prevDayData.c\n      const prevClose = prevDayData.c || dayData.o\n      const change = currentPrice - prevClose\n      const changePercent = (change / prevClose) * 100\n\n      return {\n        symbol: symbol.toUpperCase(),\n        name: ticker.name || symbol.toUpperCase(),\n        price: currentPrice,\n        change,\n        changePercent,\n        volume: dayData.v || 0,\n        marketCap: ticker.market_cap,\n        pe: undefined,\n        dividend: undefined\n      }\n    } catch (error) {\n      console.error('Error fetching stock quote from Polygon:', error)\n\n      // Fallback to previous day data if snapshot fails\n      try {\n        const fallbackResponse = await axios.get(\n          `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`,\n          {\n            params: {\n              adjusted: 'true',\n              apikey: this.apiKey\n            }\n          }\n        )\n\n        const data = fallbackResponse.data.results[0]\n        return {\n          symbol: symbol.toUpperCase(),\n          name: symbol.toUpperCase(),\n          price: data.c,\n          change: data.c - data.o,\n          changePercent: ((data.c - data.o) / data.o) * 100,\n          volume: data.v,\n          marketCap: undefined,\n          pe: undefined,\n          dividend: undefined\n        }\n      } catch (fallbackError) {\n        console.error('Polygon fallback also failed:', fallbackError)\n        throw new Error(`Failed to fetch quote for ${symbol}`)\n      }\n    }\n  }\n\n  // Get historical candlestick data (optimized for paid plans)\n  async getHistoricalData(\n    symbol: string,\n    timespan: 'minute' | 'hour' | 'day' | 'week' | 'month' = 'day',\n    multiplier: number = 1,\n    from: string,\n    to: string\n  ): Promise<CandlestickData[]> {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`,\n        {\n          params: {\n            adjusted: 'true',\n            sort: 'asc',\n            limit: 50000, // Higher limit for paid plans\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      if (!response.data.results || response.data.results.length === 0) {\n        console.warn(`No historical data found for ${symbol}`)\n        return []\n      }\n\n      return response.data.results.map((candle: any) => ({\n        timestamp: candle.t,\n        open: candle.o,\n        high: candle.h,\n        low: candle.l,\n        close: candle.c,\n        volume: candle.v\n      }))\n    } catch (error) {\n      console.error(`Error fetching historical data for ${symbol}:`, error)\n\n      // Log the specific error for debugging\n      if (error.response) {\n        console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`)\n        console.error('Response data:', error.response.data)\n      }\n\n      throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`)\n    }\n  }\n\n  // Get company details\n  async getCompanyDetails(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results\n    } catch (error) {\n      console.error('Error fetching company details:', error)\n      return null\n    }\n  }\n\n  // Get market status\n  async getMarketStatus() {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v1/marketstatus/now`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching market status:', error)\n      return null\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers`,\n        {\n          params: {\n            search: query,\n            market: 'stocks',\n            active: 'true',\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results || []\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n}\n\n// Create a singleton instance\nexport const polygonAPI = new PolygonAPI()\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGA,MAAM,mBAAmB;AACzB,MAAM,UAAU,QAAQ,GAAG,CAAC,eAAe;AAEpC,MAAM;IACH,OAAc;IAEtB,YAAY,MAAe,CAAE;QAC3B,IAAI,CAAC,MAAM,GAAG,UAAU,WAAW;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,oFAAoF;IACpF,MAAM,cAAc,MAAc,EAAsB;QACtD,IAAI;YACF,qEAAqE;YACrE,MAAM,WAAW,MAAM,kLAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,8CAA8C,EAAE,QAAQ,EAC5E;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE,SAAS,IAAI;YAEnE,gDAAgD;YAChD,IAAI;YACJ,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG;gBAC7D,0CAA0C;gBAC1C,SAAS,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE;YACnC,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE;gBAC/B,0CAA0C;gBAC1C,SAAS,SAAS,IAAI,CAAC,MAAM;YAC/B,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,OAAO,yCAAyC,CAAC;YACxF;YAEA,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,QAAQ;YAC/C;YACA,MAAM,UAAU,OAAO,GAAG,IAAI,CAAC;YAC/B,MAAM,cAAc,OAAO,OAAO,IAAI,CAAC;YACvC,MAAM,YAAY,OAAO,SAAS,IAAI,CAAC;YACvC,MAAM,YAAY,OAAO,SAAS,IAAI,CAAC;YAEvC,sCAAsC;YACtC,MAAM,eAAe,UAAU,CAAC,IAAI,QAAQ,CAAC,IAAI,YAAY,CAAC;YAC9D,MAAM,YAAY,YAAY,CAAC,IAAI,QAAQ,CAAC;YAC5C,MAAM,SAAS,eAAe;YAC9B,MAAM,gBAAgB,AAAC,SAAS,YAAa;YAE7C,OAAO;gBACL,QAAQ,OAAO,WAAW;gBAC1B,MAAM,OAAO,IAAI,IAAI,OAAO,WAAW;gBACvC,OAAO;gBACP;gBACA;gBACA,QAAQ,QAAQ,CAAC,IAAI;gBACrB,WAAW,OAAO,UAAU;gBAC5B,IAAI;gBACJ,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAE1D,kDAAkD;YAClD,IAAI;gBACF,MAAM,mBAAmB,MAAM,kLAAK,CAAC,GAAG,CACtC,GAAG,iBAAiB,gBAAgB,EAAE,OAAO,KAAK,CAAC,EACnD;oBACE,QAAQ;wBACN,UAAU;wBACV,QAAQ,IAAI,CAAC,MAAM;oBACrB;gBACF;gBAGF,MAAM,OAAO,iBAAiB,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC7C,OAAO;oBACL,QAAQ,OAAO,WAAW;oBAC1B,MAAM,OAAO,WAAW;oBACxB,OAAO,KAAK,CAAC;oBACb,QAAQ,KAAK,CAAC,GAAG,KAAK,CAAC;oBACvB,eAAe,AAAC,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,GAAI;oBAC9C,QAAQ,KAAK,CAAC;oBACd,WAAW;oBACX,IAAI;oBACJ,UAAU;gBACZ;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,QAAQ;YACvD;QACF;IACF;IAEA,6DAA6D;IAC7D,MAAM,kBACJ,MAAc,EACd,WAAyD,KAAK,EAC9D,aAAqB,CAAC,EACtB,IAAY,EACZ,EAAU,EACkB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,kLAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,gBAAgB,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,EAC5F;gBACE,QAAQ;oBACN,UAAU;oBACV,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;gBAChE,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,QAAQ;gBACrD,OAAO,EAAE;YACX;YAEA,OAAO,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;oBACjD,WAAW,OAAO,CAAC;oBACnB,MAAM,OAAO,CAAC;oBACd,MAAM,OAAO,CAAC;oBACd,KAAK,OAAO,CAAC;oBACb,OAAO,OAAO,CAAC;oBACf,QAAQ,OAAO,CAAC;gBAClB,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAE/D,uCAAuC;YACvC,IAAI,MAAM,QAAQ,EAAE;gBAClB,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;gBAC1F,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACrD;YAEA,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,OAAO,EAAE,EAAE,MAAM,OAAO,EAAE;QACnF;IACF;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,MAAc,EAAE;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,kLAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,sBAAsB,EAAE,QAAQ,EACpD;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,OAAO;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,kLAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,oBAAoB,CAAC,EACzC;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,aAAa,KAAa,EAAE,QAAgB,EAAE,EAAE;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,kLAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,qBAAqB,CAAC,EAC1C;gBACE,QAAQ;oBACN,QAAQ;oBACR,QAAQ;oBACR,QAAQ;oBACR;oBACA,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,EAAE;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;AACF;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/ibkr.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, EventName, Contract } from '@stoqey/ib'\nimport { StockData } from '@/types/trading'\n\nexport interface IBKRConfig {\n  host: string\n  port: number\n  clientId: number\n  paperTrading: boolean\n}\n\nexport interface IBKRMarketData {\n  symbol: string\n  price: number\n  change: number\n  changePercent: number\n  volume: number\n  bid: number\n  ask: number\n  last: number\n  close: number\n  previousClose: number\n  marketCap?: number\n  avgVolume?: number\n}\n\nexport class IBKRAPI {\n  private ib: IBApi\n  private config: IBKRConfig\n  private connected: boolean = false\n  private marketDataRequests: Map<number, { symbol: string; resolve: Function; reject: Function }> = new Map()\n  private nextReqId: number = 1\n\n  constructor(config: IBKRConfig) {\n    this.config = config\n    this.ib = new IBApi({\n      host: config.host,\n      port: config.port,\n      clientId: config.clientId,\n    })\n\n    this.setupEventHandlers()\n  }\n\n  private setupEventHandlers() {\n    // Connection events\n    this.ib.on(EventName.connected, () => {\n      console.log('✅ Connected to IBKR TWS')\n      this.connected = true\n    })\n\n    this.ib.on(EventName.disconnected, () => {\n      console.log('❌ Disconnected from IBKR TWS')\n      this.connected = false\n    })\n\n    this.ib.on(EventName.error, (err, code, reqId) => {\n      console.error(`IBKR Error ${code}:`, err)\n      \n      // Handle market data request errors\n      if (reqId && this.marketDataRequests.has(reqId)) {\n        const request = this.marketDataRequests.get(reqId)!\n        request.reject(new Error(`IBKR Error ${code}: ${err}`))\n        this.marketDataRequests.delete(reqId)\n      }\n    })\n\n    // Market data events\n    this.ib.on(EventName.tickPrice, (reqId: number, tickType: number, price: number, canAutoExecute: number) => {\n      const request = this.marketDataRequests.get(reqId)\n      if (request) {\n        // Store price data - we'll collect multiple ticks before resolving\n        if (!request.data) request.data = {}\n        \n        switch (tickType) {\n          case 1: // Bid\n            request.data.bid = price\n            break\n          case 2: // Ask\n            request.data.ask = price\n            break\n          case 4: // Last\n            request.data.last = price\n            request.data.price = price\n            break\n          case 9: // Close\n            request.data.close = price\n            request.data.previousClose = price\n            break\n        }\n      }\n    })\n\n    this.ib.on(EventName.tickSize, (reqId: number, tickType: number, size: number) => {\n      const request = this.marketDataRequests.get(reqId)\n      if (request) {\n        if (!request.data) request.data = {}\n        \n        switch (tickType) {\n          case 0: // Bid size\n            request.data.bidSize = size\n            break\n          case 3: // Ask size\n            request.data.askSize = size\n            break\n          case 5: // Last size\n            request.data.lastSize = size\n            break\n          case 8: // Volume\n            request.data.volume = size\n            break\n        }\n      }\n    })\n\n    // Market data snapshot complete\n    this.ib.on(EventName.tickSnapshotEnd, (reqId: number) => {\n      const request = this.marketDataRequests.get(reqId)\n      if (request) {\n        const data = request.data || {}\n        const marketData: IBKRMarketData = {\n          symbol: request.symbol,\n          price: data.last || data.close || data.bid || data.ask || 0,\n          change: data.last && data.previousClose ? data.last - data.previousClose : 0,\n          changePercent: data.last && data.previousClose ? ((data.last - data.previousClose) / data.previousClose) * 100 : 0,\n          volume: data.volume || 0,\n          bid: data.bid || 0,\n          ask: data.ask || 0,\n          last: data.last || 0,\n          close: data.close || 0,\n          previousClose: data.previousClose || 0,\n          marketCap: 0,\n          avgVolume: 0\n        }\n        \n        request.resolve(marketData)\n        this.marketDataRequests.delete(reqId)\n      }\n    })\n  }\n\n  async connect(): Promise<boolean> {\n    return new Promise((resolve, reject) => {\n      if (this.connected) {\n        resolve(true)\n        return\n      }\n\n      const timeout = setTimeout(() => {\n        reject(new Error('IBKR connection timeout - ensure TWS/IB Gateway is running'))\n      }, 10000)\n\n      this.ib.once(EventName.connected, () => {\n        clearTimeout(timeout)\n        resolve(true)\n      })\n\n      this.ib.once(EventName.error, (err) => {\n        clearTimeout(timeout)\n        reject(new Error(`IBKR connection failed: ${err}`))\n      })\n\n      console.log(`🔌 Connecting to IBKR TWS at ${this.config.host}:${this.config.port}...`)\n      this.ib.connect()\n    })\n  }\n\n  disconnect(): void {\n    if (this.connected) {\n      this.ib.disconnect()\n    }\n  }\n\n  private createStockContract(symbol: string): Contract {\n    return {\n      symbol: symbol.toUpperCase(),\n      secType: 'STK',\n      exchange: 'SMART',\n      currency: 'USD',\n    }\n  }\n\n  async getMarketData(symbol: string): Promise<IBKRMarketData> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR - ensure TWS/IB Gateway is running')\n    }\n\n    return new Promise((resolve, reject) => {\n      const reqId = this.nextReqId++\n      const contract = this.createStockContract(symbol)\n\n      // Store the request\n      this.marketDataRequests.set(reqId, {\n        symbol: symbol.toUpperCase(),\n        resolve,\n        reject,\n        data: {}\n      })\n\n      // Set timeout for the request\n      setTimeout(() => {\n        if (this.marketDataRequests.has(reqId)) {\n          this.marketDataRequests.delete(reqId)\n          reject(new Error(`Market data request timeout for ${symbol}`))\n        }\n      }, 10000)\n\n      // Request market data snapshot\n      this.ib.reqMktData(reqId, contract, '', true, false, [])\n    })\n  }\n\n  // Convert IBKR market data to StockData format\n  async getStockQuote(symbol: string): Promise<StockData> {\n    const marketData = await this.getMarketData(symbol)\n    \n    return {\n      symbol: marketData.symbol,\n      name: marketData.symbol, // IBKR doesn't provide company names in market data\n      price: marketData.price,\n      change: marketData.change,\n      changePercent: marketData.changePercent,\n      volume: marketData.volume,\n      marketCap: marketData.marketCap,\n      pe: undefined, // Not available in basic market data\n      dividend: undefined // Not available in basic market data\n    }\n  }\n\n  isConnected(): boolean {\n    return this.connected\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAyBO,MAAM;IACH,GAAS;IACT,OAAkB;IAClB,YAAqB,MAAK;IAC1B,qBAA2F,IAAI,MAAK;IACpG,YAAoB,EAAC;IAE7B,YAAY,MAAkB,CAAE;QAC9B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,EAAE,GAAG,IAAI,0LAAK,CAAC;YAClB,MAAM,OAAO,IAAI;YACjB,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;QAC3B;QAEA,IAAI,CAAC,kBAAkB;IACzB;IAEQ,qBAAqB;QAC3B,oBAAoB;QACpB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,8LAAS,CAAC,SAAS,EAAE;YAC9B,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,8LAAS,CAAC,YAAY,EAAE;YACjC,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,8LAAS,CAAC,KAAK,EAAE,CAAC,KAAK,MAAM;YACtC,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,EAAE;YAErC,oCAAoC;YACpC,IAAI,SAAS,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ;gBAC/C,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;gBAC5C,QAAQ,MAAM,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,KAAK;gBACrD,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YACjC;QACF;QAEA,qBAAqB;QACrB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,8LAAS,CAAC,SAAS,EAAE,CAAC,OAAe,UAAkB,OAAe;YAC/E,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;YAC5C,IAAI,SAAS;gBACX,mEAAmE;gBACnE,IAAI,CAAC,QAAQ,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC;gBAEnC,OAAQ;oBACN,KAAK;wBACH,QAAQ,IAAI,CAAC,GAAG,GAAG;wBACnB;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,GAAG,GAAG;wBACnB;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,IAAI,GAAG;wBACpB,QAAQ,IAAI,CAAC,KAAK,GAAG;wBACrB;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,KAAK,GAAG;wBACrB,QAAQ,IAAI,CAAC,aAAa,GAAG;wBAC7B;gBACJ;YACF;QACF;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,8LAAS,CAAC,QAAQ,EAAE,CAAC,OAAe,UAAkB;YAC/D,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;YAC5C,IAAI,SAAS;gBACX,IAAI,CAAC,QAAQ,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC;gBAEnC,OAAQ;oBACN,KAAK;wBACH,QAAQ,IAAI,CAAC,OAAO,GAAG;wBACvB;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,OAAO,GAAG;wBACvB;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,QAAQ,GAAG;wBACxB;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,MAAM,GAAG;wBACtB;gBACJ;YACF;QACF;QAEA,gCAAgC;QAChC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,8LAAS,CAAC,eAAe,EAAE,CAAC;YACrC,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;YAC5C,IAAI,SAAS;gBACX,MAAM,OAAO,QAAQ,IAAI,IAAI,CAAC;gBAC9B,MAAM,aAA6B;oBACjC,QAAQ,QAAQ,MAAM;oBACtB,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;oBAC1D,QAAQ,KAAK,IAAI,IAAI,KAAK,aAAa,GAAG,KAAK,IAAI,GAAG,KAAK,aAAa,GAAG;oBAC3E,eAAe,KAAK,IAAI,IAAI,KAAK,aAAa,GAAG,AAAC,CAAC,KAAK,IAAI,GAAG,KAAK,aAAa,IAAI,KAAK,aAAa,GAAI,MAAM;oBACjH,QAAQ,KAAK,MAAM,IAAI;oBACvB,KAAK,KAAK,GAAG,IAAI;oBACjB,KAAK,KAAK,GAAG,IAAI;oBACjB,MAAM,KAAK,IAAI,IAAI;oBACnB,OAAO,KAAK,KAAK,IAAI;oBACrB,eAAe,KAAK,aAAa,IAAI;oBACrC,WAAW;oBACX,WAAW;gBACb;gBAEA,QAAQ,OAAO,CAAC;gBAChB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YACjC;QACF;IACF;IAEA,MAAM,UAA4B;QAChC,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,QAAQ;gBACR;YACF;YAEA,MAAM,UAAU,WAAW;gBACzB,OAAO,IAAI,MAAM;YACnB,GAAG;YAEH,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,8LAAS,CAAC,SAAS,EAAE;gBAChC,aAAa;gBACb,QAAQ;YACV;YAEA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,8LAAS,CAAC,KAAK,EAAE,CAAC;gBAC7B,aAAa;gBACb,OAAO,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK;YACnD;YAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;YACrF,IAAI,CAAC,EAAE,CAAC,OAAO;QACjB;IACF;IAEA,aAAmB;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,EAAE,CAAC,UAAU;QACpB;IACF;IAEQ,oBAAoB,MAAc,EAAY;QACpD,OAAO;YACL,QAAQ,OAAO,WAAW;YAC1B,SAAS;YACT,UAAU;YACV,UAAU;QACZ;IACF;IAEA,MAAM,cAAc,MAAc,EAA2B;QAC3D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,QAAQ,IAAI,CAAC,SAAS;YAC5B,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC;YAE1C,oBAAoB;YACpB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO;gBACjC,QAAQ,OAAO,WAAW;gBAC1B;gBACA;gBACA,MAAM,CAAC;YACT;YAEA,8BAA8B;YAC9B,WAAW;gBACT,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ;oBACtC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;oBAC/B,OAAO,IAAI,MAAM,CAAC,gCAAgC,EAAE,QAAQ;gBAC9D;YACF,GAAG;YAEH,+BAA+B;YAC/B,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,UAAU,IAAI,MAAM,OAAO,EAAE;QACzD;IACF;IAEA,+CAA+C;IAC/C,MAAM,cAAc,MAAc,EAAsB;QACtD,MAAM,aAAa,MAAM,IAAI,CAAC,aAAa,CAAC;QAE5C,OAAO;YACL,QAAQ,WAAW,MAAM;YACzB,MAAM,WAAW,MAAM;YACvB,OAAO,WAAW,KAAK;YACvB,QAAQ,WAAW,MAAM;YACzB,eAAe,WAAW,aAAa;YACvC,QAAQ,WAAW,MAAM;YACzB,WAAW,WAAW,SAAS;YAC/B,IAAI;YACJ,UAAU,UAAU,qCAAqC;QAC3D;IACF;IAEA,cAAuB;QACrB,OAAO,IAAI,CAAC,SAAS;IACvB;AACF", "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/enhancedSwingScanner.ts"], "sourcesContent": ["import { SwingTradingStrategies, StrategySetup } from './swingStrategies'\nimport { PolygonAPI } from './polygon'\nimport { IBKRAPI, IBKRConfig } from './ibkr'\nimport { StockData, CandlestickData } from '@/types/trading'\nimport { format, subDays } from 'date-fns'\n\nexport interface EnhancedScanResult {\n  symbol: string\n  name: string\n  sector: string\n  quote: StockData\n  overnightSetup?: StrategySetup\n  breakoutSetup?: StrategySetup\n  bestStrategy?: 'overnight_momentum' | 'technical_breakout'\n  overallScore: number\n  rank: number\n  scanTime: string\n  alerts: string[]\n  riskWarnings: string[]\n}\n\nexport interface StrategyScanSummary {\n  totalScanned: number\n  overnightSetups: number\n  breakoutSetups: number\n  bothStrategies: number\n  topSetups: EnhancedScanResult[]\n  scanDuration: number\n  marketConditions: {\n    timeOfDay: string\n    isOptimalScanTime: boolean\n    marketHours: boolean\n  }\n}\n\nexport class EnhancedSwingScanner {\n  private polygonAPI: PolygonAPI\n  private ibkrAPI: IBKRAPI\n  private accountSize: number\n  private useIBKR: boolean = true\n\n  constructor(accountSize: number = 100000) {\n    this.polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)\n\n    // Initialize IBKR API\n    this.ibkrAPI = new IBKRAPI({\n      host: '127.0.0.1',\n      port: 7497, // TWS paper trading port (7497 for paper, 7496 for live)\n      clientId: 1,\n      paperTrading: true\n    })\n\n    this.accountSize = accountSize\n    console.log('🔧 Enhanced Swing Scanner initialized - Using IBKR TWS as primary data source')\n    console.log('🔧 Polygon API as backup - FMP removed completely')\n    console.log('🔧 IBKR API initialized - will attempt connection to TWS on port 7497 when scanning starts')\n  }\n\n  // Main enhanced scanning function\n  async scanWithStrategies(\n    symbols: string[], \n    maxConcurrent: number = 5\n  ): Promise<StrategyScanSummary> {\n    const startTime = Date.now()\n    const results: EnhancedScanResult[] = []\n    const failed: string[] = []\n\n    console.log(`Starting enhanced strategy scan of ${symbols.length} stocks...`)\n\n    // Check if we're in optimal scan time (12:00-16:00 ET)\n    const marketConditions = this.getMarketConditions()\n\n    // Process stocks in batches\n    for (let i = 0; i < symbols.length; i += maxConcurrent) {\n      const batch = symbols.slice(i, i + maxConcurrent)\n      const batchPromises = batch.map(symbol => this.scanSingleStockStrategies(symbol))\n      \n      const batchResults = await Promise.allSettled(batchPromises)\n      \n      batchResults.forEach((result, index) => {\n        const symbol = batch[index]\n        if (result.status === 'fulfilled' && result.value) {\n          results.push(result.value)\n        } else {\n          failed.push(symbol)\n          console.warn(`Failed to scan ${symbol}:`, result.status === 'rejected' ? result.reason : 'Unknown error')\n        }\n      })\n\n      // Rate limiting delay\n      if (i + maxConcurrent < symbols.length) {\n        await new Promise(resolve => setTimeout(resolve, 1000))\n      }\n    }\n\n    // Sort by overall score and assign ranks\n    results.sort((a, b) => b.overallScore - a.overallScore)\n    results.forEach((result, index) => {\n      result.rank = index + 1\n    })\n\n    // Calculate summary statistics\n    const overnightSetups = results.filter(r => r.overnightSetup).length\n    const breakoutSetups = results.filter(r => r.breakoutSetup).length\n    const bothStrategies = results.filter(r => r.overnightSetup && r.breakoutSetup).length\n\n    const scanDuration = Date.now() - startTime\n\n    // Disconnect from IBKR when done\n    if (this.ibkrAPI.isConnected()) {\n      this.ibkrAPI.disconnect()\n      console.log('🔌 Disconnected from IBKR TWS')\n    }\n\n    console.log(`📊 Scan completed: ${results.length} valid results out of ${symbols.length} stocks`)\n    console.log(`📊 Failed scans: ${failed.length} stocks`)\n\n    return {\n      totalScanned: symbols.length,\n      overnightSetups,\n      breakoutSetups,\n      bothStrategies,\n      topSetups: results.slice(0, 25), // Top 25 setups\n      scanDuration,\n      marketConditions\n    }\n  }\n\n  // Scan individual stock for both strategies\n  private async scanSingleStockStrategies(symbol: string): Promise<EnhancedScanResult | null> {\n    try {\n      console.log(`🔍 Starting scan for ${symbol}...`)\n\n      // Get stock quote and historical data\n      // Use IBKR as primary (no rate limits), fallback to Polygon, then Historical Data\n      let quote\n      let historicalData\n\n      if (this.useIBKR) {\n        try {\n          console.log(`📊 Using IBKR TWS for ${symbol} quote (no rate limits)...`)\n\n          // Connect to IBKR if not already connected\n          if (!this.ibkrAPI.isConnected()) {\n            await this.ibkrAPI.connect()\n          }\n\n          [quote, historicalData] = await Promise.all([\n            this.ibkrAPI.getStockQuote(symbol),\n            this.getHistoricalData(symbol)\n          ])\n          console.log(`✅ Successfully retrieved IBKR quote for ${symbol}: $${quote.price}`)\n        } catch (ibkrError: any) {\n          console.log(`⚠️ IBKR failed for ${symbol}: ${ibkrError.message}`)\n          if (ibkrError.message.includes('connection') || ibkrError.message.includes('TWS')) {\n            console.log(`🔌 TWS connection issue - ensure TWS is running and API is enabled`)\n            console.log(`🔧 Check: File → Global Configuration → API → Settings → Enable ActiveX and Socket Clients`)\n          }\n          console.log(`🔄 Falling back to Polygon API...`)\n          this.useIBKR = false // Disable IBKR for subsequent requests\n        }\n      }\n\n      // Fallback to Polygon if IBKR failed or not enabled\n      if (!this.useIBKR) {\n        try {\n          console.log(`📊 Using Polygon API for ${symbol} quote...`)\n          [quote, historicalData] = await Promise.all([\n            this.polygonAPI.getStockQuote(symbol),\n            this.getHistoricalData(symbol)\n          ])\n          console.log(`✅ Successfully retrieved Polygon quote for ${symbol}: $${quote.price}`)\n        } catch (polygonError: any) {\n          console.log(`❌ Polygon failed for ${symbol}: ${polygonError.message}`)\n          console.log(`🔄 Using historical data fallback (no FMP)...`)\n          // Get historical data and use latest price as quote\n          historicalData = await this.getHistoricalData(symbol)\n          if (historicalData && historicalData.length > 0) {\n            const latest = historicalData[historicalData.length - 1]\n            const previous = historicalData[historicalData.length - 2]\n            const change = previous ? latest.close - previous.close : 0\n            const changePercent = previous ? ((change / previous.close) * 100) : 0\n\n            quote = {\n              symbol: symbol,\n              name: symbol,\n              price: latest.close,\n              change: change,\n              changePercent: changePercent,\n              volume: latest.volume,\n              marketCap: 0, // Not available from historical data\n              pe: 0, // Not available from historical data\n              dividend: undefined\n            }\n            console.log(`✅ Using historical data fallback for ${symbol}: $${latest.close}`)\n          } else {\n            throw new Error(`All data sources failed for ${symbol}`)\n          }\n        }\n      }\n\n      console.log(`📊 Quote for ${symbol}:`, {\n        symbol: quote.symbol,\n        price: quote.price,\n        volume: quote.volume,\n        changePercent: quote.changePercent\n      })\n      console.log(`📈 Historical data length for ${symbol}:`, historicalData?.length)\n\n      if (!quote) {\n        throw new Error(`No quote data available for ${symbol}`)\n      }\n\n      if (!historicalData || historicalData.length < 30) {\n        throw new Error(`Insufficient historical data for ${symbol} - need at least 30 days, got ${historicalData?.length || 0}`)\n      }\n\n      console.log(`🧮 Analyzing overnight momentum for ${symbol}...`)\n      // Analyze both strategies\n      const overnightSetup = SwingTradingStrategies.analyzeOvernightMomentum(\n        symbol, historicalData, quote, this.accountSize\n      )\n      console.log(`✅ Overnight setup for ${symbol}:`, overnightSetup ? 'Found' : 'None')\n\n      console.log(`🧮 Analyzing technical breakout for ${symbol}...`)\n      const breakoutSetup = SwingTradingStrategies.analyzeTechnicalBreakout(\n        symbol, historicalData, quote, this.accountSize\n      )\n      console.log(`✅ Breakout setup for ${symbol}:`, breakoutSetup ? 'Found' : 'None')\n\n      // Skip if no valid setups\n      if (!overnightSetup && !breakoutSetup) {\n        console.log(`⚠️ No valid setups found for ${symbol}`)\n        return null\n      }\n\n      // Determine best strategy and overall score\n      const { bestStrategy, overallScore } = this.calculateBestStrategy(overnightSetup, breakoutSetup)\n\n      // Generate alerts and warnings\n      const alerts = this.generateAlerts(overnightSetup, breakoutSetup, quote)\n      const riskWarnings = this.generateRiskWarnings(overnightSetup, breakoutSetup, quote)\n\n      const result = {\n        symbol,\n        name: quote.name,\n        sector: this.getSectorForSymbol(symbol),\n        quote,\n        overnightSetup: overnightSetup || undefined,\n        breakoutSetup: breakoutSetup || undefined,\n        bestStrategy,\n        overallScore,\n        rank: 0, // Will be set after sorting\n        scanTime: new Date().toISOString(),\n        alerts,\n        riskWarnings\n      }\n\n      console.log(`✅ Successfully scanned ${symbol} with score ${overallScore}`)\n      return result\n    } catch (error) {\n      console.error(`❌ Error scanning ${symbol}:`, error)\n      if (error instanceof Error) {\n        console.error(`❌ Error message: ${error.message}`)\n        console.error(`❌ Error stack: ${error.stack}`)\n      }\n      return null\n    }\n  }\n\n  // Get historical data with optimized API usage\n  private async getHistoricalData(symbol: string): Promise<CandlestickData[]> {\n    const to = format(new Date(), 'yyyy-MM-dd')\n    const from = format(subDays(new Date(), 100), 'yyyy-MM-dd') // 100 days should be sufficient\n\n    try {\n      console.log(`Fetching historical data for ${symbol} from ${from} to ${to}`)\n      const data = await this.polygonAPI.getHistoricalData(symbol, 'day', 1, from, to)\n\n      if (data.length === 0) {\n        console.warn(`No historical data returned for ${symbol}`)\n        throw new Error('No historical data available')\n      }\n\n      console.log(`Successfully fetched ${data.length} days of data for ${symbol}`)\n      return data\n    } catch (error) {\n      console.error(`Failed to fetch historical data for ${symbol}:`, error)\n      throw error\n    }\n  }\n\n  // Calculate best strategy and overall score\n  private calculateBestStrategy(\n    overnight?: StrategySetup | null, \n    breakout?: StrategySetup | null\n  ): { bestStrategy?: 'overnight_momentum' | 'technical_breakout', overallScore: number } {\n    if (!overnight && !breakout) {\n      return { overallScore: 0 }\n    }\n\n    if (overnight && !breakout) {\n      return { bestStrategy: 'overnight_momentum', overallScore: overnight.confidence }\n    }\n\n    if (breakout && !overnight) {\n      return { bestStrategy: 'technical_breakout', overallScore: breakout.confidence }\n    }\n\n    if (overnight && breakout) {\n      // Both strategies valid - choose higher confidence\n      if (overnight.confidence > breakout.confidence) {\n        return { bestStrategy: 'overnight_momentum', overallScore: overnight.confidence + 5 } // Bonus for multiple setups\n      } else {\n        return { bestStrategy: 'technical_breakout', overallScore: breakout.confidence + 5 }\n      }\n    }\n\n    return { overallScore: 0 }\n  }\n\n  // Generate trading alerts\n  private generateAlerts(\n    overnight?: StrategySetup | null,\n    breakout?: StrategySetup | null,\n    quote?: StockData\n  ): string[] {\n    const alerts: string[] = []\n\n    if (overnight) {\n      alerts.push(`🚀 OVERNIGHT MOMENTUM: Entry ${overnight.entryPrice.toFixed(2)}, Target ${overnight.targets[0].toFixed(2)}`)\n      alerts.push(`⏰ Execute in final 30-60 min before close`)\n      alerts.push(`🛑 Stop: ${overnight.stopLoss.toFixed(2)} (${((overnight.entryPrice - overnight.stopLoss) / overnight.entryPrice * 100).toFixed(1)}% risk)`)\n    }\n\n    if (breakout) {\n      alerts.push(`📈 BREAKOUT SETUP: Entry ${breakout.entryPrice.toFixed(2)}, riding 8-EMA`)\n      alerts.push(`🎯 Targets: ${breakout.targets.map(t => t.toFixed(2)).join(', ')}`)\n      alerts.push(`🛑 Stop: Daily close below ${breakout.stopLoss.toFixed(2)}`)\n    }\n\n    if (quote && quote.changePercent > 5) {\n      alerts.push(`🔥 Strong momentum: +${quote.changePercent.toFixed(1)}% today`)\n    }\n\n    return alerts\n  }\n\n  // Generate risk warnings\n  private generateRiskWarnings(\n    overnight?: StrategySetup | null,\n    breakout?: StrategySetup | null,\n    quote?: StockData\n  ): string[] {\n    const warnings: string[] = []\n\n    if (overnight) {\n      warnings.push(`⚠️ Overnight gap risk - size down vs intraday trades`)\n      if (quote && quote.changePercent > 8) {\n        warnings.push(`⚠️ Extended move (+${quote.changePercent.toFixed(1)}%) - consider smaller size`)\n      }\n    }\n\n    if (quote && (quote.marketCap || 0) < 1000000000) {\n      warnings.push(`⚠️ Small cap overnight risk - volatile gaps possible`)\n    }\n\n    if (quote && quote.volume < 1000000) {\n      warnings.push(`⚠️ Lower volume - may have liquidity issues`)\n    }\n\n    return warnings\n  }\n\n  // Get market conditions\n  private getMarketConditions() {\n    const now = new Date()\n    const currentHour = now.getHours()\n    const currentMinute = now.getMinutes()\n    const timeDecimal = currentHour + currentMinute / 60\n\n    // Convert to ET (simplified - doesn't handle DST)\n    const etTime = timeDecimal - 5 // Assuming EST\n\n    return {\n      timeOfDay: `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`,\n      isOptimalScanTime: etTime >= 12 && etTime <= 16, // 12:00-16:00 ET\n      marketHours: etTime >= 9.5 && etTime <= 16 // 9:30-16:00 ET\n    }\n  }\n\n  // Get sector for symbol (reuse from previous implementation)\n  private getSectorForSymbol(symbol: string): string {\n    const techSymbols = ['MSFT', 'NVDA', 'GOOG', 'GOOGL', 'META', 'AVGO', 'TSM', 'ORCL', 'CSCO', 'AMD', 'ASML', 'MU', 'LRCX', 'PLTR', 'APP', 'NET', 'DDOG', 'ZS', 'SHOP', 'SOUN', 'IONQ', 'RGTI', 'RIOT', 'HUT', 'IREN', 'ASTS', 'NBIS']\n    const financialSymbols = ['JPM', 'BAC', 'MS', 'SCHW', 'C', 'HOOD', 'SOFI', 'TIGR', 'FUTU']\n    const healthcareSymbols = ['JNJ', 'ABBV', 'MRK', 'GILD']\n    const industrialSymbols = ['GE', 'CAT', 'BA', 'GEV', 'UAL', 'VRT', 'RKLB']\n    const materialsSymbols = ['AEM', 'NEM', 'PAAS', 'BTG', 'HL', 'MP', 'AG']\n    const consumerSymbols = ['AMZN', 'DIS', 'SBUX', 'MO', 'DASH', 'GM', 'NCLH', 'CELH', 'LEVI', 'ELF', 'ETSY', 'W']\n    const communicationSymbols = ['NFLX', 'RBLX', 'BILI']\n    const energySymbols = ['CEG', 'VST', 'CCJ']\n\n    if (techSymbols.includes(symbol)) return 'Technology'\n    if (financialSymbols.includes(symbol)) return 'Financial Services'\n    if (healthcareSymbols.includes(symbol)) return 'Healthcare'\n    if (industrialSymbols.includes(symbol)) return 'Industrial'\n    if (materialsSymbols.includes(symbol)) return 'Materials'\n    if (consumerSymbols.includes(symbol)) return 'Consumer'\n    if (communicationSymbols.includes(symbol)) return 'Communication Services'\n    if (energySymbols.includes(symbol)) return 'Energy'\n    \n    return 'Other'\n  }\n\n  // Quick scan with strategies\n  async quickStrategyScan(prioritySymbols: string[]): Promise<EnhancedScanResult[]> {\n    const summary = await this.scanWithStrategies(prioritySymbols, 8)\n    return summary.topSetups\n  }\n}\n\n// Create singleton instance\nexport const enhancedSwingScanner = new EnhancedSwingScanner()\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAEA;AAAA;;;;;AA+BO,MAAM;IACH,WAAsB;IACtB,QAAgB;IAChB,YAAmB;IACnB,UAAmB,KAAI;IAE/B,YAAY,cAAsB,MAAM,CAAE;QACxC,IAAI,CAAC,UAAU,GAAG,IAAI,qKAAU,CAAC,QAAQ,GAAG,CAAC,eAAe;QAE5D,sBAAsB;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,+JAAO,CAAC;YACzB,MAAM;YACN,MAAM;YACN,UAAU;YACV,cAAc;QAChB;QAEA,IAAI,CAAC,WAAW,GAAG;QACnB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;IACd;IAEA,kCAAkC;IAClC,MAAM,mBACJ,OAAiB,EACjB,gBAAwB,CAAC,EACK;QAC9B,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,UAAgC,EAAE;QACxC,MAAM,SAAmB,EAAE;QAE3B,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ,MAAM,CAAC,UAAU,CAAC;QAE5E,uDAAuD;QACvD,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;QAEjD,4BAA4B;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,cAAe;YACtD,MAAM,QAAQ,QAAQ,KAAK,CAAC,GAAG,IAAI;YACnC,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,yBAAyB,CAAC;YAEzE,MAAM,eAAe,MAAM,QAAQ,UAAU,CAAC;YAE9C,aAAa,OAAO,CAAC,CAAC,QAAQ;gBAC5B,MAAM,SAAS,KAAK,CAAC,MAAM;gBAC3B,IAAI,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,EAAE;oBACjD,QAAQ,IAAI,CAAC,OAAO,KAAK;gBAC3B,OAAO;oBACL,OAAO,IAAI,CAAC;oBACZ,QAAQ,IAAI,CAAC,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG;gBAC3F;YACF;YAEA,sBAAsB;YACtB,IAAI,IAAI,gBAAgB,QAAQ,MAAM,EAAE;gBACtC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;QAEA,yCAAyC;QACzC,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;QACtD,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,OAAO,IAAI,GAAG,QAAQ;QACxB;QAEA,+BAA+B;QAC/B,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,EAAE,MAAM;QACpE,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,EAAE,MAAM;QAClE,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,IAAI,EAAE,aAAa,EAAE,MAAM;QAEtF,MAAM,eAAe,KAAK,GAAG,KAAK;QAElC,iCAAiC;QACjC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI;YAC9B,IAAI,CAAC,OAAO,CAAC,UAAU;YACvB,QAAQ,GAAG,CAAC;QACd;QAEA,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ,MAAM,CAAC,sBAAsB,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;QAChG,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC;QAEtD,OAAO;YACL,cAAc,QAAQ,MAAM;YAC5B;YACA;YACA;YACA,WAAW,QAAQ,KAAK,CAAC,GAAG;YAC5B;YACA;QACF;IACF;IAEA,4CAA4C;IAC5C,MAAc,0BAA0B,MAAc,EAAsC;QAC1F,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,GAAG,CAAC;YAE/C,sCAAsC;YACtC,kFAAkF;YAClF,IAAI;YACJ,IAAI;YAEJ,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,IAAI;oBACF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,OAAO,0BAA0B,CAAC;oBAEvE,2CAA2C;oBAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI;wBAC/B,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO;oBAC5B;oBAEA,CAAC,OAAO,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC1C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;wBAC3B,IAAI,CAAC,iBAAiB,CAAC;qBACxB;oBACD,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,OAAO,GAAG,EAAE,MAAM,KAAK,EAAE;gBAClF,EAAE,OAAO,WAAgB;oBACvB,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,OAAO,EAAE,EAAE,UAAU,OAAO,EAAE;oBAChE,IAAI,UAAU,OAAO,CAAC,QAAQ,CAAC,iBAAiB,UAAU,OAAO,CAAC,QAAQ,CAAC,QAAQ;wBACjF,QAAQ,GAAG,CAAC,CAAC,kEAAkE,CAAC;wBAChF,QAAQ,GAAG,CAAC,CAAC,0FAA0F,CAAC;oBAC1G;oBACA,QAAQ,GAAG,CAAC,CAAC,iCAAiC,CAAC;oBAC/C,IAAI,CAAC,OAAO,GAAG,OAAM,uCAAuC;gBAC9D;YACF;YAEA,oDAAoD;YACpD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,IAAI;oBACF,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,OAAO,SAAS,CAAC,CACzD,CAAC,OAAO,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC1C,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;wBAC9B,IAAI,CAAC,iBAAiB,CAAC;qBACxB;oBACD,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,OAAO,GAAG,EAAE,MAAM,KAAK,EAAE;gBACrF,EAAE,OAAO,cAAmB;oBAC1B,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,aAAa,OAAO,EAAE;oBACrE,QAAQ,GAAG,CAAC,CAAC,6CAA6C,CAAC;oBAC3D,oDAAoD;oBACpD,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;oBAC9C,IAAI,kBAAkB,eAAe,MAAM,GAAG,GAAG;wBAC/C,MAAM,SAAS,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE;wBACxD,MAAM,WAAW,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE;wBAC1D,MAAM,SAAS,WAAW,OAAO,KAAK,GAAG,SAAS,KAAK,GAAG;wBAC1D,MAAM,gBAAgB,WAAY,AAAC,SAAS,SAAS,KAAK,GAAI,MAAO;wBAErE,QAAQ;4BACN,QAAQ;4BACR,MAAM;4BACN,OAAO,OAAO,KAAK;4BACnB,QAAQ;4BACR,eAAe;4BACf,QAAQ,OAAO,MAAM;4BACrB,WAAW;4BACX,IAAI;4BACJ,UAAU;wBACZ;wBACA,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,OAAO,GAAG,EAAE,OAAO,KAAK,EAAE;oBAChF,OAAO;wBACL,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,QAAQ;oBACzD;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,EAAE;gBACrC,QAAQ,MAAM,MAAM;gBACpB,OAAO,MAAM,KAAK;gBAClB,QAAQ,MAAM,MAAM;gBACpB,eAAe,MAAM,aAAa;YACpC;YACA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,OAAO,CAAC,CAAC,EAAE,gBAAgB;YAExE,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,QAAQ;YACzD;YAEA,IAAI,CAAC,kBAAkB,eAAe,MAAM,GAAG,IAAI;gBACjD,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,OAAO,8BAA8B,EAAE,gBAAgB,UAAU,GAAG;YAC1H;YAEA,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,OAAO,GAAG,CAAC;YAC9D,0BAA0B;YAC1B,MAAM,iBAAiB,yLAAsB,CAAC,wBAAwB,CACpE,QAAQ,gBAAgB,OAAO,IAAI,CAAC,WAAW;YAEjD,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC,EAAE,iBAAiB,UAAU;YAE3E,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,OAAO,GAAG,CAAC;YAC9D,MAAM,gBAAgB,yLAAsB,CAAC,wBAAwB,CACnE,QAAQ,gBAAgB,OAAO,IAAI,CAAC,WAAW;YAEjD,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC,EAAE,gBAAgB,UAAU;YAEzE,0BAA0B;YAC1B,IAAI,CAAC,kBAAkB,CAAC,eAAe;gBACrC,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,QAAQ;gBACpD,OAAO;YACT;YAEA,4CAA4C;YAC5C,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,gBAAgB;YAElF,+BAA+B;YAC/B,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,gBAAgB,eAAe;YAClE,MAAM,eAAe,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,eAAe;YAE9E,MAAM,SAAS;gBACb;gBACA,MAAM,MAAM,IAAI;gBAChB,QAAQ,IAAI,CAAC,kBAAkB,CAAC;gBAChC;gBACA,gBAAgB,kBAAkB;gBAClC,eAAe,iBAAiB;gBAChC;gBACA;gBACA,MAAM;gBACN,UAAU,IAAI,OAAO,WAAW;gBAChC;gBACA;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,OAAO,YAAY,EAAE,cAAc;YACzE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,EAAE;YAC7C,IAAI,iBAAiB,OAAO;gBAC1B,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,MAAM,OAAO,EAAE;gBACjD,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,MAAM,KAAK,EAAE;YAC/C;YACA,OAAO;QACT;IACF;IAEA,+CAA+C;IAC/C,MAAc,kBAAkB,MAAc,EAA8B;QAC1E,MAAM,KAAK,IAAA,iMAAM,EAAC,IAAI,QAAQ;QAC9B,MAAM,OAAO,IAAA,iMAAM,EAAC,IAAA,mLAAO,EAAC,IAAI,QAAQ,MAAM,cAAc,gCAAgC;;QAE5F,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO,MAAM,EAAE,KAAK,IAAI,EAAE,IAAI;YAC1E,MAAM,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,OAAO,GAAG,MAAM;YAE7E,IAAI,KAAK,MAAM,KAAK,GAAG;gBACrB,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,QAAQ;gBACxD,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,KAAK,MAAM,CAAC,kBAAkB,EAAE,QAAQ;YAC5E,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,OAAO,CAAC,CAAC,EAAE;YAChE,MAAM;QACR;IACF;IAEA,4CAA4C;IACpC,sBACN,SAAgC,EAChC,QAA+B,EACuD;QACtF,IAAI,CAAC,aAAa,CAAC,UAAU;YAC3B,OAAO;gBAAE,cAAc;YAAE;QAC3B;QAEA,IAAI,aAAa,CAAC,UAAU;YAC1B,OAAO;gBAAE,cAAc;gBAAsB,cAAc,UAAU,UAAU;YAAC;QAClF;QAEA,IAAI,YAAY,CAAC,WAAW;YAC1B,OAAO;gBAAE,cAAc;gBAAsB,cAAc,SAAS,UAAU;YAAC;QACjF;QAEA,IAAI,aAAa,UAAU;YACzB,mDAAmD;YACnD,IAAI,UAAU,UAAU,GAAG,SAAS,UAAU,EAAE;gBAC9C,OAAO;oBAAE,cAAc;oBAAsB,cAAc,UAAU,UAAU,GAAG;gBAAE,EAAE,4BAA4B;;YACpH,OAAO;gBACL,OAAO;oBAAE,cAAc;oBAAsB,cAAc,SAAS,UAAU,GAAG;gBAAE;YACrF;QACF;QAEA,OAAO;YAAE,cAAc;QAAE;IAC3B;IAEA,0BAA0B;IAClB,eACN,SAAgC,EAChC,QAA+B,EAC/B,KAAiB,EACP;QACV,MAAM,SAAmB,EAAE;QAE3B,IAAI,WAAW;YACb,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,UAAU,UAAU,CAAC,OAAO,CAAC,GAAG,SAAS,EAAE,UAAU,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI;YACxH,OAAO,IAAI,CAAC,CAAC,yCAAyC,CAAC;YACvD,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,UAAU,UAAU,GAAG,UAAU,QAAQ,IAAI,UAAU,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;QAC1J;QAEA,IAAI,UAAU;YACZ,OAAO,IAAI,CAAC,CAAC,yBAAyB,EAAE,SAAS,UAAU,CAAC,OAAO,CAAC,GAAG,cAAc,CAAC;YACtF,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO;YAC/E,OAAO,IAAI,CAAC,CAAC,2BAA2B,EAAE,SAAS,QAAQ,CAAC,OAAO,CAAC,IAAI;QAC1E;QAEA,IAAI,SAAS,MAAM,aAAa,GAAG,GAAG;YACpC,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,MAAM,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;QAC7E;QAEA,OAAO;IACT;IAEA,yBAAyB;IACjB,qBACN,SAAgC,EAChC,QAA+B,EAC/B,KAAiB,EACP;QACV,MAAM,WAAqB,EAAE;QAE7B,IAAI,WAAW;YACb,SAAS,IAAI,CAAC,CAAC,oDAAoD,CAAC;YACpE,IAAI,SAAS,MAAM,aAAa,GAAG,GAAG;gBACpC,SAAS,IAAI,CAAC,CAAC,mBAAmB,EAAE,MAAM,aAAa,CAAC,OAAO,CAAC,GAAG,0BAA0B,CAAC;YAChG;QACF;QAEA,IAAI,SAAS,CAAC,MAAM,SAAS,IAAI,CAAC,IAAI,YAAY;YAChD,SAAS,IAAI,CAAC,CAAC,oDAAoD,CAAC;QACtE;QAEA,IAAI,SAAS,MAAM,MAAM,GAAG,SAAS;YACnC,SAAS,IAAI,CAAC,CAAC,2CAA2C,CAAC;QAC7D;QAEA,OAAO;IACT;IAEA,wBAAwB;IAChB,sBAAsB;QAC5B,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,IAAI,QAAQ;QAChC,MAAM,gBAAgB,IAAI,UAAU;QACpC,MAAM,cAAc,cAAc,gBAAgB;QAElD,kDAAkD;QAClD,MAAM,SAAS,cAAc,EAAE,eAAe;;QAE9C,OAAO;YACL,WAAW,GAAG,YAAY,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,cAAc,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;YACpG,mBAAmB,UAAU,MAAM,UAAU;YAC7C,aAAa,UAAU,OAAO,UAAU,GAAG,gBAAgB;QAC7D;IACF;IAEA,6DAA6D;IACrD,mBAAmB,MAAc,EAAU;QACjD,MAAM,cAAc;YAAC;YAAQ;YAAQ;YAAQ;YAAS;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAM;YAAQ;YAAQ;YAAO;YAAO;YAAQ;YAAM;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAO;YAAQ;YAAQ;SAAO;QACpO,MAAM,mBAAmB;YAAC;YAAO;YAAO;YAAM;YAAQ;YAAK;YAAQ;YAAQ;YAAQ;SAAO;QAC1F,MAAM,oBAAoB;YAAC;YAAO;YAAQ;YAAO;SAAO;QACxD,MAAM,oBAAoB;YAAC;YAAM;YAAO;YAAM;YAAO;YAAO;YAAO;SAAO;QAC1E,MAAM,mBAAmB;YAAC;YAAO;YAAO;YAAQ;YAAO;YAAM;YAAM;SAAK;QACxE,MAAM,kBAAkB;YAAC;YAAQ;YAAO;YAAQ;YAAM;YAAQ;YAAM;YAAQ;YAAQ;YAAQ;YAAO;YAAQ;SAAI;QAC/G,MAAM,uBAAuB;YAAC;YAAQ;YAAQ;SAAO;QACrD,MAAM,gBAAgB;YAAC;YAAO;YAAO;SAAM;QAE3C,IAAI,YAAY,QAAQ,CAAC,SAAS,OAAO;QACzC,IAAI,iBAAiB,QAAQ,CAAC,SAAS,OAAO;QAC9C,IAAI,kBAAkB,QAAQ,CAAC,SAAS,OAAO;QAC/C,IAAI,kBAAkB,QAAQ,CAAC,SAAS,OAAO;QAC/C,IAAI,iBAAiB,QAAQ,CAAC,SAAS,OAAO;QAC9C,IAAI,gBAAgB,QAAQ,CAAC,SAAS,OAAO;QAC7C,IAAI,qBAAqB,QAAQ,CAAC,SAAS,OAAO;QAClD,IAAI,cAAc,QAAQ,CAAC,SAAS,OAAO;QAE3C,OAAO;IACT;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB,eAAyB,EAAiC;QAChF,MAAM,UAAU,MAAM,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;QAC/D,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,MAAM,uBAAuB,IAAI", "debugId": null}}, {"offset": {"line": 1889, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/app/api/scanner/strategies/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { enhancedSwingScanner } from '@/lib/enhancedSwingScanner'\nimport { PRIORITY_SYMBOLS, ALL_SYMBOLS } from '@/data/watchlist'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const scanType = searchParams.get('type') || 'quick' // quick, full\n    const accountSize = parseInt(searchParams.get('accountSize') || '100000')\n    const limit = parseInt(searchParams.get('limit') || '20')\n    \n    console.log(`Starting ${scanType} strategy scan...`)\n    \n    // Set account size for position sizing\n    const scanner = new (require('@/lib/enhancedSwingScanner').EnhancedSwingScanner)(accountSize)\n    \n    let summary\n    if (scanType === 'full') {\n      summary = await scanner.scanWithStrategies(ALL_SYMBOLS, 1) // Sequential to prevent rate limiting\n    } else {\n      // Use ALL_SYMBOLS for quick scan too, but with higher concurrency\n      summary = await scanner.scanWithStrategies(ALL_SYMBOLS, 3) // Scan all 70+ stocks with moderate concurrency\n    }\n    \n    // Limit results if requested\n    const limitedSummary = {\n      ...summary,\n      topSetups: summary.topSetups?.slice(0, limit) || []\n    }\n\n    console.log('📊 API returning summary:', limitedSummary) // Debug logging\n\n    return NextResponse.json(limitedSummary)\n  } catch (error) {\n    console.error('Error in strategy scanner API:', error)\n    return NextResponse.json(\n      { error: 'Failed to perform strategy scan' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC,WAAW,QAAQ,cAAc;;QACnE,MAAM,cAAc,SAAS,aAAa,GAAG,CAAC,kBAAkB;QAChE,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,iBAAiB,CAAC;QAEnD,uCAAuC;QACvC,MAAM,UAAU,IAAK,uHAAsC,oBAAoB,CAAE;QAEjF,IAAI;QACJ,IAAI,aAAa,QAAQ;YACvB,UAAU,MAAM,QAAQ,kBAAkB,CAAC,yKAAW,EAAE,IAAG,sCAAsC;QACnG,OAAO;YACL,kEAAkE;YAClE,UAAU,MAAM,QAAQ,kBAAkB,CAAC,yKAAW,EAAE,IAAG,gDAAgD;QAC7G;QAEA,6BAA6B;QAC7B,MAAM,iBAAiB;YACrB,GAAG,OAAO;YACV,WAAW,QAAQ,SAAS,EAAE,MAAM,GAAG,UAAU,EAAE;QACrD;QAEA,QAAQ,GAAG,CAAC,6BAA6B,iBAAgB,gBAAgB;QAEzE,OAAO,gLAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gLAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
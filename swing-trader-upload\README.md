# 🚀 SwingTrader AI - Professional Trading Platform

## 🎯 Quick Start

### Windows Users:
1. Double-click: `START-SWINGTRADER.bat`
2. Wait for installation and build to complete
3. Open browser to: http://localhost:3000

### Mac/Linux Users:
1. Open Terminal in this folder
2. Run: `chmod +x start-swingtrader.sh && ./start-swingtrader.sh`
3. Open browser to: http://localhost:3000

### Alternative Setup:
1. Run: `node quick-start.js` (auto-detects your OS)

## 🔑 API Keys Pre-Configured

This package includes pre-configured API keys for:
- ✅ Polygon.io (Real-time market data)
- ✅ Financial Modeling Prep (Stock quotes)
- ✅ Alpaca Trading (Paper trading)

## 🎯 Features

### Professional Swing Trading Strategies:
- **Overnight Momentum Continuation**
- **Technical Breakout Trend-Follow**
- **Automated Stock Scanning** (70+ stocks)
- **Risk Management & Position Sizing**

### Technical Analysis:
- RSI, MACD, Moving Averages
- Support/Resistance Detection
- Volume Analysis
- ATR-based Risk Management

### User Interface:
- Professional dark theme
- Real-time scanning
- Strategy comparison
- Risk warnings and alerts

## 🌐 Cloud Deployment

To deploy to the cloud for sharing:
```bash
node deploy.js
```

## 📞 Support

If you encounter issues:
1. Ensure Node.js is installed (https://nodejs.org/)
2. Check that port 3000 is available
3. See DEPLOYMENT-GUIDE.md for detailed instructions

## 🎉 Ready to Trade!

Your professional swing trading platform is ready to use!

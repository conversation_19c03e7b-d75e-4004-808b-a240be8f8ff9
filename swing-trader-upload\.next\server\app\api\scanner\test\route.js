var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/scanner/test/route.js")
R.c("server/chunks/7b731_next_90323cd8._.js")
R.c("server/chunks/[root-of-the-server]__a2ff872f._.js")
R.m("[project]/SwingTrader-AI-Package/.next-internal/server/app/api/scanner/test/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/SwingTrader-AI-Package/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/SwingTrader-AI-Package/src/app/api/scanner/test/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/SwingTrader-AI-Package/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/SwingTrader-AI-Package/src/app/api/scanner/test/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports

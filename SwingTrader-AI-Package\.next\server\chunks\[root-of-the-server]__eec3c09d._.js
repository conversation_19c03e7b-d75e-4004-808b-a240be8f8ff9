module.exports = [
"[project]/SwingTrader-AI-Package/.next-internal/server/app/api/scanner/strategies/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/SwingTrader-AI-Package/src/data/watchlist.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ALL_SYMBOLS",
    ()=>ALL_SYMBOLS,
    "PRIORITY_SYMBOLS",
    ()=>PRIORITY_SYMBOLS,
    "STOCKS_BY_SECTOR",
    ()=>STOCKS_BY_SECTOR,
    "SWING_TRADING_WATCHLIST",
    ()=>SWING_TRADING_WATCHLIST
]);
const SWING_TRADING_WATCHLIST = [
    // Large Cap Tech Giants
    {
        symbol: 'MSFT',
        name: 'Microsoft Corp',
        sector: 'Technology',
        marketCap: 'large'
    },
    {
        symbol: 'NVDA',
        name: 'NVIDIA Corp',
        sector: 'Technology',
        marketCap: 'large'
    },
    {
        symbol: 'AMZN',
        name: 'Amazon.com Inc',
        sector: 'Consumer Discretionary',
        marketCap: 'large'
    },
    {
        symbol: 'GOOG',
        name: 'Alphabet Inc Class C',
        sector: 'Technology',
        marketCap: 'large'
    },
    {
        symbol: 'GOOGL',
        name: 'Alphabet Inc Class A',
        sector: 'Technology',
        marketCap: 'large'
    },
    {
        symbol: 'META',
        name: 'Meta Platforms Inc',
        sector: 'Technology',
        marketCap: 'large'
    },
    {
        symbol: 'AVGO',
        name: 'Broadcom Inc',
        sector: 'Technology',
        marketCap: 'large'
    },
    {
        symbol: 'TSM',
        name: 'Taiwan Semiconductor',
        sector: 'Technology',
        marketCap: 'large'
    },
    {
        symbol: 'ORCL',
        name: 'Oracle Corp',
        sector: 'Technology',
        marketCap: 'large'
    },
    {
        symbol: 'NFLX',
        name: 'Netflix Inc',
        sector: 'Communication Services',
        marketCap: 'large'
    },
    {
        symbol: 'CSCO',
        name: 'Cisco Systems Inc',
        sector: 'Technology',
        marketCap: 'large'
    },
    {
        symbol: 'AMD',
        name: 'Advanced Micro Devices Inc',
        sector: 'Technology',
        marketCap: 'large'
    },
    // Financial Services
    {
        symbol: 'JPM',
        name: 'JPMorgan Chase & Co',
        sector: 'Financial Services',
        marketCap: 'large'
    },
    {
        symbol: 'BAC',
        name: 'Bank of America Corp',
        sector: 'Financial Services',
        marketCap: 'large'
    },
    {
        symbol: 'MS',
        name: 'Morgan Stanley',
        sector: 'Financial Services',
        marketCap: 'large'
    },
    {
        symbol: 'SCHW',
        name: 'Charles Schwab Corp',
        sector: 'Financial Services',
        marketCap: 'large'
    },
    {
        symbol: 'C',
        name: 'Citigroup Inc',
        sector: 'Financial Services',
        marketCap: 'large'
    },
    // Healthcare & Pharmaceuticals
    {
        symbol: 'JNJ',
        name: 'Johnson & Johnson',
        sector: 'Healthcare',
        marketCap: 'large'
    },
    {
        symbol: 'ABBV',
        name: 'AbbVie Inc',
        sector: 'Healthcare',
        marketCap: 'large'
    },
    {
        symbol: 'MRK',
        name: 'Merck & Co Inc',
        sector: 'Healthcare',
        marketCap: 'large'
    },
    {
        symbol: 'GILD',
        name: 'Gilead Sciences Inc',
        sector: 'Healthcare',
        marketCap: 'large'
    },
    // Industrial & Manufacturing
    {
        symbol: 'GE',
        name: 'General Electric Co',
        sector: 'Industrial',
        marketCap: 'large'
    },
    {
        symbol: 'CAT',
        name: 'Caterpillar Inc',
        sector: 'Industrial',
        marketCap: 'large'
    },
    {
        symbol: 'BA',
        name: 'Boeing Co',
        sector: 'Industrial',
        marketCap: 'large'
    },
    {
        symbol: 'GEV',
        name: 'GE Vernova Inc',
        sector: 'Industrial',
        marketCap: 'large'
    },
    // Semiconductors
    {
        symbol: 'ASML',
        name: 'ASML Holding NV',
        sector: 'Technology',
        marketCap: 'large'
    },
    {
        symbol: 'MU',
        name: 'Micron Technology Inc',
        sector: 'Technology',
        marketCap: 'large'
    },
    {
        symbol: 'LRCX',
        name: 'Lam Research Corp',
        sector: 'Technology',
        marketCap: 'large'
    },
    // Consumer & Retail
    {
        symbol: 'DIS',
        name: 'Walt Disney Co',
        sector: 'Communication Services',
        marketCap: 'large'
    },
    {
        symbol: 'SBUX',
        name: 'Starbucks Corp',
        sector: 'Consumer Discretionary',
        marketCap: 'large'
    },
    {
        symbol: 'MO',
        name: 'Altria Group Inc',
        sector: 'Consumer Staples',
        marketCap: 'large'
    },
    // Energy & Utilities
    {
        symbol: 'CEG',
        name: 'Constellation Energy Corp',
        sector: 'Utilities',
        marketCap: 'large'
    },
    {
        symbol: 'VST',
        name: 'Vistra Corp',
        sector: 'Utilities',
        marketCap: 'mid'
    },
    // Automotive
    {
        symbol: 'GM',
        name: 'General Motors Co',
        sector: 'Consumer Discretionary',
        marketCap: 'large'
    },
    // Growth & Tech Mid-Caps
    {
        symbol: 'PLTR',
        name: 'Palantir Technologies Inc',
        sector: 'Technology',
        marketCap: 'mid'
    },
    {
        symbol: 'APP',
        name: 'Applovin Corp',
        sector: 'Technology',
        marketCap: 'mid'
    },
    {
        symbol: 'DASH',
        name: 'DoorDash Inc',
        sector: 'Consumer Discretionary',
        marketCap: 'mid'
    },
    {
        symbol: 'NET',
        name: 'Cloudflare Inc',
        sector: 'Technology',
        marketCap: 'mid'
    },
    {
        symbol: 'DDOG',
        name: 'Datadog Inc',
        sector: 'Technology',
        marketCap: 'mid'
    },
    {
        symbol: 'ZS',
        name: 'Zscaler Inc',
        sector: 'Technology',
        marketCap: 'mid'
    },
    {
        symbol: 'SHOP',
        name: 'Shopify Inc',
        sector: 'Technology',
        marketCap: 'mid'
    },
    {
        symbol: 'RBLX',
        name: 'Roblox Corp',
        sector: 'Communication Services',
        marketCap: 'mid'
    },
    // Mining & Commodities
    {
        symbol: 'AEM',
        name: 'Agnico Eagle Mines Ltd',
        sector: 'Materials',
        marketCap: 'mid'
    },
    {
        symbol: 'NEM',
        name: 'Newmont Corp',
        sector: 'Materials',
        marketCap: 'large'
    },
    {
        symbol: 'CCJ',
        name: 'Cameco Corp',
        sector: 'Energy',
        marketCap: 'mid'
    },
    {
        symbol: 'PAAS',
        name: 'Pan American Silver Corp',
        sector: 'Materials',
        marketCap: 'small'
    },
    {
        symbol: 'BTG',
        name: 'B2Gold Corp',
        sector: 'Materials',
        marketCap: 'small'
    },
    {
        symbol: 'HL',
        name: 'Hecla Mining Co',
        sector: 'Materials',
        marketCap: 'small'
    },
    {
        symbol: 'MP',
        name: 'MP Materials Corp',
        sector: 'Materials',
        marketCap: 'small'
    },
    {
        symbol: 'AG',
        name: 'First Majestic Silver Corp',
        sector: 'Materials',
        marketCap: 'small'
    },
    // Transportation & Travel
    {
        symbol: 'UAL',
        name: 'United Airlines Holdings Inc',
        sector: 'Industrial',
        marketCap: 'mid'
    },
    {
        symbol: 'NCLH',
        name: 'Norwegian Cruise Line',
        sector: 'Consumer Discretionary',
        marketCap: 'mid'
    },
    // Fintech & Trading
    {
        symbol: 'HOOD',
        name: 'Robinhood Markets Inc',
        sector: 'Financial Services',
        marketCap: 'mid'
    },
    {
        symbol: 'SOFI',
        name: 'SoFi Technologies Inc',
        sector: 'Financial Services',
        marketCap: 'small'
    },
    // Consumer Brands
    {
        symbol: 'CELH',
        name: 'Celsius Holdings Inc',
        sector: 'Consumer Staples',
        marketCap: 'small'
    },
    {
        symbol: 'LEVI',
        name: 'Levi Strauss & Co',
        sector: 'Consumer Discretionary',
        marketCap: 'small'
    },
    {
        symbol: 'ELF',
        name: 'e.l.f. Beauty Inc',
        sector: 'Consumer Discretionary',
        marketCap: 'small'
    },
    {
        symbol: 'ETSY',
        name: 'Etsy Inc',
        sector: 'Consumer Discretionary',
        marketCap: 'mid'
    },
    {
        symbol: 'W',
        name: 'Wayfair Inc',
        sector: 'Consumer Discretionary',
        marketCap: 'mid'
    },
    // Crypto & Blockchain
    {
        symbol: 'RIOT',
        name: 'Riot Platforms Inc',
        sector: 'Technology',
        marketCap: 'small'
    },
    {
        symbol: 'HUT',
        name: 'Hut 8 Corp',
        sector: 'Technology',
        marketCap: 'small'
    },
    {
        symbol: 'IREN',
        name: 'IREN Ltd',
        sector: 'Technology',
        marketCap: 'small'
    },
    // International & Emerging
    {
        symbol: 'BILI',
        name: 'Bilibili Inc',
        sector: 'Communication Services',
        marketCap: 'small'
    },
    {
        symbol: 'TIGR',
        name: 'UP Fintech Holding Ltd',
        sector: 'Financial Services',
        marketCap: 'small'
    },
    {
        symbol: 'FUTU',
        name: 'Futu Holdings Ltd',
        sector: 'Financial Services',
        marketCap: 'small'
    },
    {
        symbol: 'NBIS',
        name: 'Nebius Group NV',
        sector: 'Technology',
        marketCap: 'small'
    },
    // Emerging Tech & AI
    {
        symbol: 'SOUN',
        name: 'SoundHound AI Inc',
        sector: 'Technology',
        marketCap: 'small'
    },
    {
        symbol: 'IONQ',
        name: 'IonQ Inc',
        sector: 'Technology',
        marketCap: 'small'
    },
    {
        symbol: 'RGTI',
        name: 'Rigetti Computing Inc',
        sector: 'Technology',
        marketCap: 'small'
    },
    // Aerospace & Defense
    {
        symbol: 'RKLB',
        name: 'Rocket Lab Corp',
        sector: 'Industrial',
        marketCap: 'small'
    },
    {
        symbol: 'ASTS',
        name: 'AST SpaceMobile Inc',
        sector: 'Technology',
        marketCap: 'small'
    },
    // Infrastructure & Utilities
    {
        symbol: 'VRT',
        name: 'Vertiv Holdings Co',
        sector: 'Industrial',
        marketCap: 'mid'
    }
];
const STOCKS_BY_SECTOR = SWING_TRADING_WATCHLIST.reduce((acc, stock)=>{
    if (!acc[stock.sector]) {
        acc[stock.sector] = [];
    }
    acc[stock.sector].push(stock);
    return acc;
}, {});
const ALL_SYMBOLS = SWING_TRADING_WATCHLIST.map((stock)=>stock.symbol);
const PRIORITY_SYMBOLS = [
    'MSFT',
    'NVDA',
    'AMZN',
    'GOOG',
    'GOOGL',
    'META',
    'AVGO',
    'TSM',
    'JPM',
    'NFLX',
    'ORCL',
    'JNJ',
    'BAC',
    'ABBV',
    'ASML',
    'PLTR'
];
}),
"[project]/SwingTrader-AI-Package/src/lib/indicators.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TechnicalIndicators",
    ()=>TechnicalIndicators
]);
class TechnicalIndicators {
    // Simple Moving Average
    static sma(data, period) {
        const result = [];
        for(let i = period - 1; i < data.length; i++){
            const sum = data.slice(i - period + 1, i + 1).reduce((a, b)=>a + b, 0);
            result.push(sum / period);
        }
        return result;
    }
    // Exponential Moving Average
    static ema(data, period) {
        const result = [];
        const multiplier = 2 / (period + 1);
        // Start with SMA for first value
        let ema = data.slice(0, period).reduce((a, b)=>a + b, 0) / period;
        result.push(ema);
        for(let i = period; i < data.length; i++){
            ema = data[i] * multiplier + ema * (1 - multiplier);
            result.push(ema);
        }
        return result;
    }
    // Relative Strength Index
    static rsi(data, period = 14) {
        const gains = [];
        const losses = [];
        for(let i = 1; i < data.length; i++){
            const change = data[i] - data[i - 1];
            gains.push(change > 0 ? change : 0);
            losses.push(change < 0 ? Math.abs(change) : 0);
        }
        const avgGains = this.sma(gains, period);
        const avgLosses = this.sma(losses, period);
        return avgGains.map((gain, i)=>{
            const rs = gain / avgLosses[i];
            return 100 - 100 / (1 + rs);
        });
    }
    // MACD (Moving Average Convergence Divergence)
    static macd(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
        const fastEMA = this.ema(data, fastPeriod);
        const slowEMA = this.ema(data, slowPeriod);
        // Align arrays (slowEMA starts later)
        const startIndex = slowPeriod - fastPeriod;
        const macdLine = fastEMA.slice(startIndex).map((fast, i)=>fast - slowEMA[i]);
        const signalLine = this.ema(macdLine, signalPeriod);
        const histogram = macdLine.slice(signalPeriod - 1).map((macd, i)=>macd - signalLine[i]);
        return {
            macd: macdLine,
            signal: signalLine,
            histogram
        };
    }
    // Bollinger Bands
    static bollingerBands(data, period = 20, stdDev = 2) {
        const sma = this.sma(data, period);
        const bands = sma.map((avg, i)=>{
            const slice = data.slice(i, i + period);
            const variance = slice.reduce((sum, val)=>sum + Math.pow(val - avg, 2), 0) / period;
            const standardDeviation = Math.sqrt(variance);
            return {
                upper: avg + standardDeviation * stdDev,
                middle: avg,
                lower: avg - standardDeviation * stdDev
            };
        });
        return bands;
    }
    // Support and Resistance Levels
    static findSupportResistance(candles, lookback = 20) {
        const highs = candles.map((c)=>c.high);
        const lows = candles.map((c)=>c.low);
        const resistance = [];
        const support = [];
        for(let i = lookback; i < candles.length - lookback; i++){
            const currentHigh = highs[i];
            const currentLow = lows[i];
            // Check if current high is a local maximum
            const isResistance = highs.slice(i - lookback, i).every((h)=>h <= currentHigh) && highs.slice(i + 1, i + lookback + 1).every((h)=>h <= currentHigh);
            // Check if current low is a local minimum
            const isSupport = lows.slice(i - lookback, i).every((l)=>l >= currentLow) && lows.slice(i + 1, i + lookback + 1).every((l)=>l >= currentLow);
            if (isResistance) resistance.push(currentHigh);
            if (isSupport) support.push(currentLow);
        }
        return {
            support,
            resistance
        };
    }
    // Volume analysis
    static volumeAnalysis(candles, period = 20) {
        const volumes = candles.map((c)=>c.volume);
        const avgVolume = this.sma(volumes, period);
        const currentVolume = volumes[volumes.length - 1];
        const currentAvgVolume = avgVolume[avgVolume.length - 1];
        return {
            currentVolume,
            averageVolume: currentAvgVolume,
            volumeRatio: currentVolume / currentAvgVolume,
            isHighVolume: currentVolume > currentAvgVolume * 1.5,
            isLowVolume: currentVolume < currentAvgVolume * 0.5
        };
    }
    // Swing Trading Analysis
    static analyzeSwingSetup(candles) {
        const closes = candles.map((c)=>c.close);
        const indicators = [];
        // RSI Analysis
        const rsi = this.rsi(closes);
        const currentRSI = rsi[rsi.length - 1];
        let rsiSignal = 'NEUTRAL';
        let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`;
        if (currentRSI < 30) {
            rsiSignal = 'BUY';
            rsiDescription += ' - Oversold condition, potential bounce';
        } else if (currentRSI > 70) {
            rsiSignal = 'SELL';
            rsiDescription += ' - Overbought condition, potential pullback';
        } else {
            rsiDescription += ' - Neutral zone';
        }
        indicators.push({
            name: 'RSI',
            value: currentRSI,
            signal: rsiSignal,
            description: rsiDescription
        });
        // Moving Average Analysis
        const sma20 = this.sma(closes, 20);
        const sma50 = this.sma(closes, 50);
        const currentPrice = closes[closes.length - 1];
        const currentSMA20 = sma20[sma20.length - 1];
        const currentSMA50 = sma50[sma50.length - 1];
        let maSignal = 'NEUTRAL';
        let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`;
        if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {
            maSignal = 'BUY';
            maDescription += ' - Bullish trend';
        } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {
            maSignal = 'SELL';
            maDescription += ' - Bearish trend';
        } else {
            maDescription += ' - Mixed signals';
        }
        indicators.push({
            name: 'Moving Averages',
            value: (currentPrice / currentSMA20 - 1) * 100,
            signal: maSignal,
            description: maDescription
        });
        // MACD Analysis
        const macdData = this.macd(closes);
        const currentMACD = macdData.macd[macdData.macd.length - 1];
        const currentSignal = macdData.signal[macdData.signal.length - 1];
        const currentHistogram = macdData.histogram[macdData.histogram.length - 1];
        let macdSignal = 'NEUTRAL';
        let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`;
        if (currentMACD > currentSignal && currentHistogram > 0) {
            macdSignal = 'BUY';
            macdDescription += ' - Bullish momentum';
        } else if (currentMACD < currentSignal && currentHistogram < 0) {
            macdSignal = 'SELL';
            macdDescription += ' - Bearish momentum';
        } else {
            macdDescription += ' - Momentum shifting';
        }
        indicators.push({
            name: 'MACD',
            value: currentHistogram,
            signal: macdSignal,
            description: macdDescription
        });
        // Volume Analysis
        const volumeData = this.volumeAnalysis(candles);
        let volumeSignal = 'NEUTRAL';
        let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`;
        if (volumeData.isHighVolume) {
            volumeSignal = 'BUY';
            volumeDescription += ' - High volume confirms move';
        } else if (volumeData.isLowVolume) {
            volumeSignal = 'SELL';
            volumeDescription += ' - Low volume, weak conviction';
        } else {
            volumeDescription += ' - Normal volume';
        }
        indicators.push({
            name: 'Volume',
            value: volumeData.volumeRatio,
            signal: volumeSignal,
            description: volumeDescription
        });
        return indicators;
    }
}
}),
"[project]/SwingTrader-AI-Package/src/lib/swingStrategies.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "SwingTradingStrategies",
    ()=>SwingTradingStrategies
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/src/lib/indicators.ts [app-route] (ecmascript)");
;
class SwingTradingStrategies {
    static DEFAULT_CRITERIA = {
        minPrice: 5.0,
        minVolume: 500000,
        minMarketCap: *********,
        minATRPercent: 1.5,
        above200SMA: false,
        maxDistanceFrom8EMA: 3.0,
        minRoomToResistance: 0.5,
        scanTimeStart: "09:00",
        scanTimeEnd: "20:00",
        maxRiskPerTrade: 2.0,
        maxConcurrentPositions: 5 // Increased from 3
    };
    // Strategy #1: Overnight Momentum Continuation
    static analyzeOvernightMomentum(symbol, candles, quote, accountSize = 100000) {
        if (candles.length < 50) return null;
        const closes = candles.map((c)=>c.close);
        const highs = candles.map((c)=>c.high);
        const lows = candles.map((c)=>c.low);
        const volumes = candles.map((c)=>c.volume);
        const currentPrice = quote.price;
        const currentVolume = quote.volume;
        const changePercent = quote.changePercent;
        // Calculate technical indicators (adjusted for shorter history)
        const sma50 = __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].sma(closes, Math.min(50, closes.length - 1)) // Use 50-day instead of 200-day
        ;
        const ema8 = __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].ema(closes, Math.min(8, closes.length - 1));
        const atr = this.calculateATR(candles, Math.min(14, candles.length - 1));
        const current50SMA = sma50[sma50.length - 1];
        const current8EMA = ema8[ema8.length - 1];
        const currentATR = atr[atr.length - 1];
        // Basic qualification filters (using 50-day SMA instead of 200-day)
        if (!this.passesBasicFilters(quote, currentVolume, current50SMA, currentPrice)) {
            return null;
        }
        // Check if it's a top intraday gainer (top decile movers)
        if (changePercent < 2.0) return null // Minimum 2% gain for momentum
        ;
        // Check distance from 8-EMA (not wildly extended)
        const distanceFrom8EMA = Math.abs(currentPrice - current8EMA) / currentATR;
        if (distanceFrom8EMA > this.DEFAULT_CRITERIA.maxDistanceFrom8EMA) return null;
        // Look for defended intraday level (simplified - using VWAP proxy)
        const vwap = this.calculateVWAP(candles.slice(-1)[0]);
        const keyLevel = Math.max(vwap, currentPrice * 0.98) // Approximate defended level
        ;
        // Check if holding gains (>50% of day's range)
        const todayHigh = highs[highs.length - 1];
        const todayLow = lows[lows.length - 1];
        const dayRange = todayHigh - todayLow;
        const currentFromLow = currentPrice - todayLow;
        const holdingGainsPercent = currentFromLow / dayRange;
        if (holdingGainsPercent < 0.5) return null // Must hold >50% of range
        ;
        // Calculate room to next resistance
        const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR);
        if (roomToResistance < this.DEFAULT_CRITERIA.minRoomToResistance) return null;
        // Position sizing (risk 0.5-1% of account)
        const riskPercent = 0.75 // 0.75% risk for overnight holds
        ;
        const stopDistance = currentPrice - keyLevel;
        const riskAmount = accountSize * (riskPercent / 100);
        const positionSize = Math.floor(riskAmount / stopDistance);
        // Targets: Pre-market scale at 3-5%, opening hour at 5-8%
        const targets = [
            currentPrice * 1.03,
            currentPrice * 1.05,
            currentPrice * 1.08 // 8% extended target
        ];
        const confidence = this.calculateOvernightConfidence(changePercent, holdingGainsPercent, currentVolume, roomToResistance);
        return {
            strategy: 'overnight_momentum',
            confidence,
            entryPrice: currentPrice,
            stopLoss: keyLevel,
            targets,
            positionSize,
            riskAmount,
            holdingPeriod: 'overnight',
            keyLevel,
            invalidation: `Daily close below ${keyLevel.toFixed(2)} or gap down below level`,
            notes: [
                'Enter final 30-60 min before close',
                'Exit pre-market on strength or first 45min',
                'Hard stop if gaps below defended level',
                'Scale out aggressively if gaps >1 ATR up'
            ]
        };
    }
    // Strategy #2: Technical Breakout Trend-Follow (8-EMA + 200-SMA)
    static analyzeTechnicalBreakout(symbol, candles, quote, accountSize = 100000) {
        if (candles.length < 50) return null;
        const closes = candles.map((c)=>c.close);
        const volumes = candles.map((c)=>c.volume);
        const currentPrice = quote.price;
        // Calculate technical indicators (adjusted for shorter history)
        const sma50 = __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].sma(closes, Math.min(50, closes.length - 1));
        const ema8 = __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].ema(closes, Math.min(8, closes.length - 1));
        const atr = this.calculateATR(candles, Math.min(14, candles.length - 1));
        const current50SMA = sma50[sma50.length - 1];
        const current8EMA = ema8[ema8.length - 1];
        const currentATR = atr[atr.length - 1];
        // Basic qualification filters (using 50-day SMA)
        if (!this.passesBasicFilters(quote, quote.volume, current50SMA, currentPrice)) {
            return null;
        }
        // Check if in clear uptrend above 50-SMA (adjusted from 200-SMA)
        if (currentPrice <= current50SMA) return null;
        // Check 8-EMA behavior - should be "hugging" the 8-EMA
        const distanceFrom8EMA = Math.abs(currentPrice - current8EMA);
        const emaDistancePercent = distanceFrom8EMA / currentPrice * 100;
        // Should be close to 8-EMA (within 2-3% for quality trend-follow)
        if (emaDistancePercent > 3.0) return null;
        // Check for recent breakout or EMA reclaim
        const recentEMAReclaim = this.checkEMAReclaim(closes, ema8, 5) // Last 5 days
        ;
        if (!recentEMAReclaim) return null;
        // Volume expansion check
        const avgVolume = __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].sma(volumes, 20);
        const currentAvgVolume = avgVolume[avgVolume.length - 1];
        const volumeExpansion = quote.volume / currentAvgVolume;
        if (volumeExpansion < 1.2) return null // Need some volume expansion
        ;
        // Calculate room to next resistance
        const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR);
        if (roomToResistance < 1.5) return null // Need more room for trend-follow
        ;
        // Position sizing (risk 1% of account)
        const riskPercent = 1.0;
        const stopDistance = currentPrice - current8EMA // Stop at 8-EMA break
        ;
        const riskAmount = accountSize * (riskPercent / 100);
        const positionSize = Math.floor(riskAmount / stopDistance);
        // Targets: Scale at resistance levels
        const targets = [
            currentPrice * 1.05,
            currentPrice * 1.10,
            currentPrice * 1.15 // 15% extended target
        ];
        const confidence = this.calculateBreakoutConfidence(emaDistancePercent, volumeExpansion, roomToResistance, quote.changePercent);
        return {
            strategy: 'technical_breakout',
            confidence,
            entryPrice: currentPrice,
            stopLoss: current8EMA,
            targets,
            positionSize,
            riskAmount,
            holdingPeriod: 'days_to_weeks',
            keyLevel: current8EMA,
            invalidation: `Daily close below 8-EMA (${current8EMA.toFixed(2)})`,
            notes: [
                'Enter on afternoon reclaim of 8-EMA',
                'Add only on higher-low pullbacks to 8-EMA',
                'Scale partials at resistance levels',
                'Exit on daily close below 8-EMA'
            ]
        };
    }
    // Helper methods
    static passesBasicFilters(quote, volume, sma50, price) {
        return price >= this.DEFAULT_CRITERIA.minPrice && volume >= this.DEFAULT_CRITERIA.minVolume && (quote.marketCap || 0) >= this.DEFAULT_CRITERIA.minMarketCap && price > sma50 // Using 50-day SMA instead of 200-day for shorter history
        ;
    }
    static calculateATR(candles, period) {
        const trueRanges = [];
        for(let i = 1; i < candles.length; i++){
            const high = candles[i].high;
            const low = candles[i].low;
            const prevClose = candles[i - 1].close;
            const tr = Math.max(high - low, Math.abs(high - prevClose), Math.abs(low - prevClose));
            trueRanges.push(tr);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].sma(trueRanges, period);
    }
    static calculateVWAP(candle) {
        // Simplified VWAP calculation using typical price
        return (candle.high + candle.low + candle.close) / 3;
    }
    static calculateRoomToResistance(candles, currentPrice, atr) {
        // Find recent highs as resistance levels
        const recentHighs = candles.slice(-20).map((c)=>c.high);
        const maxHigh = Math.max(...recentHighs);
        const roomToHigh = maxHigh - currentPrice;
        return roomToHigh / atr;
    }
    static checkEMAReclaim(closes, ema8, lookback) {
        // Check if price recently reclaimed 8-EMA
        for(let i = Math.max(0, closes.length - lookback); i < closes.length - 1; i++){
            if (closes[i] < ema8[i] && closes[i + 1] > ema8[i + 1]) {
                return true // Found a reclaim
                ;
            }
        }
        return false;
    }
    static calculateOvernightConfidence(changePercent, holdingGains, volume, roomToResistance) {
        let confidence = 50;
        // Change percent bonus
        if (changePercent > 5) confidence += 15;
        else if (changePercent > 3) confidence += 10;
        else if (changePercent > 2) confidence += 5;
        // Holding gains bonus
        if (holdingGains > 0.8) confidence += 15;
        else if (holdingGains > 0.6) confidence += 10;
        else if (holdingGains > 0.5) confidence += 5;
        // Volume bonus
        if (volume > 2000000) confidence += 10;
        else if (volume > 1000000) confidence += 5;
        // Room to resistance
        if (roomToResistance > 2) confidence += 10;
        else if (roomToResistance > 1.5) confidence += 5;
        return Math.min(95, Math.max(30, confidence));
    }
    static calculateBreakoutConfidence(emaDistance, volumeExpansion, roomToResistance, changePercent) {
        let confidence = 60;
        // EMA proximity bonus (closer is better for trend-follow)
        if (emaDistance < 1) confidence += 15;
        else if (emaDistance < 2) confidence += 10;
        else if (emaDistance < 3) confidence += 5;
        // Volume expansion bonus
        if (volumeExpansion > 2) confidence += 15;
        else if (volumeExpansion > 1.5) confidence += 10;
        else if (volumeExpansion > 1.2) confidence += 5;
        // Room to resistance
        if (roomToResistance > 3) confidence += 15;
        else if (roomToResistance > 2) confidence += 10;
        else if (roomToResistance > 1.5) confidence += 5;
        // Positive momentum
        if (changePercent > 2) confidence += 5;
        return Math.min(95, Math.max(40, confidence));
    }
}
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/tty [external] (tty, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}),
"[externals]/os [external] (os, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[project]/SwingTrader-AI-Package/src/lib/polygon.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "PolygonAPI",
    ()=>PolygonAPI,
    "polygonAPI",
    ()=>polygonAPI
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
const POLYGON_BASE_URL = 'https://api.polygon.io';
const API_KEY = process.env.POLYGON_API_KEY;
class PolygonAPI {
    apiKey;
    constructor(apiKey){
        this.apiKey = apiKey || API_KEY || '';
        if (!this.apiKey) {
            throw new Error('Polygon API key is required');
        }
    }
    // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)
    async getStockQuote(symbol) {
        try {
            // Use snapshot endpoint for real-time data (available on paid plans)
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            const data = response.data.results[0];
            if (!data) {
                throw new Error(`No data found for ${symbol}`);
            }
            const ticker = data.value || data;
            const dayData = ticker.day || {};
            const prevDayData = ticker.prevDay || {};
            const lastQuote = ticker.lastQuote || {};
            const lastTrade = ticker.lastTrade || {};
            // Use the most recent price available
            const currentPrice = lastTrade.p || dayData.c || prevDayData.c;
            const prevClose = prevDayData.c || dayData.o;
            const change = currentPrice - prevClose;
            const changePercent = change / prevClose * 100;
            return {
                symbol: symbol.toUpperCase(),
                name: ticker.name || symbol.toUpperCase(),
                price: currentPrice,
                change,
                changePercent,
                volume: dayData.v || 0,
                marketCap: ticker.market_cap,
                pe: undefined,
                dividend: undefined
            };
        } catch (error) {
            console.error('Error fetching stock quote from Polygon:', error);
            // Fallback to previous day data if snapshot fails
            try {
                const fallbackResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`, {
                    params: {
                        adjusted: 'true',
                        apikey: this.apiKey
                    }
                });
                const data = fallbackResponse.data.results[0];
                return {
                    symbol: symbol.toUpperCase(),
                    name: symbol.toUpperCase(),
                    price: data.c,
                    change: data.c - data.o,
                    changePercent: (data.c - data.o) / data.o * 100,
                    volume: data.v,
                    marketCap: undefined,
                    pe: undefined,
                    dividend: undefined
                };
            } catch (fallbackError) {
                console.error('Polygon fallback also failed:', fallbackError);
                throw new Error(`Failed to fetch quote for ${symbol}`);
            }
        }
    }
    // Get historical candlestick data (optimized for paid plans)
    async getHistoricalData(symbol, timespan = 'day', multiplier = 1, from, to) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`, {
                params: {
                    adjusted: 'true',
                    sort: 'asc',
                    limit: 50000,
                    apikey: this.apiKey
                }
            });
            if (!response.data.results || response.data.results.length === 0) {
                console.warn(`No historical data found for ${symbol}`);
                return [];
            }
            return response.data.results.map((candle)=>({
                    timestamp: candle.t,
                    open: candle.o,
                    high: candle.h,
                    low: candle.l,
                    close: candle.c,
                    volume: candle.v
                }));
        } catch (error) {
            console.error(`Error fetching historical data for ${symbol}:`, error);
            // Log the specific error for debugging
            if (error.response) {
                console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`);
                console.error('Response data:', error.response.data);
            }
            throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`);
        }
    }
    // Get company details
    async getCompanyDetails(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data.results;
        } catch (error) {
            console.error('Error fetching company details:', error);
            return null;
        }
    }
    // Get market status
    async getMarketStatus() {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v1/marketstatus/now`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching market status:', error);
            return null;
        }
    }
    // Search for stocks
    async searchStocks(query, limit = 10) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v3/reference/tickers`, {
                params: {
                    search: query,
                    market: 'stocks',
                    active: 'true',
                    limit,
                    apikey: this.apiKey
                }
            });
            return response.data.results || [];
        } catch (error) {
            console.error('Error searching stocks:', error);
            return [];
        }
    }
}
const polygonAPI = new PolygonAPI();
}),
"[project]/SwingTrader-AI-Package/src/lib/fmp.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FMPAPI",
    ()=>FMPAPI,
    "fmpAPI",
    ()=>fmpAPI
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
const FMP_BASE_URL = 'https://financialmodelingprep.com/api';
const API_KEY = process.env.FMP_API_KEY;
class FMPAPI {
    apiKey;
    constructor(apiKey){
        this.apiKey = apiKey || API_KEY || '';
        if (!this.apiKey) {
            throw new Error('FMP API key is required');
        }
    }
    // Get real-time stock quote with rate limiting
    async getStockQuote(symbol) {
        try {
            // Add delay to prevent rate limiting
            await this.delay(100); // 100ms delay between requests
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/quote/${symbol}`, {
                params: {
                    apikey: this.apiKey
                },
                timeout: 10000 // 10 second timeout
            });
            const data = response.data[0];
            if (!data) {
                throw new Error(`No data found for symbol ${symbol}`);
            }
            return {
                symbol: data.symbol,
                name: data.name || data.symbol,
                price: data.price,
                change: data.change,
                changePercent: data.changesPercentage,
                volume: data.volume,
                marketCap: data.marketCap,
                pe: data.pe,
                dividend: undefined // Will be fetched separately if needed
            };
        } catch (error) {
            console.error('Error fetching FMP stock quote:', error);
            // Handle rate limiting with exponential backoff
            if (error.response?.status === 429) {
                console.warn(`⚠️ Rate limited for ${symbol}, implementing backoff...`);
                // Try multiple retries with increasing delays
                for(let attempt = 1; attempt <= 3; attempt++){
                    const delay = Math.min(1000 * Math.pow(2, attempt), 10000) // Max 10 seconds
                    ;
                    console.log(`Retry attempt ${attempt} for ${symbol} after ${delay}ms...`);
                    await this.delay(delay);
                    try {
                        const retryResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/quote/${symbol}`, {
                            params: {
                                apikey: this.apiKey
                            },
                            timeout: 15000
                        });
                        const retryData = retryResponse.data[0];
                        if (retryData) {
                            console.log(`✅ Successfully retrieved ${symbol} on attempt ${attempt}`);
                            return {
                                symbol: retryData.symbol,
                                name: retryData.name || retryData.symbol,
                                price: retryData.price,
                                change: retryData.change,
                                changePercent: retryData.changesPercentage,
                                volume: retryData.volume,
                                marketCap: retryData.marketCap,
                                pe: retryData.pe,
                                dividend: undefined
                            };
                        }
                    } catch (retryError) {
                        if (retryError.response?.status !== 429) {
                            break;
                        }
                        console.warn(`Retry ${attempt} failed for ${symbol}, continuing...`);
                    }
                }
                console.error(`❌ All retries exhausted for ${symbol}`);
            }
            throw new Error(`Failed to fetch quote for ${symbol}`);
        }
    }
    // Helper method for delays
    delay(ms) {
        return new Promise((resolve)=>setTimeout(resolve, ms));
    }
    // Get company profile
    async getCompanyProfile(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/profile/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data[0];
        } catch (error) {
            console.error('Error fetching company profile:', error);
            return null;
        }
    }
    // Get financial ratios
    async getFinancialRatios(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/ratios/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data[0] // Most recent ratios
            ;
        } catch (error) {
            console.error('Error fetching financial ratios:', error);
            return null;
        }
    }
    // Get key metrics
    async getKeyMetrics(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/key-metrics/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data[0] // Most recent metrics
            ;
        } catch (error) {
            console.error('Error fetching key metrics:', error);
            return null;
        }
    }
    // Get analyst recommendations
    async getAnalystRecommendations(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching analyst recommendations:', error);
            return [];
        }
    }
    // Get earnings calendar
    async getEarningsCalendar(from, to) {
        try {
            const params = {
                apikey: this.apiKey
            };
            if (from) params.from = from;
            if (to) params.to = to;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/earning_calendar`, {
                params
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching earnings calendar:', error);
            return [];
        }
    }
    // Get economic calendar
    async getEconomicCalendar(from, to) {
        try {
            const params = {
                apikey: this.apiKey
            };
            if (from) params.from = from;
            if (to) params.to = to;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/economic_calendar`, {
                params
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching economic calendar:', error);
            return [];
        }
    }
    // Search for stocks
    async searchStocks(query, limit = 10) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/search`, {
                params: {
                    query,
                    limit,
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error searching stocks:', error);
            return [];
        }
    }
    // Get sector performance
    async getSectorPerformance() {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/sector-performance`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching sector performance:', error);
            return [];
        }
    }
    // Get market gainers/losers
    async getMarketMovers(type) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/stock_market/${type}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error(`Error fetching market ${type}:`, error);
            return [];
        }
    }
}
const fmpAPI = new FMPAPI();
}),
"[externals]/net [external] (net, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}),
"[project]/SwingTrader-AI-Package/src/lib/ibkr.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "IBKRAPI",
    ()=>IBKRAPI
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/@stoqey/ib/dist/index.js [app-route] (ecmascript)");
;
class IBKRAPI {
    ib;
    config;
    connected = false;
    marketDataRequests = new Map();
    nextReqId = 1;
    constructor(config){
        this.config = config;
        this.ib = new __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IBApi"]({
            host: config.host,
            port: config.port,
            clientId: config.clientId
        });
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        // Connection events
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].connected, ()=>{
            console.log('✅ Connected to IBKR TWS');
            this.connected = true;
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].disconnected, ()=>{
            console.log('❌ Disconnected from IBKR TWS');
            this.connected = false;
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].error, (err, code, reqId)=>{
            console.error(`IBKR Error ${code}:`, err);
            // Handle market data request errors
            if (reqId && this.marketDataRequests.has(reqId)) {
                const request = this.marketDataRequests.get(reqId);
                request.reject(new Error(`IBKR Error ${code}: ${err}`));
                this.marketDataRequests.delete(reqId);
            }
        });
        // Market data events
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].tickPrice, (reqId, tickType, price, canAutoExecute)=>{
            const request = this.marketDataRequests.get(reqId);
            if (request) {
                // Store price data - we'll collect multiple ticks before resolving
                if (!request.data) request.data = {};
                switch(tickType){
                    case 1:
                        request.data.bid = price;
                        break;
                    case 2:
                        request.data.ask = price;
                        break;
                    case 4:
                        request.data.last = price;
                        request.data.price = price;
                        break;
                    case 9:
                        request.data.close = price;
                        request.data.previousClose = price;
                        break;
                }
            }
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].tickSize, (reqId, tickType, size)=>{
            const request = this.marketDataRequests.get(reqId);
            if (request) {
                if (!request.data) request.data = {};
                switch(tickType){
                    case 0:
                        request.data.bidSize = size;
                        break;
                    case 3:
                        request.data.askSize = size;
                        break;
                    case 5:
                        request.data.lastSize = size;
                        break;
                    case 8:
                        request.data.volume = size;
                        break;
                }
            }
        });
        // Market data snapshot complete
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].tickSnapshotEnd, (reqId)=>{
            const request = this.marketDataRequests.get(reqId);
            if (request) {
                const data = request.data || {};
                const marketData = {
                    symbol: request.symbol,
                    price: data.last || data.close || data.bid || data.ask || 0,
                    change: data.last && data.previousClose ? data.last - data.previousClose : 0,
                    changePercent: data.last && data.previousClose ? (data.last - data.previousClose) / data.previousClose * 100 : 0,
                    volume: data.volume || 0,
                    bid: data.bid || 0,
                    ask: data.ask || 0,
                    last: data.last || 0,
                    close: data.close || 0,
                    previousClose: data.previousClose || 0,
                    marketCap: 0,
                    avgVolume: 0
                };
                request.resolve(marketData);
                this.marketDataRequests.delete(reqId);
            }
        });
    }
    async connect() {
        return new Promise((resolve, reject)=>{
            if (this.connected) {
                resolve(true);
                return;
            }
            const timeout = setTimeout(()=>{
                reject(new Error('IBKR connection timeout - ensure TWS/IB Gateway is running'));
            }, 10000);
            this.ib.once(__TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].connected, ()=>{
                clearTimeout(timeout);
                resolve(true);
            });
            this.ib.once(__TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].error, (err)=>{
                clearTimeout(timeout);
                reject(new Error(`IBKR connection failed: ${err}`));
            });
            console.log(`🔌 Connecting to IBKR TWS at ${this.config.host}:${this.config.port}...`);
            this.ib.connect();
        });
    }
    disconnect() {
        if (this.connected) {
            this.ib.disconnect();
        }
    }
    createStockContract(symbol) {
        return {
            symbol: symbol.toUpperCase(),
            secType: 'STK',
            exchange: 'SMART',
            currency: 'USD'
        };
    }
    async getMarketData(symbol) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR - ensure TWS/IB Gateway is running');
        }
        return new Promise((resolve, reject)=>{
            const reqId = this.nextReqId++;
            const contract = this.createStockContract(symbol);
            // Store the request
            this.marketDataRequests.set(reqId, {
                symbol: symbol.toUpperCase(),
                resolve,
                reject,
                data: {}
            });
            // Set timeout for the request
            setTimeout(()=>{
                if (this.marketDataRequests.has(reqId)) {
                    this.marketDataRequests.delete(reqId);
                    reject(new Error(`Market data request timeout for ${symbol}`));
                }
            }, 10000);
            // Request market data snapshot
            this.ib.reqMktData(reqId, contract, '', true, false, []);
        });
    }
    // Convert IBKR market data to StockData format
    async getStockQuote(symbol) {
        const marketData = await this.getMarketData(symbol);
        return {
            symbol: marketData.symbol,
            name: marketData.symbol,
            price: marketData.price,
            change: marketData.change,
            changePercent: marketData.changePercent,
            volume: marketData.volume,
            marketCap: marketData.marketCap,
            pe: undefined,
            dividend: undefined // Not available in basic market data
        };
    }
    isConnected() {
        return this.connected;
    }
}
}),
"[project]/SwingTrader-AI-Package/src/lib/enhancedSwingScanner.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "EnhancedSwingScanner",
    ()=>EnhancedSwingScanner,
    "enhancedSwingScanner",
    ()=>enhancedSwingScanner
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$swingStrategies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/src/lib/swingStrategies.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/src/lib/polygon.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$fmp$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/src/lib/fmp.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$ibkr$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/src/lib/ibkr.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/date-fns/format.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/date-fns/subDays.js [app-route] (ecmascript)");
;
;
;
;
;
class EnhancedSwingScanner {
    fmpAPI;
    polygonAPI;
    ibkrAPI;
    accountSize;
    useIBKR = true;
    constructor(accountSize = 100000){
        this.fmpAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$fmp$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FMPAPI"](process.env.FMP_API_KEY);
        this.polygonAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolygonAPI"](process.env.POLYGON_API_KEY);
        // Initialize IBKR API
        this.ibkrAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$ibkr$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IBKRAPI"]({
            host: '127.0.0.1',
            port: 7497,
            clientId: 1,
            paperTrading: true
        });
        this.accountSize = accountSize;
        console.log('🔧 Enhanced Swing Scanner initialized - Using IBKR TWS as primary data source (no rate limits)');
    }
    // Main enhanced scanning function
    async scanWithStrategies(symbols, maxConcurrent = 5) {
        const startTime = Date.now();
        const results = [];
        const failed = [];
        console.log(`Starting enhanced strategy scan of ${symbols.length} stocks...`);
        // Check if we're in optimal scan time (12:00-16:00 ET)
        const marketConditions = this.getMarketConditions();
        // Process stocks in batches
        for(let i = 0; i < symbols.length; i += maxConcurrent){
            const batch = symbols.slice(i, i + maxConcurrent);
            const batchPromises = batch.map((symbol)=>this.scanSingleStockStrategies(symbol));
            const batchResults = await Promise.allSettled(batchPromises);
            batchResults.forEach((result, index)=>{
                const symbol = batch[index];
                if (result.status === 'fulfilled' && result.value) {
                    results.push(result.value);
                } else {
                    failed.push(symbol);
                    console.warn(`Failed to scan ${symbol}:`, result.status === 'rejected' ? result.reason : 'Unknown error');
                }
            });
            // Rate limiting delay
            if (i + maxConcurrent < symbols.length) {
                await new Promise((resolve)=>setTimeout(resolve, 1000));
            }
        }
        // Sort by overall score and assign ranks
        results.sort((a, b)=>b.overallScore - a.overallScore);
        results.forEach((result, index)=>{
            result.rank = index + 1;
        });
        // Calculate summary statistics
        const overnightSetups = results.filter((r)=>r.overnightSetup).length;
        const breakoutSetups = results.filter((r)=>r.breakoutSetup).length;
        const bothStrategies = results.filter((r)=>r.overnightSetup && r.breakoutSetup).length;
        const scanDuration = Date.now() - startTime;
        // Disconnect from IBKR when done
        if (this.ibkrAPI.isConnected()) {
            this.ibkrAPI.disconnect();
            console.log('🔌 Disconnected from IBKR TWS');
        }
        return {
            totalScanned: symbols.length,
            overnightSetups,
            breakoutSetups,
            bothStrategies,
            topSetups: results.slice(0, 25),
            scanDuration,
            marketConditions
        };
    }
    // Scan individual stock for both strategies
    async scanSingleStockStrategies(symbol) {
        try {
            console.log(`🔍 Starting scan for ${symbol}...`);
            // Get stock quote and historical data
            // Use IBKR as primary (no rate limits), fallback to Polygon, then FMP
            let quote;
            let historicalData;
            if (this.useIBKR) {
                try {
                    console.log(`📊 Using IBKR TWS for ${symbol} quote (no rate limits)...`);
                    // Connect to IBKR if not already connected
                    if (!this.ibkrAPI.isConnected()) {
                        await this.ibkrAPI.connect();
                    }
                    [quote, historicalData] = await Promise.all([
                        this.ibkrAPI.getStockQuote(symbol),
                        this.getHistoricalData(symbol)
                    ]);
                    console.log(`✅ Successfully retrieved IBKR quote for ${symbol}: $${quote.price}`);
                } catch (ibkrError) {
                    console.log(`⚠️ IBKR failed for ${symbol}: ${ibkrError.message}`);
                    console.log(`🔄 Falling back to Polygon API...`);
                    this.useIBKR = false; // Disable IBKR for subsequent requests
                }
            }
            // Fallback to Polygon if IBKR failed or not enabled
            if (!this.useIBKR) {
                try {
                    console.log(`📊 Using Polygon API for ${symbol} quote...`)[quote, historicalData] = await Promise.all([
                        this.polygonAPI.getStockQuote(symbol),
                        this.getHistoricalData(symbol)
                    ]);
                    console.log(`✅ Successfully retrieved Polygon quote for ${symbol}: $${quote.price}`);
                } catch (polygonError) {
                    console.log(`⚠️ Polygon failed for ${symbol}, trying FMP fallback...`);
                    try {
                        [quote, historicalData] = await Promise.all([
                            this.fmpAPI.getStockQuote(symbol),
                            this.getHistoricalData(symbol)
                        ]);
                        console.log(`✅ Successfully retrieved FMP quote for ${symbol}: $${quote.price}`);
                    } catch (fmpError) {
                        console.log(`❌ All APIs failed for ${symbol}, using historical data fallback...`);
                        // Get historical data and use latest price as quote
                        historicalData = await this.getHistoricalData(symbol);
                        if (historicalData && historicalData.length > 0) {
                            const latest = historicalData[historicalData.length - 1];
                            const previous = historicalData[historicalData.length - 2];
                            const change = previous ? latest.close - previous.close : 0;
                            const changePercent = previous ? change / previous.close * 100 : 0;
                            quote = {
                                symbol: symbol,
                                name: symbol,
                                price: latest.close,
                                change: change,
                                changePercent: changePercent,
                                volume: latest.volume,
                                marketCap: 0,
                                pe: 0,
                                dividend: undefined
                            };
                            console.log(`✅ Using historical data fallback for ${symbol}: $${latest.close}`);
                        } else {
                            throw new Error(`All data sources failed for ${symbol}`);
                        }
                    }
                }
            }
            console.log(`📊 Quote for ${symbol}:`, {
                symbol: quote.symbol,
                price: quote.price,
                volume: quote.volume,
                changePercent: quote.changePercent
            });
            console.log(`📈 Historical data length for ${symbol}:`, historicalData?.length);
            if (!quote) {
                throw new Error(`No quote data available for ${symbol}`);
            }
            if (!historicalData || historicalData.length < 30) {
                throw new Error(`Insufficient historical data for ${symbol} - need at least 30 days, got ${historicalData?.length || 0}`);
            }
            console.log(`🧮 Analyzing overnight momentum for ${symbol}...`);
            // Analyze both strategies
            const overnightSetup = __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$swingStrategies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SwingTradingStrategies"].analyzeOvernightMomentum(symbol, historicalData, quote, this.accountSize);
            console.log(`✅ Overnight setup for ${symbol}:`, overnightSetup ? 'Found' : 'None');
            console.log(`🧮 Analyzing technical breakout for ${symbol}...`);
            const breakoutSetup = __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$swingStrategies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SwingTradingStrategies"].analyzeTechnicalBreakout(symbol, historicalData, quote, this.accountSize);
            console.log(`✅ Breakout setup for ${symbol}:`, breakoutSetup ? 'Found' : 'None');
            // Skip if no valid setups
            if (!overnightSetup && !breakoutSetup) {
                console.log(`⚠️ No valid setups found for ${symbol}`);
                return null;
            }
            // Determine best strategy and overall score
            const { bestStrategy, overallScore } = this.calculateBestStrategy(overnightSetup, breakoutSetup);
            // Generate alerts and warnings
            const alerts = this.generateAlerts(overnightSetup, breakoutSetup, quote);
            const riskWarnings = this.generateRiskWarnings(overnightSetup, breakoutSetup, quote);
            const result = {
                symbol,
                name: quote.name,
                sector: this.getSectorForSymbol(symbol),
                quote,
                overnightSetup: overnightSetup || undefined,
                breakoutSetup: breakoutSetup || undefined,
                bestStrategy,
                overallScore,
                rank: 0,
                scanTime: new Date().toISOString(),
                alerts,
                riskWarnings
            };
            console.log(`✅ Successfully scanned ${symbol} with score ${overallScore}`);
            return result;
        } catch (error) {
            console.error(`❌ Error scanning ${symbol}:`, error);
            if (error instanceof Error) {
                console.error(`❌ Error message: ${error.message}`);
                console.error(`❌ Error stack: ${error.stack}`);
            }
            return null;
        }
    }
    // Get historical data with optimized API usage
    async getHistoricalData(symbol) {
        const to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), 'yyyy-MM-dd');
        const from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 100), 'yyyy-MM-dd') // 100 days should be sufficient
        ;
        try {
            console.log(`Fetching historical data for ${symbol} from ${from} to ${to}`);
            const data = await this.polygonAPI.getHistoricalData(symbol, 'day', 1, from, to);
            if (data.length === 0) {
                console.warn(`No historical data returned for ${symbol}`);
                throw new Error('No historical data available');
            }
            console.log(`Successfully fetched ${data.length} days of data for ${symbol}`);
            return data;
        } catch (error) {
            console.error(`Failed to fetch historical data for ${symbol}:`, error);
            throw error;
        }
    }
    // Calculate best strategy and overall score
    calculateBestStrategy(overnight, breakout) {
        if (!overnight && !breakout) {
            return {
                overallScore: 0
            };
        }
        if (overnight && !breakout) {
            return {
                bestStrategy: 'overnight_momentum',
                overallScore: overnight.confidence
            };
        }
        if (breakout && !overnight) {
            return {
                bestStrategy: 'technical_breakout',
                overallScore: breakout.confidence
            };
        }
        if (overnight && breakout) {
            // Both strategies valid - choose higher confidence
            if (overnight.confidence > breakout.confidence) {
                return {
                    bestStrategy: 'overnight_momentum',
                    overallScore: overnight.confidence + 5
                } // Bonus for multiple setups
                ;
            } else {
                return {
                    bestStrategy: 'technical_breakout',
                    overallScore: breakout.confidence + 5
                };
            }
        }
        return {
            overallScore: 0
        };
    }
    // Generate trading alerts
    generateAlerts(overnight, breakout, quote) {
        const alerts = [];
        if (overnight) {
            alerts.push(`🚀 OVERNIGHT MOMENTUM: Entry ${overnight.entryPrice.toFixed(2)}, Target ${overnight.targets[0].toFixed(2)}`);
            alerts.push(`⏰ Execute in final 30-60 min before close`);
            alerts.push(`🛑 Stop: ${overnight.stopLoss.toFixed(2)} (${((overnight.entryPrice - overnight.stopLoss) / overnight.entryPrice * 100).toFixed(1)}% risk)`);
        }
        if (breakout) {
            alerts.push(`📈 BREAKOUT SETUP: Entry ${breakout.entryPrice.toFixed(2)}, riding 8-EMA`);
            alerts.push(`🎯 Targets: ${breakout.targets.map((t)=>t.toFixed(2)).join(', ')}`);
            alerts.push(`🛑 Stop: Daily close below ${breakout.stopLoss.toFixed(2)}`);
        }
        if (quote && quote.changePercent > 5) {
            alerts.push(`🔥 Strong momentum: +${quote.changePercent.toFixed(1)}% today`);
        }
        return alerts;
    }
    // Generate risk warnings
    generateRiskWarnings(overnight, breakout, quote) {
        const warnings = [];
        if (overnight) {
            warnings.push(`⚠️ Overnight gap risk - size down vs intraday trades`);
            if (quote && quote.changePercent > 8) {
                warnings.push(`⚠️ Extended move (+${quote.changePercent.toFixed(1)}%) - consider smaller size`);
            }
        }
        if (quote && (quote.marketCap || 0) < 1000000000) {
            warnings.push(`⚠️ Small cap overnight risk - volatile gaps possible`);
        }
        if (quote && quote.volume < 1000000) {
            warnings.push(`⚠️ Lower volume - may have liquidity issues`);
        }
        return warnings;
    }
    // Get market conditions
    getMarketConditions() {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        const timeDecimal = currentHour + currentMinute / 60;
        // Convert to ET (simplified - doesn't handle DST)
        const etTime = timeDecimal - 5 // Assuming EST
        ;
        return {
            timeOfDay: `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`,
            isOptimalScanTime: etTime >= 12 && etTime <= 16,
            marketHours: etTime >= 9.5 && etTime <= 16 // 9:30-16:00 ET
        };
    }
    // Get sector for symbol (reuse from previous implementation)
    getSectorForSymbol(symbol) {
        const techSymbols = [
            'MSFT',
            'NVDA',
            'GOOG',
            'GOOGL',
            'META',
            'AVGO',
            'TSM',
            'ORCL',
            'CSCO',
            'AMD',
            'ASML',
            'MU',
            'LRCX',
            'PLTR',
            'APP',
            'NET',
            'DDOG',
            'ZS',
            'SHOP',
            'SOUN',
            'IONQ',
            'RGTI',
            'RIOT',
            'HUT',
            'IREN',
            'ASTS',
            'NBIS'
        ];
        const financialSymbols = [
            'JPM',
            'BAC',
            'MS',
            'SCHW',
            'C',
            'HOOD',
            'SOFI',
            'TIGR',
            'FUTU'
        ];
        const healthcareSymbols = [
            'JNJ',
            'ABBV',
            'MRK',
            'GILD'
        ];
        const industrialSymbols = [
            'GE',
            'CAT',
            'BA',
            'GEV',
            'UAL',
            'VRT',
            'RKLB'
        ];
        const materialsSymbols = [
            'AEM',
            'NEM',
            'PAAS',
            'BTG',
            'HL',
            'MP',
            'AG'
        ];
        const consumerSymbols = [
            'AMZN',
            'DIS',
            'SBUX',
            'MO',
            'DASH',
            'GM',
            'NCLH',
            'CELH',
            'LEVI',
            'ELF',
            'ETSY',
            'W'
        ];
        const communicationSymbols = [
            'NFLX',
            'RBLX',
            'BILI'
        ];
        const energySymbols = [
            'CEG',
            'VST',
            'CCJ'
        ];
        if (techSymbols.includes(symbol)) return 'Technology';
        if (financialSymbols.includes(symbol)) return 'Financial Services';
        if (healthcareSymbols.includes(symbol)) return 'Healthcare';
        if (industrialSymbols.includes(symbol)) return 'Industrial';
        if (materialsSymbols.includes(symbol)) return 'Materials';
        if (consumerSymbols.includes(symbol)) return 'Consumer';
        if (communicationSymbols.includes(symbol)) return 'Communication Services';
        if (energySymbols.includes(symbol)) return 'Energy';
        return 'Other';
    }
    // Quick scan with strategies
    async quickStrategyScan(prioritySymbols) {
        const summary = await this.scanWithStrategies(prioritySymbols, 8);
        return summary.topSetups;
    }
}
const enhancedSwingScanner = new EnhancedSwingScanner();
}),
"[project]/SwingTrader-AI-Package/src/app/api/scanner/strategies/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$data$2f$watchlist$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/src/data/watchlist.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const scanType = searchParams.get('type') || 'quick' // quick, full
        ;
        const accountSize = parseInt(searchParams.get('accountSize') || '100000');
        const limit = parseInt(searchParams.get('limit') || '20');
        console.log(`Starting ${scanType} strategy scan...`);
        // Set account size for position sizing
        const scanner = new (__turbopack_context__.r("[project]/SwingTrader-AI-Package/src/lib/enhancedSwingScanner.ts [app-route] (ecmascript)")).EnhancedSwingScanner(accountSize);
        let summary;
        if (scanType === 'full') {
            summary = await scanner.scanWithStrategies(__TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$data$2f$watchlist$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ALL_SYMBOLS"], 1); // Sequential to prevent rate limiting
        } else {
            summary = await scanner.scanWithStrategies(__TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$data$2f$watchlist$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIORITY_SYMBOLS"], 2); // Very conservative to prevent rate limiting
        }
        // Limit results if requested
        const limitedSummary = {
            ...summary,
            topSetups: summary.topSetups.slice(0, limit)
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(limitedSummary);
    } catch (error) {
        console.error('Error in strategy scanner API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to perform strategy scan'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__eec3c09d._.js.map
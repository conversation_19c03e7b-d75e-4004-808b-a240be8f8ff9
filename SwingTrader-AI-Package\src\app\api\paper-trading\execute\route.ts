import { NextRequest, NextResponse } from 'next/server'
import { EnhancedScanResult } from '@/lib/enhancedSwingScanner'
import { PaperTrade } from '@/types/paperTrading'
import { v4 as uuidv4 } from 'uuid'

export async function POST(request: NextRequest) {
  try {
    const { scanResult, accountSize }: { 
      scanResult: EnhancedScanResult
      accountSize: number 
    } = await request.json()

    if (!scanResult || !accountSize) {
      return NextResponse.json({ error: 'Scan result and account size are required' }, { status: 400 })
    }

    // Determine which setup to use
    const setup = scanResult.overnightSetup || scanResult.breakoutSetup
    if (!setup) {
      return NextResponse.json({ error: 'No trading setup found' }, { status: 400 })
    }

    const strategy = scanResult.overnightSetup ? 'overnight_momentum' : 'technical_breakout'

    // Calculate position sizing with risk management
    const entryPrice = setup.entryPrice
    const stopLoss = setup.stopLoss
    const riskPerShare = Math.abs(entryPrice - stopLoss)
    
    // Use 1-2% risk per trade (configurable)
    const riskPercentage = 1.5 // 1.5% of account
    const riskAmount = accountSize * (riskPercentage / 100)
    
    // Calculate position size based on risk
    let positionSize = Math.floor(riskAmount / riskPerShare)
    
    // Ensure we don't exceed reasonable position limits
    const maxPositionValue = accountSize * 0.1 // Max 10% of account per position
    const maxShares = Math.floor(maxPositionValue / entryPrice)
    positionSize = Math.min(positionSize, maxShares)
    
    // Minimum position size check
    if (positionSize < 1) {
      return NextResponse.json({ 
        error: 'Position size too small - increase account size or reduce risk per share' 
      }, { status: 400 })
    }

    // Create paper trade record
    const paperTrade: PaperTrade = {
      id: uuidv4(),
      symbol: scanResult.symbol,
      strategy,
      entryPrice,
      stopLoss,
      targets: setup.targets,
      positionSize,
      accountSize,
      riskAmount,
      riskPercentage,
      entryTime: new Date().toISOString(),
      status: 'open',
      currentPrice: scanResult.quote.price,
      unrealizedPnL: 0,
      notes: `${strategy} setup - Score: ${scanResult.overallScore}/100`
    }

    // Calculate potential profit/loss
    const potentialProfit = (setup.targets[0] - entryPrice) * positionSize
    const potentialLoss = (entryPrice - stopLoss) * positionSize
    const riskRewardRatio = potentialProfit / Math.abs(potentialLoss)

    const executionSummary = {
      paperTrade,
      executionDetails: {
        symbol: scanResult.symbol,
        strategy,
        entryPrice: entryPrice,
        stopLoss: stopLoss,
        firstTarget: setup.targets[0],
        positionSize,
        positionValue: entryPrice * positionSize,
        riskAmount,
        riskPercentage,
        potentialProfit,
        potentialLoss,
        riskRewardRatio: Math.round(riskRewardRatio * 100) / 100,
        executionTime: new Date().toISOString()
      }
    }

    // In a real application, you would save this to a database
    // For now, we'll return the trade details for client-side storage
    
    return NextResponse.json(executionSummary)

  } catch (error) {
    console.error('Paper trading execution error:', error)
    return NextResponse.json(
      { error: 'Failed to execute paper trade' },
      { status: 500 }
    )
  }
}

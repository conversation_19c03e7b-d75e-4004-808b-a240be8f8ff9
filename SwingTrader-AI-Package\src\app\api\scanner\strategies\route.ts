import { NextRequest, NextResponse } from 'next/server'
import { enhancedSwingScanner } from '@/lib/enhancedSwingScanner'
import { PRIORITY_SYMBOLS, ALL_SYMBOLS } from '@/data/watchlist'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const scanType = searchParams.get('type') || 'quick' // quick, full
    const accountSize = parseInt(searchParams.get('accountSize') || '100000')
    const limit = parseInt(searchParams.get('limit') || '20')
    
    console.log(`Starting ${scanType} strategy scan...`)
    
    // Set account size for position sizing
    const scanner = new (require('@/lib/enhancedSwingScanner').EnhancedSwingScanner)(accountSize)
    
    let summary
    if (scanType === 'full') {
      summary = await scanner.scanWithStrategies(ALL_SYMBOLS, 1) // Sequential to prevent rate limiting
    } else {
      summary = await scanner.scanWithStrategies(PRIORITY_SYMBOLS, 2) // Very conservative to prevent rate limiting
    }
    
    // Limit results if requested
    const limitedSummary = {
      ...summary,
      topSetups: summary.topSetups?.slice(0, limit) || []
    }

    console.log('📊 API returning summary:', limitedSummary) // Debug logging

    return NextResponse.json(limitedSummary)
  } catch (error) {
    console.error('Error in strategy scanner API:', error)
    return NextResponse.json(
      { error: 'Failed to perform strategy scan' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { PolygonAPI } from '@/lib/polygon'
import { SwingTradingAnalyzer } from '@/lib/swingAnalysis'
import { format, subDays } from 'date-fns'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ symbol: string }> }
) {
  try {
    const { symbol } = await params
    const { searchParams } = new URL(request.url)
    
    const timeframe = searchParams.get('timeframe') || '1D'
    const days = parseInt(searchParams.get('days') || '100')
    
    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      )
    }

    // Get historical data for analysis
    const polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)
    const to = format(new Date(), 'yyyy-MM-dd')
    const from = format(subDays(new Date(), days), 'yyyy-MM-dd')
    
    const historicalData = await polygonAPI.getHistoricalData(
      symbol.toUpperCase(),
      'day',
      1,
      from,
      to
    )

    if (historicalData.length < 50) {
      return NextResponse.json(
        { error: 'Insufficient historical data for analysis' },
        { status: 400 }
      )
    }

    // Perform swing trading analysis
    const analysis = SwingTradingAnalyzer.analyzeSwingTrade(
      symbol.toUpperCase(),
      historicalData,
      timeframe
    )
    
    return NextResponse.json(analysis)
  } catch (error) {
    console.error('Error in swing analysis API:', error)
    return NextResponse.json(
      { error: 'Failed to perform swing trading analysis' },
      { status: 500 }
    )
  }
}

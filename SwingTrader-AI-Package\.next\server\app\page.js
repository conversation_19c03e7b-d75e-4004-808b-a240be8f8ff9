var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/app/page.js")
R.c("server/chunks/ssr/7b731_4c2f090f._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/SwingTrader-AI-Package_src_app_27d51553._.js")
R.c("server/chunks/ssr/[root-of-the-server]__3860e9de._.js")
R.c("server/chunks/ssr/7b731_next_dist_client_components_302a3fb3._.js")
R.c("server/chunks/ssr/7b731_next_dist_client_components_builtin_forbidden_cb28475c.js")
R.c("server/chunks/ssr/7b731_next_dist_client_components_builtin_unauthorized_c77ffe00.js")
R.c("server/chunks/ssr/7b731_next_dist_client_components_builtin_global-error_1516dfa5.js")
R.c("server/chunks/ssr/7b731_next_dist_a5964036._.js")
R.c("server/chunks/ssr/[root-of-the-server]__cc3a8831._.js")
R.m("[project]/SwingTrader-AI-Package/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/SwingTrader-AI-Package/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/SwingTrader-AI-Package/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/SwingTrader-AI-Package/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/SwingTrader-AI-Package/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/SwingTrader-AI-Package/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/SwingTrader-AI-Package/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/SwingTrader-AI-Package/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/SwingTrader-AI-Package/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/SwingTrader-AI-Package/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/SwingTrader-AI-Package/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/SwingTrader-AI-Package/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/SwingTrader-AI-Package/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/SwingTrader-AI-Package/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/SwingTrader-AI-Package/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/SwingTrader-AI-Package/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/SwingTrader-AI-Package/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/SwingTrader-AI-Package/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/SwingTrader-AI-Package/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/SwingTrader-AI-Package/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/SwingTrader-AI-Package/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports

module.exports = [
"[project]/SwingTrader-AI-Package/.next-internal/server/app/api/stocks/quote/[symbol]/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/tty [external] (tty, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}),
"[externals]/os [external] (os, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[project]/SwingTrader-AI-Package/src/lib/fmp.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FMPAPI",
    ()=>FMPAPI,
    "fmpAPI",
    ()=>fmpAPI
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
const FMP_BASE_URL = 'https://financialmodelingprep.com/api';
const API_KEY = process.env.FMP_API_KEY;
class FMPAPI {
    apiKey;
    constructor(apiKey){
        this.apiKey = apiKey || API_KEY || '';
        if (!this.apiKey) {
            throw new Error('FMP API key is required');
        }
    }
    // Get real-time stock quote
    async getStockQuote(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/quote/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            const data = response.data[0];
            if (!data) {
                throw new Error(`No data found for symbol ${symbol}`);
            }
            return {
                symbol: data.symbol,
                name: data.name || data.symbol,
                price: data.price,
                change: data.change,
                changePercent: data.changesPercentage,
                volume: data.volume,
                marketCap: data.marketCap,
                pe: data.pe,
                dividend: undefined // Will be fetched separately if needed
            };
        } catch (error) {
            console.error('Error fetching FMP stock quote:', error);
            throw new Error(`Failed to fetch quote for ${symbol}`);
        }
    }
    // Get company profile
    async getCompanyProfile(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/profile/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data[0];
        } catch (error) {
            console.error('Error fetching company profile:', error);
            return null;
        }
    }
    // Get financial ratios
    async getFinancialRatios(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/ratios/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data[0] // Most recent ratios
            ;
        } catch (error) {
            console.error('Error fetching financial ratios:', error);
            return null;
        }
    }
    // Get key metrics
    async getKeyMetrics(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/key-metrics/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data[0] // Most recent metrics
            ;
        } catch (error) {
            console.error('Error fetching key metrics:', error);
            return null;
        }
    }
    // Get analyst recommendations
    async getAnalystRecommendations(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching analyst recommendations:', error);
            return [];
        }
    }
    // Get earnings calendar
    async getEarningsCalendar(from, to) {
        try {
            const params = {
                apikey: this.apiKey
            };
            if (from) params.from = from;
            if (to) params.to = to;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/earning_calendar`, {
                params
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching earnings calendar:', error);
            return [];
        }
    }
    // Get economic calendar
    async getEconomicCalendar(from, to) {
        try {
            const params = {
                apikey: this.apiKey
            };
            if (from) params.from = from;
            if (to) params.to = to;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/economic_calendar`, {
                params
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching economic calendar:', error);
            return [];
        }
    }
    // Search for stocks
    async searchStocks(query, limit = 10) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/search`, {
                params: {
                    query,
                    limit,
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error searching stocks:', error);
            return [];
        }
    }
    // Get sector performance
    async getSectorPerformance() {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/sector-performance`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching sector performance:', error);
            return [];
        }
    }
    // Get market gainers/losers
    async getMarketMovers(type) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/stock_market/${type}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error(`Error fetching market ${type}:`, error);
            return [];
        }
    }
}
const fmpAPI = new FMPAPI();
}),
"[project]/SwingTrader-AI-Package/src/app/api/stocks/quote/[symbol]/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$fmp$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/src/lib/fmp.ts [app-route] (ecmascript)");
;
;
async function GET(request, { params }) {
    try {
        const { symbol } = await params;
        if (!symbol) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Symbol parameter is required'
            }, {
                status: 400
            });
        }
        // Use FMP API for stock quotes
        const fmpAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$fmp$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FMPAPI"](process.env.FMP_API_KEY);
        const quote = await fmpAPI.getStockQuote(symbol.toUpperCase());
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(quote);
    } catch (error) {
        console.error('Error in quote API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch stock quote'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__03524e93._.js.map
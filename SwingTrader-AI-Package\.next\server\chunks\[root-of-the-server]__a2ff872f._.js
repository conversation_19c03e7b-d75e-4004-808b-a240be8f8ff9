module.exports = [
"[project]/SwingTrader-AI-Package/.next-internal/server/app/api/scanner/test/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/SwingTrader-AI-Package/src/app/api/scanner/test/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/next/server.js [app-route] (ecmascript)");
;
async function GET(request) {
    try {
        // Simulate a successful scan with mock data
        const mockResults = [
            {
                symbol: 'AAPL',
                name: 'Apple Inc.',
                sector: 'Technology',
                quote: {
                    symbol: 'AAPL',
                    name: 'Apple Inc.',
                    price: 175.50,
                    change: 2.25,
                    changePercent: 1.30,
                    volume: 45000000,
                    marketCap: 2800000000000,
                    pe: 28.5,
                    dividend: 0.24
                },
                overnightSetup: {
                    strategy: 'overnight_momentum',
                    score: 85,
                    confidence: 'HIGH',
                    entry: 175.50,
                    stopLoss: 172.00,
                    target1: 180.00,
                    target2: 185.00,
                    riskReward: 2.57,
                    positionSize: 285,
                    riskAmount: 997.50,
                    signals: [
                        'Strong overnight gap up',
                        'Above 8 EMA',
                        'High volume confirmation',
                        'Bullish momentum'
                    ],
                    timeframe: '1-3 days',
                    marketCondition: 'Bullish trend continuation'
                },
                bestStrategy: 'overnight_momentum',
                overallScore: 85,
                rank: 1,
                scanTime: new Date().toISOString(),
                alerts: [
                    'High volume breakout'
                ],
                riskWarnings: []
            },
            {
                symbol: 'NVDA',
                name: 'NVIDIA Corporation',
                sector: 'Technology',
                quote: {
                    symbol: 'NVDA',
                    name: 'NVIDIA Corporation',
                    price: 485.20,
                    change: 8.75,
                    changePercent: 1.84,
                    volume: 35000000,
                    marketCap: 1200000000000,
                    pe: 65.2,
                    dividend: 0.16
                },
                breakoutSetup: {
                    strategy: 'technical_breakout',
                    score: 78,
                    confidence: 'HIGH',
                    entry: 485.20,
                    stopLoss: 475.00,
                    target1: 500.00,
                    target2: 515.00,
                    riskReward: 2.93,
                    positionSize: 98,
                    riskAmount: 999.60,
                    signals: [
                        'Breakout above resistance',
                        'Volume surge',
                        'RSI momentum',
                        'Moving average support'
                    ],
                    timeframe: '2-5 days',
                    marketCondition: 'Technical breakout pattern'
                },
                bestStrategy: 'technical_breakout',
                overallScore: 78,
                rank: 2,
                scanTime: new Date().toISOString(),
                alerts: [
                    'Technical breakout confirmed'
                ],
                riskWarnings: []
            },
            {
                symbol: 'TSLA',
                name: 'Tesla, Inc.',
                sector: 'Consumer Discretionary',
                quote: {
                    symbol: 'TSLA',
                    name: 'Tesla, Inc.',
                    price: 245.80,
                    change: 5.20,
                    changePercent: 2.16,
                    volume: 28000000,
                    marketCap: 780000000000,
                    pe: 45.8,
                    dividend: 0
                },
                overnightSetup: {
                    strategy: 'overnight_momentum',
                    score: 72,
                    confidence: 'MEDIUM',
                    entry: 245.80,
                    stopLoss: 240.00,
                    target1: 255.00,
                    target2: 265.00,
                    riskReward: 3.31,
                    positionSize: 172,
                    riskAmount: 996.80,
                    signals: [
                        'Gap up with volume',
                        'Above key moving averages',
                        'Momentum building',
                        'Sector strength'
                    ],
                    timeframe: '1-4 days',
                    marketCondition: 'Momentum continuation'
                },
                bestStrategy: 'overnight_momentum',
                overallScore: 72,
                rank: 3,
                scanTime: new Date().toISOString(),
                alerts: [
                    'Strong momentum signal'
                ],
                riskWarnings: [
                    'High volatility stock'
                ]
            }
        ];
        const summary = {
            totalScanned: 16,
            validSetups: 3,
            avgScore: 78.3,
            topSector: 'Technology',
            scanDuration: '2.1s',
            timestamp: new Date().toISOString(),
            results: mockResults
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(summary);
    } catch (error) {
        console.error('Error in test scanner:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to run test scan'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__a2ff872f._.js.map
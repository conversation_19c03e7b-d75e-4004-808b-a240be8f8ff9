'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Zap, Target, Clock, AlertTriangle, <PERSON><PERSON>dingUp, Moon, BarChart3 } from 'lucide-react'
import { EnhancedScanResult, StrategyScanSummary } from '@/lib/enhancedSwingScanner'
import { formatCurrency, formatPercentage } from '@/lib/utils'

interface StrategyScannerProps {
  autoScan?: boolean
  accountSize?: number
}

export function StrategyScanner({ autoScan = false, accountSize = 100000 }: StrategyScannerProps) {
  const [isScanning, setIsScanning] = useState(false)
  const [scanResults, setScanResults] = useState<StrategyScanSummary | null>(null)
  const [selectedStrategy, setSelectedStrategy] = useState<'both' | 'overnight' | 'breakout'>('both')
  const [error, setError] = useState<string | null>(null)
  const [userAccountSize, setUserAccountSize] = useState(accountSize)

  // Auto-scan on component mount if enabled
  useEffect(() => {
    if (autoScan) {
      handleStrategyScan('quick')
    }
  }, [autoScan])

  const handleStrategyScan = async (scanType: 'quick' | 'full' | 'test') => {
    setIsScanning(true)
    setError(null)
    setScanResults(null)

    try {
      let response
      if (scanType === 'test') {
        response = await fetch('/api/scanner/test')
      } else {
        response = await fetch(
          `/api/scanner/strategies?type=${scanType}&accountSize=${userAccountSize}&limit=20`
        )
      }

      if (!response.ok) throw new Error('Failed to fetch strategy scan results')

      const data = await response.json()
      setScanResults(data)
    } catch (err) {
      setError('Failed to perform strategy scan. Please try again.')
      console.error('Strategy scan error:', err)
    } finally {
      setIsScanning(false)
    }
  }

  const getStrategyIcon = (strategy: string) => {
    switch (strategy) {
      case 'overnight_momentum':
        return <Moon className="h-4 w-4 text-purple-400" />
      case 'technical_breakout':
        return <TrendingUp className="h-4 w-4 text-green-400" />
      default:
        return <BarChart3 className="h-4 w-4 text-blue-400" />
    }
  }

  const getStrategyName = (strategy: string) => {
    switch (strategy) {
      case 'overnight_momentum':
        return 'Overnight Momentum'
      case 'technical_breakout':
        return 'Technical Breakout'
      default:
        return 'Mixed Strategy'
    }
  }

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case 'overnight_momentum':
        return 'bg-purple-500/20 text-purple-400'
      case 'technical_breakout':
        return 'bg-green-500/20 text-green-400'
      default:
        return 'bg-blue-500/20 text-blue-400'
    }
  }

  const filteredResults = scanResults?.topSetups.filter(result => {
    if (selectedStrategy === 'both') return true
    if (selectedStrategy === 'overnight') return result.overnightSetup
    if (selectedStrategy === 'breakout') return result.breakoutSetup
    return true
  }) || []

  return (
    <div className="space-y-6">
      {/* Strategy Scanner Controls */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Zap className="mr-2 h-5 w-5 text-yellow-400" />
            Professional Swing Trading Strategies
          </CardTitle>
          <CardDescription className="text-slate-300">
            Automated scanner implementing proven swing trading methodologies with precise entry/exit rules
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Account Size Input */}
          <div className="mb-4">
            <label className="block text-sm text-slate-300 mb-2">Account Size (for position sizing)</label>
            <input
              type="number"
              value={userAccountSize}
              onChange={(e) => setUserAccountSize(parseInt(e.target.value) || 100000)}
              className="w-32 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm"
              min="10000"
              step="10000"
              disabled={isScanning}
            />
          </div>

          {/* Strategy Filter */}
          <div className="mb-4">
            <label className="block text-sm text-slate-300 mb-2">Strategy Filter</label>
            <div className="flex gap-2">
              <Button
                variant={selectedStrategy === 'both' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStrategy('both')}
                className={selectedStrategy === 'both' ? 'bg-blue-600' : 'border-slate-600 text-slate-300'}
              >
                All Strategies
              </Button>
              <Button
                variant={selectedStrategy === 'overnight' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStrategy('overnight')}
                className={selectedStrategy === 'overnight' ? 'bg-purple-600' : 'border-slate-600 text-slate-300'}
              >
                <Moon className="mr-1 h-3 w-3" />
                Overnight
              </Button>
              <Button
                variant={selectedStrategy === 'breakout' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStrategy('breakout')}
                className={selectedStrategy === 'breakout' ? 'bg-green-600' : 'border-slate-600 text-slate-300'}
              >
                <TrendingUp className="mr-1 h-3 w-3" />
                Breakout
              </Button>
            </div>
          </div>

          {/* Scan Buttons */}
          <div className="flex gap-4 mb-4">
            <Button
              onClick={() => handleStrategyScan('quick')}
              disabled={isScanning}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isScanning ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Zap className="mr-2 h-4 w-4" />
              )}
              Quick Strategy Scan
            </Button>
            
            <Button
              onClick={() => handleStrategyScan('full')}
              disabled={isScanning}
              variant="outline"
              className="border-slate-600 text-slate-300 hover:bg-slate-800"
            >
              {isScanning ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Target className="mr-2 h-4 w-4" />
              )}
              Full Strategy Scan
            </Button>

            <Button
              onClick={() => handleStrategyScan('test')}
              disabled={isScanning}
              variant="outline"
              className="bg-green-600/10 border-green-600 text-green-400 hover:bg-green-600/20"
            >
              {isScanning ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <span className="mr-2">🧪</span>
              )}
              Test
            </Button>
          </div>

          {/* Market Conditions */}
          {scanResults?.marketConditions && (
            <div className="p-3 bg-slate-700/50 rounded-lg">
              <div className="flex items-center justify-between text-sm">
                <span className="text-slate-300">Market Conditions:</span>
                <div className="flex items-center gap-4">
                  <span className="text-white">{scanResults.marketConditions.timeOfDay}</span>
                  <Badge className={scanResults.marketConditions.isOptimalScanTime 
                    ? 'bg-green-500/20 text-green-400' 
                    : 'bg-yellow-500/20 text-yellow-400'
                  }>
                    {scanResults.marketConditions.isOptimalScanTime ? 'Optimal Scan Time' : 'Outside Optimal Hours'}
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {isScanning && (
            <div className="text-center py-4">
              <Loader2 className="mx-auto h-8 w-8 animate-spin text-blue-400" />
              <p className="text-slate-300 mt-2">
                Analyzing stocks for professional swing trading setups...
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="bg-red-900/20 border-red-500/50">
          <CardContent className="p-6">
            <p className="text-red-300 text-center">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Strategy Scan Results */}
      {scanResults && (
        <div className="space-y-6">
          {/* Scan Summary */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Strategy Scan Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{scanResults.totalScanned}</div>
                  <div className="text-sm text-slate-300">Stocks Scanned</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{scanResults.overnightSetups}</div>
                  <div className="text-sm text-slate-300">Overnight Setups</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{scanResults.breakoutSetups}</div>
                  <div className="text-sm text-slate-300">Breakout Setups</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{scanResults.bothStrategies}</div>
                  <div className="text-sm text-slate-300">Both Strategies</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Strategy Setups */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Target className="mr-2 h-5 w-5 text-green-400" />
                Professional Trading Setups ({filteredResults.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredResults.map((result, index) => (
                  <div key={result.symbol} className="p-4 bg-slate-700/50 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="text-lg font-bold text-white">#{result.rank}</div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-semibold text-white">{result.symbol}</span>
                            {result.bestStrategy && getStrategyIcon(result.bestStrategy)}
                            <Badge className={result.bestStrategy ? getStrategyColor(result.bestStrategy) : 'bg-gray-500/20 text-gray-400'}>
                              {result.bestStrategy ? getStrategyName(result.bestStrategy) : 'No Setup'}
                            </Badge>
                          </div>
                          <div className="text-sm text-slate-300">{result.name}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xl font-bold text-white">
                          {result.overallScore.toFixed(1)}/100
                        </div>
                        <div className="text-sm text-slate-300">Confidence</div>
                      </div>
                    </div>

                    {/* Strategy Details */}
                    {result.overnightSetup && (
                      <div className="mb-3 p-3 bg-purple-900/20 rounded border border-purple-500/30">
                        <div className="flex items-center mb-2">
                          <Moon className="mr-2 h-4 w-4 text-purple-400" />
                          <span className="text-purple-400 font-semibold">Overnight Momentum Strategy</span>
                          <Badge className="ml-2 bg-purple-500/20 text-purple-400">
                            {result.overnightSetup.confidence.toFixed(1)}% confidence
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                          <div>
                            <div className="text-slate-300">Entry</div>
                            <div className="text-white font-semibold">{formatCurrency(result.overnightSetup.entryPrice)}</div>
                          </div>
                          <div>
                            <div className="text-slate-300">Stop</div>
                            <div className="text-red-400 font-semibold">{formatCurrency(result.overnightSetup.stopLoss)}</div>
                          </div>
                          <div>
                            <div className="text-slate-300">Target</div>
                            <div className="text-green-400 font-semibold">{formatCurrency(result.overnightSetup.targets[0])}</div>
                          </div>
                          <div>
                            <div className="text-slate-300">Position Size</div>
                            <div className="text-white font-semibold">{result.overnightSetup.positionSize.toLocaleString()} shares</div>
                          </div>
                        </div>
                      </div>
                    )}

                    {result.breakoutSetup && (
                      <div className="mb-3 p-3 bg-green-900/20 rounded border border-green-500/30">
                        <div className="flex items-center mb-2">
                          <TrendingUp className="mr-2 h-4 w-4 text-green-400" />
                          <span className="text-green-400 font-semibold">Technical Breakout Strategy</span>
                          <Badge className="ml-2 bg-green-500/20 text-green-400">
                            {result.breakoutSetup.confidence.toFixed(1)}% confidence
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                          <div>
                            <div className="text-slate-300">Entry</div>
                            <div className="text-white font-semibold">{formatCurrency(result.breakoutSetup.entryPrice)}</div>
                          </div>
                          <div>
                            <div className="text-slate-300">8-EMA Stop</div>
                            <div className="text-red-400 font-semibold">{formatCurrency(result.breakoutSetup.stopLoss)}</div>
                          </div>
                          <div>
                            <div className="text-slate-300">First Target</div>
                            <div className="text-green-400 font-semibold">{formatCurrency(result.breakoutSetup.targets[0])}</div>
                          </div>
                          <div>
                            <div className="text-slate-300">Position Size</div>
                            <div className="text-white font-semibold">{result.breakoutSetup.positionSize.toLocaleString()} shares</div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Alerts and Warnings */}
                    {result.alerts.length > 0 && (
                      <div className="mb-2">
                        <div className="text-sm text-slate-300 mb-1">Trading Alerts:</div>
                        {result.alerts.map((alert, i) => (
                          <div key={i} className="text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded mb-1">
                            {alert}
                          </div>
                        ))}
                      </div>
                    )}

                    {result.riskWarnings.length > 0 && (
                      <div>
                        <div className="text-sm text-slate-300 mb-1">Risk Warnings:</div>
                        {result.riskWarnings.map((warning, i) => (
                          <div key={i} className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded mb-1 flex items-center">
                            <AlertTriangle className="mr-1 h-3 w-3" />
                            {warning}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

import { SwingTradingStrategies, StrategySetup } from './swingStrategies'
import { PolygonAPI } from './polygon'
import { FMPAPI } from './fmp'
import { StockData, CandlestickData } from '@/types/trading'
import { format, subDays } from 'date-fns'

export interface EnhancedScanResult {
  symbol: string
  name: string
  sector: string
  quote: StockData
  overnightSetup?: StrategySetup
  breakoutSetup?: StrategySetup
  bestStrategy?: 'overnight_momentum' | 'technical_breakout'
  overallScore: number
  rank: number
  scanTime: string
  alerts: string[]
  riskWarnings: string[]
}

export interface StrategyScanSummary {
  totalScanned: number
  overnightSetups: number
  breakoutSetups: number
  bothStrategies: number
  topSetups: EnhancedScanResult[]
  scanDuration: number
  marketConditions: {
    timeOfDay: string
    isOptimalScanTime: boolean
    marketHours: boolean
  }
}

export class EnhancedSwingScanner {
  private fmpAPI: FMPAPI
  private polygonAPI: PolygonAPI
  private accountSize: number

  constructor(accountSize: number = 100000) {
    this.fmpAPI = new FMPAPI(process.env.FMP_API_KEY)
    this.polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)
    this.accountSize = accountSize
    console.log('🔧 Enhanced Swing Scanner initialized - Using Polygon API as primary data source (no rate limits)')
  }

  // Main enhanced scanning function
  async scanWithStrategies(
    symbols: string[], 
    maxConcurrent: number = 5
  ): Promise<StrategyScanSummary> {
    const startTime = Date.now()
    const results: EnhancedScanResult[] = []
    const failed: string[] = []

    console.log(`Starting enhanced strategy scan of ${symbols.length} stocks...`)

    // Check if we're in optimal scan time (12:00-16:00 ET)
    const marketConditions = this.getMarketConditions()

    // Process stocks in batches
    for (let i = 0; i < symbols.length; i += maxConcurrent) {
      const batch = symbols.slice(i, i + maxConcurrent)
      const batchPromises = batch.map(symbol => this.scanSingleStockStrategies(symbol))
      
      const batchResults = await Promise.allSettled(batchPromises)
      
      batchResults.forEach((result, index) => {
        const symbol = batch[index]
        if (result.status === 'fulfilled' && result.value) {
          results.push(result.value)
        } else {
          failed.push(symbol)
          console.warn(`Failed to scan ${symbol}:`, result.status === 'rejected' ? result.reason : 'Unknown error')
        }
      })

      // Rate limiting delay
      if (i + maxConcurrent < symbols.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    // Sort by overall score and assign ranks
    results.sort((a, b) => b.overallScore - a.overallScore)
    results.forEach((result, index) => {
      result.rank = index + 1
    })

    // Calculate summary statistics
    const overnightSetups = results.filter(r => r.overnightSetup).length
    const breakoutSetups = results.filter(r => r.breakoutSetup).length
    const bothStrategies = results.filter(r => r.overnightSetup && r.breakoutSetup).length

    const scanDuration = Date.now() - startTime

    return {
      totalScanned: symbols.length,
      overnightSetups,
      breakoutSetups,
      bothStrategies,
      topSetups: results.slice(0, 25), // Top 25 setups
      scanDuration,
      marketConditions
    }
  }

  // Scan individual stock for both strategies
  private async scanSingleStockStrategies(symbol: string): Promise<EnhancedScanResult | null> {
    try {
      console.log(`🔍 Starting scan for ${symbol}...`)

      // Get stock quote and historical data
      // Use Polygon as primary (no rate limits), fallback to FMP if needed
      let quote
      let historicalData

      try {
        console.log(`📊 Using Polygon API for ${symbol} quote (no rate limits)...`)
        [quote, historicalData] = await Promise.all([
          this.polygonAPI.getStockQuote(symbol),
          this.getHistoricalData(symbol)
        ])
        console.log(`✅ Successfully retrieved Polygon quote for ${symbol}: $${quote.price}`)
      } catch (error: any) {
        console.log(`⚠️ Polygon failed for ${symbol}, trying FMP fallback...`)
        try {
          [quote, historicalData] = await Promise.all([
            this.fmpAPI.getStockQuote(symbol),
            this.getHistoricalData(symbol)
          ])
          console.log(`✅ Successfully retrieved FMP quote for ${symbol}: $${quote.price}`)
        } catch (fmpError: any) {
          console.log(`❌ Both Polygon and FMP failed for ${symbol}, using historical data fallback...`)
          // Get historical data and use latest price as quote
          historicalData = await this.getHistoricalData(symbol)
          if (historicalData && historicalData.length > 0) {
            const latest = historicalData[historicalData.length - 1]
            const previous = historicalData[historicalData.length - 2]
            const change = previous ? latest.close - previous.close : 0
            const changePercent = previous ? ((change / previous.close) * 100) : 0

            quote = {
              symbol: symbol,
              name: symbol,
              price: latest.close,
              change: change,
              changePercent: changePercent,
              volume: latest.volume,
              marketCap: 0, // Not available from historical data
              pe: 0, // Not available from historical data
              dividend: undefined
            }
            console.log(`✅ Using historical data fallback for ${symbol}: $${latest.close}`)
          } else {
            throw new Error(`All data sources failed for ${symbol}`)
          }
        }
      }

      console.log(`📊 Quote for ${symbol}:`, {
        symbol: quote.symbol,
        price: quote.price,
        volume: quote.volume,
        changePercent: quote.changePercent
      })
      console.log(`📈 Historical data length for ${symbol}:`, historicalData?.length)

      if (!quote) {
        throw new Error(`No quote data available for ${symbol}`)
      }

      if (!historicalData || historicalData.length < 30) {
        throw new Error(`Insufficient historical data for ${symbol} - need at least 30 days, got ${historicalData?.length || 0}`)
      }

      console.log(`🧮 Analyzing overnight momentum for ${symbol}...`)
      // Analyze both strategies
      const overnightSetup = SwingTradingStrategies.analyzeOvernightMomentum(
        symbol, historicalData, quote, this.accountSize
      )
      console.log(`✅ Overnight setup for ${symbol}:`, overnightSetup ? 'Found' : 'None')

      console.log(`🧮 Analyzing technical breakout for ${symbol}...`)
      const breakoutSetup = SwingTradingStrategies.analyzeTechnicalBreakout(
        symbol, historicalData, quote, this.accountSize
      )
      console.log(`✅ Breakout setup for ${symbol}:`, breakoutSetup ? 'Found' : 'None')

      // Skip if no valid setups
      if (!overnightSetup && !breakoutSetup) {
        console.log(`⚠️ No valid setups found for ${symbol}`)
        return null
      }

      // Determine best strategy and overall score
      const { bestStrategy, overallScore } = this.calculateBestStrategy(overnightSetup, breakoutSetup)

      // Generate alerts and warnings
      const alerts = this.generateAlerts(overnightSetup, breakoutSetup, quote)
      const riskWarnings = this.generateRiskWarnings(overnightSetup, breakoutSetup, quote)

      const result = {
        symbol,
        name: quote.name,
        sector: this.getSectorForSymbol(symbol),
        quote,
        overnightSetup: overnightSetup || undefined,
        breakoutSetup: breakoutSetup || undefined,
        bestStrategy,
        overallScore,
        rank: 0, // Will be set after sorting
        scanTime: new Date().toISOString(),
        alerts,
        riskWarnings
      }

      console.log(`✅ Successfully scanned ${symbol} with score ${overallScore}`)
      return result
    } catch (error) {
      console.error(`❌ Error scanning ${symbol}:`, error)
      if (error instanceof Error) {
        console.error(`❌ Error message: ${error.message}`)
        console.error(`❌ Error stack: ${error.stack}`)
      }
      return null
    }
  }

  // Get historical data with optimized API usage
  private async getHistoricalData(symbol: string): Promise<CandlestickData[]> {
    const to = format(new Date(), 'yyyy-MM-dd')
    const from = format(subDays(new Date(), 100), 'yyyy-MM-dd') // 100 days should be sufficient

    try {
      console.log(`Fetching historical data for ${symbol} from ${from} to ${to}`)
      const data = await this.polygonAPI.getHistoricalData(symbol, 'day', 1, from, to)

      if (data.length === 0) {
        console.warn(`No historical data returned for ${symbol}`)
        throw new Error('No historical data available')
      }

      console.log(`Successfully fetched ${data.length} days of data for ${symbol}`)
      return data
    } catch (error) {
      console.error(`Failed to fetch historical data for ${symbol}:`, error)
      throw error
    }
  }

  // Calculate best strategy and overall score
  private calculateBestStrategy(
    overnight?: StrategySetup | null, 
    breakout?: StrategySetup | null
  ): { bestStrategy?: 'overnight_momentum' | 'technical_breakout', overallScore: number } {
    if (!overnight && !breakout) {
      return { overallScore: 0 }
    }

    if (overnight && !breakout) {
      return { bestStrategy: 'overnight_momentum', overallScore: overnight.confidence }
    }

    if (breakout && !overnight) {
      return { bestStrategy: 'technical_breakout', overallScore: breakout.confidence }
    }

    if (overnight && breakout) {
      // Both strategies valid - choose higher confidence
      if (overnight.confidence > breakout.confidence) {
        return { bestStrategy: 'overnight_momentum', overallScore: overnight.confidence + 5 } // Bonus for multiple setups
      } else {
        return { bestStrategy: 'technical_breakout', overallScore: breakout.confidence + 5 }
      }
    }

    return { overallScore: 0 }
  }

  // Generate trading alerts
  private generateAlerts(
    overnight?: StrategySetup | null,
    breakout?: StrategySetup | null,
    quote?: StockData
  ): string[] {
    const alerts: string[] = []

    if (overnight) {
      alerts.push(`🚀 OVERNIGHT MOMENTUM: Entry ${overnight.entryPrice.toFixed(2)}, Target ${overnight.targets[0].toFixed(2)}`)
      alerts.push(`⏰ Execute in final 30-60 min before close`)
      alerts.push(`🛑 Stop: ${overnight.stopLoss.toFixed(2)} (${((overnight.entryPrice - overnight.stopLoss) / overnight.entryPrice * 100).toFixed(1)}% risk)`)
    }

    if (breakout) {
      alerts.push(`📈 BREAKOUT SETUP: Entry ${breakout.entryPrice.toFixed(2)}, riding 8-EMA`)
      alerts.push(`🎯 Targets: ${breakout.targets.map(t => t.toFixed(2)).join(', ')}`)
      alerts.push(`🛑 Stop: Daily close below ${breakout.stopLoss.toFixed(2)}`)
    }

    if (quote && quote.changePercent > 5) {
      alerts.push(`🔥 Strong momentum: +${quote.changePercent.toFixed(1)}% today`)
    }

    return alerts
  }

  // Generate risk warnings
  private generateRiskWarnings(
    overnight?: StrategySetup | null,
    breakout?: StrategySetup | null,
    quote?: StockData
  ): string[] {
    const warnings: string[] = []

    if (overnight) {
      warnings.push(`⚠️ Overnight gap risk - size down vs intraday trades`)
      if (quote && quote.changePercent > 8) {
        warnings.push(`⚠️ Extended move (+${quote.changePercent.toFixed(1)}%) - consider smaller size`)
      }
    }

    if (quote && (quote.marketCap || 0) < 1000000000) {
      warnings.push(`⚠️ Small cap overnight risk - volatile gaps possible`)
    }

    if (quote && quote.volume < 1000000) {
      warnings.push(`⚠️ Lower volume - may have liquidity issues`)
    }

    return warnings
  }

  // Get market conditions
  private getMarketConditions() {
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    const timeDecimal = currentHour + currentMinute / 60

    // Convert to ET (simplified - doesn't handle DST)
    const etTime = timeDecimal - 5 // Assuming EST

    return {
      timeOfDay: `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`,
      isOptimalScanTime: etTime >= 12 && etTime <= 16, // 12:00-16:00 ET
      marketHours: etTime >= 9.5 && etTime <= 16 // 9:30-16:00 ET
    }
  }

  // Get sector for symbol (reuse from previous implementation)
  private getSectorForSymbol(symbol: string): string {
    const techSymbols = ['MSFT', 'NVDA', 'GOOG', 'GOOGL', 'META', 'AVGO', 'TSM', 'ORCL', 'CSCO', 'AMD', 'ASML', 'MU', 'LRCX', 'PLTR', 'APP', 'NET', 'DDOG', 'ZS', 'SHOP', 'SOUN', 'IONQ', 'RGTI', 'RIOT', 'HUT', 'IREN', 'ASTS', 'NBIS']
    const financialSymbols = ['JPM', 'BAC', 'MS', 'SCHW', 'C', 'HOOD', 'SOFI', 'TIGR', 'FUTU']
    const healthcareSymbols = ['JNJ', 'ABBV', 'MRK', 'GILD']
    const industrialSymbols = ['GE', 'CAT', 'BA', 'GEV', 'UAL', 'VRT', 'RKLB']
    const materialsSymbols = ['AEM', 'NEM', 'PAAS', 'BTG', 'HL', 'MP', 'AG']
    const consumerSymbols = ['AMZN', 'DIS', 'SBUX', 'MO', 'DASH', 'GM', 'NCLH', 'CELH', 'LEVI', 'ELF', 'ETSY', 'W']
    const communicationSymbols = ['NFLX', 'RBLX', 'BILI']
    const energySymbols = ['CEG', 'VST', 'CCJ']

    if (techSymbols.includes(symbol)) return 'Technology'
    if (financialSymbols.includes(symbol)) return 'Financial Services'
    if (healthcareSymbols.includes(symbol)) return 'Healthcare'
    if (industrialSymbols.includes(symbol)) return 'Industrial'
    if (materialsSymbols.includes(symbol)) return 'Materials'
    if (consumerSymbols.includes(symbol)) return 'Consumer'
    if (communicationSymbols.includes(symbol)) return 'Communication Services'
    if (energySymbols.includes(symbol)) return 'Energy'
    
    return 'Other'
  }

  // Quick scan with strategies
  async quickStrategyScan(prioritySymbols: string[]): Promise<EnhancedScanResult[]> {
    const summary = await this.scanWithStrategies(prioritySymbols, 8)
    return summary.topSetups
  }
}

// Create singleton instance
export const enhancedSwingScanner = new EnhancedSwingScanner()

{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/marketDataEnrichment.ts"], "sourcesContent": ["/**\n * Market Data Enrichment Service\n * Provides real-time market data to enhance AI analysis with specific, quantifiable information\n */\n\ninterface MarketDataEnrichment {\n  symbol: string\n  sector: string\n  recentEarnings?: EarningsData\n  analystActions?: AnalystAction[]\n  sectorPerformance?: SectorPerformance\n  volatilityMetrics?: VolatilityData\n  newsEvents?: NewsEvent[]\n  economicEvents?: EconomicEvent[]\n  technicalLevels?: TechnicalLevels\n}\n\ninterface EarningsData {\n  reportDate: string\n  actualEPS: number\n  estimatedEPS: number\n  beat: boolean\n  beatAmount: number\n  revenue: number\n  revenueEstimate: number\n  nextEarningsDate?: string\n}\n\ninterface AnalystAction {\n  firm: string\n  action: 'upgrade' | 'downgrade' | 'initiate' | 'maintain'\n  rating: string\n  priceTarget: number\n  previousTarget?: number\n  date: string\n  analyst: string\n}\n\ninterface SectorPerformance {\n  sectorETF: string\n  sectorReturn1W: number\n  sectorReturn1M: number\n  spyReturn1W: number\n  spyReturn1M: number\n  relativeStrength: number\n  ranking: number\n  totalSectors: number\n}\n\ninterface VolatilityData {\n  impliedVolatility30d: number\n  impliedVolatility6m: number\n  historicalVolatility30d: number\n  historicalVolatility6m: number\n  gapFrequency: number // % of days with >2% gaps\n  averageGapSize: number\n}\n\ninterface NewsEvent {\n  headline: string\n  source: string\n  date: string\n  sentiment: 'positive' | 'negative' | 'neutral'\n  relevanceScore: number\n}\n\ninterface EconomicEvent {\n  event: string\n  date: string\n  impact: 'high' | 'medium' | 'low'\n  actual?: number\n  forecast?: number\n  previous?: number\n  unit: string\n}\n\ninterface TechnicalLevels {\n  support: Array<{ price: number; strength: number; lastTest: string }>\n  resistance: Array<{ price: number; strength: number; lastTest: string }>\n  vwap: number\n  sma20: number\n  sma50: number\n  sma200: number\n  volumeProfile: Array<{ price: number; volume: number }>\n}\n\n/**\n * Enriches market data for AI analysis\n * In production, this would connect to real data sources like Bloomberg, Refinitiv, etc.\n */\nexport class MarketDataEnrichmentService {\n  \n  /**\n   * Get enriched market data for a symbol\n   */\n  async getEnrichedData(symbol: string, sector: string): Promise<MarketDataEnrichment> {\n    // In production, this would make real API calls to data providers\n    // For now, we'll simulate realistic data based on the symbol\n    \n    const enrichedData: MarketDataEnrichment = {\n      symbol,\n      sector,\n      recentEarnings: await this.getEarningsData(symbol),\n      analystActions: await this.getAnalystActions(symbol),\n      sectorPerformance: await this.getSectorPerformance(sector),\n      volatilityMetrics: await this.getVolatilityData(symbol),\n      newsEvents: await this.getNewsEvents(symbol),\n      economicEvents: await this.getEconomicEvents(sector),\n      technicalLevels: await this.getTechnicalLevels(symbol)\n    }\n\n    return enrichedData\n  }\n\n  private async getEarningsData(symbol: string): Promise<EarningsData | undefined> {\n    // Simulate recent earnings data\n    // In production: connect to earnings calendar API\n    const earningsMap: Record<string, EarningsData> = {\n      'NVDA': {\n        reportDate: '2024-11-21',\n        actualEPS: 2.45,\n        estimatedEPS: 2.30,\n        beat: true,\n        beatAmount: 0.15,\n        revenue: 35.1e9,\n        revenueEstimate: 33.2e9,\n        nextEarningsDate: '2025-02-20'\n      },\n      'AMD': {\n        reportDate: '2024-10-29',\n        actualEPS: 0.92,\n        estimatedEPS: 0.88,\n        beat: true,\n        beatAmount: 0.04,\n        revenue: 6.8e9,\n        revenueEstimate: 6.7e9,\n        nextEarningsDate: '2025-01-28'\n      }\n    }\n    \n    return earningsMap[symbol]\n  }\n\n  private async getAnalystActions(symbol: string): Promise<AnalystAction[]> {\n    // Simulate recent analyst actions\n    // In production: connect to analyst research APIs\n    const analystMap: Record<string, AnalystAction[]> = {\n      'NVDA': [\n        {\n          firm: 'Goldman Sachs',\n          action: 'upgrade',\n          rating: 'Buy',\n          priceTarget: 250,\n          previousTarget: 220,\n          date: '2024-11-22',\n          analyst: 'Toshiya Hari'\n        },\n        {\n          firm: 'Morgan Stanley',\n          action: 'maintain',\n          rating: 'Overweight',\n          priceTarget: 240,\n          date: '2024-11-20',\n          analyst: 'Joseph Moore'\n        }\n      ],\n      'AMD': [\n        {\n          firm: 'Bank of America',\n          action: 'upgrade',\n          rating: 'Buy',\n          priceTarget: 180,\n          previousTarget: 165,\n          date: '2024-10-30',\n          analyst: 'Vivek Arya'\n        }\n      ]\n    }\n    \n    return analystMap[symbol] || []\n  }\n\n  private async getSectorPerformance(sector: string): Promise<SectorPerformance> {\n    // Simulate sector performance data\n    // In production: connect to sector ETF data\n    const sectorMap: Record<string, SectorPerformance> = {\n      'Technology': {\n        sectorETF: 'XLK',\n        sectorReturn1W: 2.8,\n        sectorReturn1M: 8.5,\n        spyReturn1W: 1.2,\n        spyReturn1M: 4.3,\n        relativeStrength: 1.23,\n        ranking: 2,\n        totalSectors: 11\n      },\n      'Materials': {\n        sectorETF: 'XLB',\n        sectorReturn1W: 3.2,\n        sectorReturn1M: 6.8,\n        spyReturn1W: 1.2,\n        spyReturn1M: 4.3,\n        relativeStrength: 1.58,\n        ranking: 1,\n        totalSectors: 11\n      }\n    }\n    \n    return sectorMap[sector] || {\n      sectorETF: 'SPY',\n      sectorReturn1W: 1.2,\n      sectorReturn1M: 4.3,\n      spyReturn1W: 1.2,\n      spyReturn1M: 4.3,\n      relativeStrength: 1.0,\n      ranking: 6,\n      totalSectors: 11\n    }\n  }\n\n  private async getVolatilityData(symbol: string): Promise<VolatilityData> {\n    // Simulate volatility metrics\n    // In production: connect to options data providers\n    return {\n      impliedVolatility30d: 45,\n      impliedVolatility6m: 38,\n      historicalVolatility30d: 42,\n      historicalVolatility6m: 35,\n      gapFrequency: 15, // 15% of days have >2% gaps\n      averageGapSize: 3.2\n    }\n  }\n\n  private async getNewsEvents(symbol: string): Promise<NewsEvent[]> {\n    // Simulate recent news events\n    // In production: connect to news APIs like Bloomberg, Reuters\n    return [\n      {\n        headline: `${symbol} announces strategic partnership with major cloud provider`,\n        source: 'Reuters',\n        date: '2024-11-25',\n        sentiment: 'positive',\n        relevanceScore: 0.85\n      }\n    ]\n  }\n\n  private async getEconomicEvents(sector: string): Promise<EconomicEvent[]> {\n    // Simulate relevant economic events\n    // In production: connect to economic calendar APIs\n    return [\n      {\n        event: 'Federal Reserve Interest Rate Decision',\n        date: '2024-12-18',\n        impact: 'high',\n        actual: undefined,\n        forecast: -0.25,\n        previous: 0,\n        unit: 'percentage points'\n      }\n    ]\n  }\n\n  private async getTechnicalLevels(symbol: string): Promise<TechnicalLevels> {\n    // Simulate technical analysis levels\n    // In production: calculate from historical price data\n    return {\n      support: [\n        { price: 180, strength: 0.85, lastTest: '2024-11-15' },\n        { price: 175, strength: 0.72, lastTest: '2024-10-28' }\n      ],\n      resistance: [\n        { price: 195, strength: 0.78, lastTest: '2024-11-20' },\n        { price: 200, strength: 0.65, lastTest: '2024-11-10' }\n      ],\n      vwap: 187.5,\n      sma20: 185.2,\n      sma50: 178.8,\n      sma200: 165.4,\n      volumeProfile: [\n        { price: 185, volume: 2500000 },\n        { price: 190, volume: 1800000 }\n      ]\n    }\n  }\n}\n\nexport const marketDataService = new MarketDataEnrichmentService()\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAuFM,MAAM;IAEX;;GAEC,GACD,MAAM,gBAAgB,MAAc,EAAE,MAAc,EAAiC;QACnF,kEAAkE;QAClE,6DAA6D;QAE7D,MAAM,eAAqC;YACzC;YACA;YACA,gBAAgB,MAAM,IAAI,CAAC,eAAe,CAAC;YAC3C,gBAAgB,MAAM,IAAI,CAAC,iBAAiB,CAAC;YAC7C,mBAAmB,MAAM,IAAI,CAAC,oBAAoB,CAAC;YACnD,mBAAmB,MAAM,IAAI,CAAC,iBAAiB,CAAC;YAChD,YAAY,MAAM,IAAI,CAAC,aAAa,CAAC;YACrC,gBAAgB,MAAM,IAAI,CAAC,iBAAiB,CAAC;YAC7C,iBAAiB,MAAM,IAAI,CAAC,kBAAkB,CAAC;QACjD;QAEA,OAAO;IACT;IAEA,MAAc,gBAAgB,MAAc,EAAqC;QAC/E,gCAAgC;QAChC,kDAAkD;QAClD,MAAM,cAA4C;YAChD,QAAQ;gBACN,YAAY;gBACZ,WAAW;gBACX,cAAc;gBACd,MAAM;gBACN,YAAY;gBACZ,SAAS;gBACT,iBAAiB;gBACjB,kBAAkB;YACpB;YACA,OAAO;gBACL,YAAY;gBACZ,WAAW;gBACX,cAAc;gBACd,MAAM;gBACN,YAAY;gBACZ,SAAS;gBACT,iBAAiB;gBACjB,kBAAkB;YACpB;QACF;QAEA,OAAO,WAAW,CAAC,OAAO;IAC5B;IAEA,MAAc,kBAAkB,MAAc,EAA4B;QACxE,kCAAkC;QAClC,kDAAkD;QAClD,MAAM,aAA8C;YAClD,QAAQ;gBACN;oBACE,MAAM;oBACN,QAAQ;oBACR,QAAQ;oBACR,aAAa;oBACb,gBAAgB;oBAChB,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,QAAQ;oBACR,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,SAAS;gBACX;aACD;YACD,OAAO;gBACL;oBACE,MAAM;oBACN,QAAQ;oBACR,QAAQ;oBACR,aAAa;oBACb,gBAAgB;oBAChB,MAAM;oBACN,SAAS;gBACX;aACD;QACH;QAEA,OAAO,UAAU,CAAC,OAAO,IAAI,EAAE;IACjC;IAEA,MAAc,qBAAqB,MAAc,EAA8B;QAC7E,mCAAmC;QACnC,4CAA4C;QAC5C,MAAM,YAA+C;YACnD,cAAc;gBACZ,WAAW;gBACX,gBAAgB;gBAChB,gBAAgB;gBAChB,aAAa;gBACb,aAAa;gBACb,kBAAkB;gBAClB,SAAS;gBACT,cAAc;YAChB;YACA,aAAa;gBACX,WAAW;gBACX,gBAAgB;gBAChB,gBAAgB;gBAChB,aAAa;gBACb,aAAa;gBACb,kBAAkB;gBAClB,SAAS;gBACT,cAAc;YAChB;QACF;QAEA,OAAO,SAAS,CAAC,OAAO,IAAI;YAC1B,WAAW;YACX,gBAAgB;YAChB,gBAAgB;YAChB,aAAa;YACb,aAAa;YACb,kBAAkB;YAClB,SAAS;YACT,cAAc;QAChB;IACF;IAEA,MAAc,kBAAkB,MAAc,EAA2B;QACvE,8BAA8B;QAC9B,mDAAmD;QACnD,OAAO;YACL,sBAAsB;YACtB,qBAAqB;YACrB,yBAAyB;YACzB,wBAAwB;YACxB,cAAc;YACd,gBAAgB;QAClB;IACF;IAEA,MAAc,cAAc,MAAc,EAAwB;QAChE,8BAA8B;QAC9B,8DAA8D;QAC9D,OAAO;YACL;gBACE,UAAU,GAAG,OAAO,0DAA0D,CAAC;gBAC/E,QAAQ;gBACR,MAAM;gBACN,WAAW;gBACX,gBAAgB;YAClB;SACD;IACH;IAEA,MAAc,kBAAkB,MAAc,EAA4B;QACxE,oCAAoC;QACpC,mDAAmD;QACnD,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,UAAU,CAAC;gBACX,UAAU;gBACV,MAAM;YACR;SACD;IACH;IAEA,MAAc,mBAAmB,MAAc,EAA4B;QACzE,qCAAqC;QACrC,sDAAsD;QACtD,OAAO;YACL,SAAS;gBACP;oBAAE,OAAO;oBAAK,UAAU;oBAAM,UAAU;gBAAa;gBACrD;oBAAE,OAAO;oBAAK,UAAU;oBAAM,UAAU;gBAAa;aACtD;YACD,YAAY;gBACV;oBAAE,OAAO;oBAAK,UAAU;oBAAM,UAAU;gBAAa;gBACrD;oBAAE,OAAO;oBAAK,UAAU;oBAAM,UAAU;gBAAa;aACtD;YACD,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,eAAe;gBACb;oBAAE,OAAO;oBAAK,QAAQ;gBAAQ;gBAC9B;oBAAE,OAAO;oBAAK,QAAQ;gBAAQ;aAC/B;QACH;IACF;AACF;AAEO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/app/api/analysis/ai-setup/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport OpenAI from 'openai'\nimport { EnhancedScanResult } from '@/lib/enhancedSwingScanner'\nimport { AIAnalysis } from '@/types/paperTrading'\nimport { marketDataService } from '@/lib/marketDataEnrichment'\n\n// Use environment variable for security (fallback to hardcoded for local development only)\nconst apiKey = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************'\n\nconsole.log('🔑 Using API Key prefix:', apiKey.substring(0, 15))\nconsole.log('🔑 Source:', process.env.OPENAI_API_KEY ? 'Environment Variable' : 'Hardcoded Fallback')\n\nconst openai = new OpenAI({\n  apiKey: apiKey,\n})\n\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('🚀 AI Analysis endpoint called')\n    console.log('🔑 Using API key:', apiKey.substring(0, 15))\n\n    const { scanResult }: { scanResult: EnhancedScanResult } = await request.json()\n\n    if (!scanResult) {\n      return NextResponse.json({ error: 'Scan result is required' }, { status: 400 })\n    }\n\n    // Prepare context for AI analysis\n    const setup = scanResult.overnightSetup || scanResult.breakoutSetup\n    if (!setup) {\n      return NextResponse.json({ error: 'No trading setup found' }, { status: 400 })\n    }\n\n    const strategyType = scanResult.overnightSetup ? 'overnight momentum' : 'technical breakout'\n    const currentPrice = scanResult.quote.price\n    const changePercent = scanResult.quote.changePercent || 0\n\n    // Get enriched market data for more specific analysis\n    const enrichedData = await marketDataService.getEnrichedData(scanResult.symbol, scanResult.sector)\n\n    const prompt = `You are a professional swing trader providing DATA-DRIVEN analysis for ${scanResult.symbol} (${scanResult.name}).\n\nCRITICAL REQUIREMENT: Provide SPECIFIC, QUANTIFIABLE data points. NO generic statements allowed.\n\nCURRENT SETUP DATA:\n- Symbol: ${scanResult.symbol} (${scanResult.name})\n- Sector: ${scanResult.sector}\n- Strategy: ${strategyType}\n- Current Price: $${currentPrice}\n- Daily Change: ${changePercent.toFixed(2)}%\n- Entry: $${setup.entryPrice}\n- Stop Loss: $${setup.stopLoss}\n- Target: $${setup.targets[0]}\n- Setup Confidence: ${setup.confidence}%\n- Overall Score: ${scanResult.overallScore}/100\n- Alerts: ${scanResult.alerts.join(', ') || 'None'}\n- Risk Warnings: ${scanResult.riskWarnings.join(', ') || 'None'}\n\nENRICHED MARKET DATA:\n${enrichedData.recentEarnings ? `\nRECENT EARNINGS:\n- Report Date: ${enrichedData.recentEarnings.reportDate}\n- Actual EPS: $${enrichedData.recentEarnings.actualEPS} vs Est: $${enrichedData.recentEarnings.estimatedEPS}\n- Beat by: $${enrichedData.recentEarnings.beatAmount} (${enrichedData.recentEarnings.beat ? 'BEAT' : 'MISS'})\n- Revenue: $${(enrichedData.recentEarnings.revenue / 1e9).toFixed(1)}B vs Est: $${(enrichedData.recentEarnings.revenueEstimate / 1e9).toFixed(1)}B\n- Next Earnings: ${enrichedData.recentEarnings.nextEarningsDate}\n` : ''}\n${enrichedData.analystActions && enrichedData.analystActions.length > 0 ? `\nRECENT ANALYST ACTIONS:\n${enrichedData.analystActions.map(action =>\n  `- ${action.firm}: ${action.action.toUpperCase()} to ${action.rating}, $${action.priceTarget} target (${action.date})`\n).join('\\n')}\n` : ''}\nSECTOR PERFORMANCE:\n- Sector ETF (${enrichedData.sectorPerformance?.sectorETF}): +${enrichedData.sectorPerformance?.sectorReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.sectorReturn1M.toFixed(1)}% (1M)\n- S&P 500: +${enrichedData.sectorPerformance?.spyReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.spyReturn1M.toFixed(1)}% (1M)\n- Relative Strength: ${enrichedData.sectorPerformance?.relativeStrength.toFixed(2)}x\n- Sector Ranking: #${enrichedData.sectorPerformance?.ranking} of ${enrichedData.sectorPerformance?.totalSectors}\n\nVOLATILITY METRICS:\n- 30-day IV: ${enrichedData.volatilityMetrics?.impliedVolatility30d}% vs 6M avg: ${enrichedData.volatilityMetrics?.impliedVolatility6m}%\n- Gap Frequency: ${enrichedData.volatilityMetrics?.gapFrequency}% of days have >2% gaps\n- Average Gap Size: ${enrichedData.volatilityMetrics?.averageGapSize}%\n\nTECHNICAL LEVELS:\n- Support: ${enrichedData.technicalLevels?.support.map(s => `$${s.price} (strength: ${(s.strength * 100).toFixed(0)}%, last test: ${s.lastTest})`).join(', ')}\n- Resistance: ${enrichedData.technicalLevels?.resistance.map(r => `$${r.price} (strength: ${(r.strength * 100).toFixed(0)}%, last test: ${r.lastTest})`).join(', ')}\n- VWAP: $${enrichedData.technicalLevels?.vwap}\n- SMA20: $${enrichedData.technicalLevels?.sma20}, SMA50: $${enrichedData.technicalLevels?.sma50}\n\nMANDATORY ANALYSIS REQUIREMENTS:\n\n**MARKET CATALYSTS** - Must include SPECIFIC data:\n- Recent earnings with actual numbers and dates (e.g., \"Q3 EPS $2.45 vs $2.30 est, reported Nov 21\")\n- Analyst actions with firm names and targets (e.g., \"Goldman upgraded to Buy, $250 target on Nov 22\")\n- Sector performance vs benchmarks with exact % (e.g., \"Materials (XLB) +3.2% vs S&P +1.1% this week\")\n- Recent news with dates and sources (e.g., \"Reuters Nov 20: Company announced $2B contract\")\n- Economic data affecting stock (e.g., \"Fed cut rates 0.25% yesterday, benefits growth stocks\")\n\n**RISK ASSESSMENT** - Must include QUANTIFIABLE risks:\n- Volatility metrics (e.g., \"30-day IV at 45% vs 6-month avg 32%\")\n- Gap risk stats (e.g., \"Stock gaps >2% on 15% of trading days past 3 months\")\n- Support/resistance with context (e.g., \"$180 support held 3x in Oct, volume spike at $185\")\n- Market conditions with probabilities (e.g., \"Fed meeting Dec 18 has 70% chance of rate cut\")\n\n**KEY LEVELS** - Must be EXACT price points:\n- Support levels with historical significance\n- Resistance that matters for this trade\n- VWAP and moving averages currently relevant\n- Volume profile levels\n\nEXAMPLES:\n✅ GOOD: \"NVDA beat Q3 EPS by $0.15 ($2.45 vs $2.30), reported Nov 21. Goldman upgraded to $250 target Nov 22.\"\n❌ BAD: \"Market sentiment is bullish\" or \"sector performing well\"\n\nReturn JSON format:\n{\n  \"setupExplanation\": \"Technical setup explanation with specific price levels and indicators\",\n  \"catalysts\": [\"Specific catalysts with dates, numbers, sources\"],\n  \"riskAssessment\": \"Quantified risks with historical data and probabilities\",\n  \"keyLevels\": [\"Exact price levels: $XXX support (held 3x), $XXX resistance\"],\n  \"timeframe\": \"Specific timeframe with reasoning\",\n  \"confidence\": number 1-100\n}\n\nProvide professional-grade analysis a trader can act on immediately with REAL DATA POINTS.`\n\n    const completion = await openai.chat.completions.create({\n      model: \"gpt-4\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are a professional swing trading analyst providing DATA-DRIVEN analysis. You must include specific numbers, dates, sources, and quantifiable metrics. Never use generic statements. Focus on actionable intelligence with real market data.\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      temperature: 0.2, // Lower temperature for more focused, factual responses\n      max_tokens: 1500, // Increased for more detailed analysis\n    })\n\n    const aiResponse = completion.choices[0]?.message?.content\n    if (!aiResponse) {\n      throw new Error('No response from AI')\n    }\n\n    // Parse the JSON response\n    let analysis: Partial<AIAnalysis>\n    try {\n      analysis = JSON.parse(aiResponse)\n    } catch (parseError) {\n      // Fallback if JSON parsing fails\n      analysis = {\n        setupExplanation: aiResponse.substring(0, 300) + '...',\n        catalysts: ['AI analysis available - see full response'],\n        riskAssessment: 'Standard swing trading risks apply',\n        keyLevels: [`Entry: $${setup.entryPrice}`, `Stop: $${setup.stopLoss}`, `Target: $${setup.targets[0]}`],\n        timeframe: '3-10 trading days',\n        confidence: Math.round(setup.confidence)\n      }\n    }\n\n    const fullAnalysis: AIAnalysis = {\n      symbol: scanResult.symbol,\n      setupExplanation: analysis.setupExplanation || 'Setup analysis not available',\n      catalysts: analysis.catalysts || [],\n      riskAssessment: analysis.riskAssessment || 'Standard risks apply',\n      keyLevels: analysis.keyLevels || [],\n      timeframe: analysis.timeframe || '3-10 days',\n      confidence: analysis.confidence || Math.round(setup.confidence),\n      lastUpdated: new Date().toISOString()\n    }\n\n    return NextResponse.json(fullAnalysis)\n\n  } catch (error) {\n    console.error('AI Analysis error:', error)\n    return NextResponse.json(\n      { error: 'Failed to generate AI analysis' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAGA;;;;AAEA,2FAA2F;AAC3F,MAAM,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI;AAE7C,QAAQ,GAAG,CAAC,4BAA4B,OAAO,SAAS,CAAC,GAAG;AAC5D,QAAQ,GAAG,CAAC,cAAc,QAAQ,GAAG,CAAC,cAAc,GAAG,yBAAyB;AAEhF,MAAM,SAAS,IAAI,mLAAM,CAAC;IACxB,QAAQ;AACV;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,qBAAqB,OAAO,SAAS,CAAC,GAAG;QAErD,MAAM,EAAE,UAAU,EAAE,GAAuC,MAAM,QAAQ,IAAI;QAE7E,IAAI,CAAC,YAAY;YACf,OAAO,gLAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,kCAAkC;QAClC,MAAM,QAAQ,WAAW,cAAc,IAAI,WAAW,aAAa;QACnE,IAAI,CAAC,OAAO;YACV,OAAO,gLAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAyB,GAAG;gBAAE,QAAQ;YAAI;QAC9E;QAEA,MAAM,eAAe,WAAW,cAAc,GAAG,uBAAuB;QACxE,MAAM,eAAe,WAAW,KAAK,CAAC,KAAK;QAC3C,MAAM,gBAAgB,WAAW,KAAK,CAAC,aAAa,IAAI;QAExD,sDAAsD;QACtD,MAAM,eAAe,MAAM,yLAAiB,CAAC,eAAe,CAAC,WAAW,MAAM,EAAE,WAAW,MAAM;QAEjG,MAAM,SAAS,CAAC,uEAAuE,EAAE,WAAW,MAAM,CAAC,EAAE,EAAE,WAAW,IAAI,CAAC;;;;;UAKzH,EAAE,WAAW,MAAM,CAAC,EAAE,EAAE,WAAW,IAAI,CAAC;UACxC,EAAE,WAAW,MAAM,CAAC;YAClB,EAAE,aAAa;kBACT,EAAE,aAAa;gBACjB,EAAE,cAAc,OAAO,CAAC,GAAG;UACjC,EAAE,MAAM,UAAU,CAAC;cACf,EAAE,MAAM,QAAQ,CAAC;WACpB,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;oBACV,EAAE,MAAM,UAAU,CAAC;iBACtB,EAAE,WAAW,YAAY,CAAC;UACjC,EAAE,WAAW,MAAM,CAAC,IAAI,CAAC,SAAS,OAAO;iBAClC,EAAE,WAAW,YAAY,CAAC,IAAI,CAAC,SAAS,OAAO;;;AAGhE,EAAE,aAAa,cAAc,GAAG,CAAC;;eAElB,EAAE,aAAa,cAAc,CAAC,UAAU,CAAC;eACzC,EAAE,aAAa,cAAc,CAAC,SAAS,CAAC,UAAU,EAAE,aAAa,cAAc,CAAC,YAAY,CAAC;YAChG,EAAE,aAAa,cAAc,CAAC,UAAU,CAAC,EAAE,EAAE,aAAa,cAAc,CAAC,IAAI,GAAG,SAAS,OAAO;YAChG,EAAE,CAAC,aAAa,cAAc,CAAC,OAAO,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,WAAW,EAAE,CAAC,aAAa,cAAc,CAAC,eAAe,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG;iBAChI,EAAE,aAAa,cAAc,CAAC,gBAAgB,CAAC;AAChE,CAAC,GAAG,GAAG;AACP,EAAE,aAAa,cAAc,IAAI,aAAa,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC;;AAE3E,EAAE,aAAa,cAAc,CAAC,GAAG,CAAC,CAAA,SAChC,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,OAAO,MAAM,CAAC,WAAW,GAAG,IAAI,EAAE,OAAO,MAAM,CAAC,GAAG,EAAE,OAAO,WAAW,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EACtH,IAAI,CAAC,MAAM;AACb,CAAC,GAAG,GAAG;;cAEO,EAAE,aAAa,iBAAiB,EAAE,UAAU,IAAI,EAAE,aAAa,iBAAiB,EAAE,eAAe,QAAQ,GAAG,SAAS,EAAE,aAAa,iBAAiB,EAAE,eAAe,QAAQ,GAAG;YACnL,EAAE,aAAa,iBAAiB,EAAE,YAAY,QAAQ,GAAG,SAAS,EAAE,aAAa,iBAAiB,EAAE,YAAY,QAAQ,GAAG;qBAClH,EAAE,aAAa,iBAAiB,EAAE,iBAAiB,QAAQ,GAAG;mBAChE,EAAE,aAAa,iBAAiB,EAAE,QAAQ,IAAI,EAAE,aAAa,iBAAiB,EAAE,aAAa;;;aAGnG,EAAE,aAAa,iBAAiB,EAAE,qBAAqB,aAAa,EAAE,aAAa,iBAAiB,EAAE,oBAAoB;iBACtH,EAAE,aAAa,iBAAiB,EAAE,aAAa;oBAC5C,EAAE,aAAa,iBAAiB,EAAE,eAAe;;;WAG1D,EAAE,aAAa,eAAe,EAAE,QAAQ,IAAI,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC,EAAE,QAAQ,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,cAAc,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM;cAChJ,EAAE,aAAa,eAAe,EAAE,WAAW,IAAI,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC,EAAE,QAAQ,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,cAAc,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM;SAC3J,EAAE,aAAa,eAAe,EAAE,KAAK;UACpC,EAAE,aAAa,eAAe,EAAE,MAAM,UAAU,EAAE,aAAa,eAAe,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0FAqCN,CAAC;QAEvF,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,YAAY;QACd;QAEA,MAAM,aAAa,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;QACnD,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,MAAM;QAClB;QAEA,0BAA0B;QAC1B,IAAI;QACJ,IAAI;YACF,WAAW,KAAK,KAAK,CAAC;QACxB,EAAE,OAAO,YAAY;YACnB,iCAAiC;YACjC,WAAW;gBACT,kBAAkB,WAAW,SAAS,CAAC,GAAG,OAAO;gBACjD,WAAW;oBAAC;iBAA4C;gBACxD,gBAAgB;gBAChB,WAAW;oBAAC,CAAC,QAAQ,EAAE,MAAM,UAAU,EAAE;oBAAE,CAAC,OAAO,EAAE,MAAM,QAAQ,EAAE;oBAAE,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE;iBAAC;gBACtG,WAAW;gBACX,YAAY,KAAK,KAAK,CAAC,MAAM,UAAU;YACzC;QACF;QAEA,MAAM,eAA2B;YAC/B,QAAQ,WAAW,MAAM;YACzB,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,WAAW,SAAS,SAAS,IAAI,EAAE;YACnC,gBAAgB,SAAS,cAAc,IAAI;YAC3C,WAAW,SAAS,SAAS,IAAI,EAAE;YACnC,WAAW,SAAS,SAAS,IAAI;YACjC,YAAY,SAAS,UAAU,IAAI,KAAK,KAAK,CAAC,MAAM,UAAU;YAC9D,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,OAAO,gLAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gLAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 SwingTrader AI - Package for Sharing');
console.log('=====================================');

// Create a clean .env.local.example with your actual keys
const envContent = `# SwingTrader AI - API Configuration
# Replace with your actual API keys

# Financial Data APIs
POLYGON_API_KEY=********************************
FMP_API_KEY=K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7

# Alpaca Trading API
ALPACA_API_KEY=PKKKYLNNZZT2EI7F3CVL
ALPACA_SECRET_KEY=Bgh3CLNSueS9Odyeb6U38UddNEluGDSIflunjinD
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# Application Settings
NEXT_PUBLIC_APP_URL=http://localhost:3000
`;

fs.writeFileSync('.env.local.example', envContent);
console.log('✅ Updated .env.local.example with your API keys');

// Create a quick start script
const quickStartContent = `#!/usr/bin/env node

console.log('🚀 SwingTrader AI - Quick Start');
console.log('===============================');
console.log('');

const os = require('os');
const { execSync } = require('child_process');

const platform = os.platform();

if (platform === 'win32') {
    console.log('Windows detected - running Windows installer...');
    execSync('install-windows.bat', { stdio: 'inherit' });
} else {
    console.log('Mac/Linux detected - running Unix installer...');
    execSync('chmod +x install-mac-linux.sh && ./install-mac-linux.sh', { stdio: 'inherit' });
}
`;

fs.writeFileSync('quick-start.js', quickStartContent);
console.log('✅ Created quick-start.js for automatic OS detection');

// Create a simple README for sharing
const readmeContent = `# 🚀 SwingTrader AI

Professional swing trading platform with AI-powered analysis and automated scanning.

## 🎯 Quick Start (1-Click Setup)

### Option 1: Automatic Setup
\`\`\`bash
node quick-start.js
\`\`\`

### Option 2: Manual Setup

**Windows:**
\`\`\`
Double-click: install-windows.bat
\`\`\`

**Mac/Linux:**
\`\`\`bash
chmod +x install-mac-linux.sh
./install-mac-linux.sh
\`\`\`

## 🌐 Cloud Deployment (Recommended)

For instant sharing with anyone:
\`\`\`bash
node deploy.js
\`\`\`

## 📖 Full Documentation

See \`DEPLOYMENT-GUIDE.md\` for complete instructions.

## 🔑 Features

- **Professional Swing Trading Strategies**
- **Automated Stock Scanning** (70+ stocks)
- **Real-time Market Data** (Polygon + FMP APIs)
- **Risk Management & Position Sizing**
- **Technical Analysis Tools**

## 🎉 Ready to Trade!

After installation, open your browser to \`http://localhost:3000\`
`;

fs.writeFileSync('README.md', readmeContent);
console.log('✅ Created README.md for easy sharing');

// Make scripts executable on Unix systems
try {
    execSync('chmod +x install-mac-linux.sh', { stdio: 'ignore' });
    execSync('chmod +x quick-start.js', { stdio: 'ignore' });
    console.log('✅ Made scripts executable');
} catch (error) {
    // Ignore on Windows
}

console.log('');
console.log('🎉 Package Ready for Sharing!');
console.log('');
console.log('📁 Your sharing options:');
console.log('');
console.log('1. 🌐 CLOUD DEPLOYMENT (Easiest):');
console.log('   Run: node deploy.js');
console.log('   Share the URL with anyone!');
console.log('');
console.log('2. 📦 LOCAL PACKAGE:');
console.log('   Zip this entire folder');
console.log('   Recipients run: node quick-start.js');
console.log('');
console.log('3. 📋 MANUAL SETUP:');
console.log('   Share folder + DEPLOYMENT-GUIDE.md');
console.log('   Recipients follow the guide');
console.log('');
console.log('✨ All methods include your API keys pre-configured!');

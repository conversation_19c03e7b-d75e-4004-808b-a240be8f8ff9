{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/polygon.ts"], "sourcesContent": ["import axios from 'axios'\nimport { CandlestickData, StockData } from '@/types/trading'\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io'\nconst API_KEY = process.env.POLYGON_API_KEY\n\nexport class PolygonAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('Polygon API key is required')\n    }\n  }\n\n  // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      // Use snapshot endpoint for real-time data (available on paid plans)\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data.results[0]\n      if (!data) {\n        throw new Error(`No data found for ${symbol}`)\n      }\n\n      const ticker = data.value || data\n      const dayData = ticker.day || {}\n      const prevDayData = ticker.prevDay || {}\n      const lastQuote = ticker.lastQuote || {}\n      const lastTrade = ticker.lastTrade || {}\n\n      // Use the most recent price available\n      const currentPrice = lastTrade.p || dayData.c || prevDayData.c\n      const prevClose = prevDayData.c || dayData.o\n      const change = currentPrice - prevClose\n      const changePercent = (change / prevClose) * 100\n\n      return {\n        symbol: symbol.toUpperCase(),\n        name: ticker.name || symbol.toUpperCase(),\n        price: currentPrice,\n        change,\n        changePercent,\n        volume: dayData.v || 0,\n        marketCap: ticker.market_cap,\n        pe: undefined,\n        dividend: undefined\n      }\n    } catch (error) {\n      console.error('Error fetching stock quote from Polygon:', error)\n\n      // Fallback to previous day data if snapshot fails\n      try {\n        const fallbackResponse = await axios.get(\n          `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`,\n          {\n            params: {\n              adjusted: 'true',\n              apikey: this.apiKey\n            }\n          }\n        )\n\n        const data = fallbackResponse.data.results[0]\n        return {\n          symbol: symbol.toUpperCase(),\n          name: symbol.toUpperCase(),\n          price: data.c,\n          change: data.c - data.o,\n          changePercent: ((data.c - data.o) / data.o) * 100,\n          volume: data.v,\n          marketCap: undefined,\n          pe: undefined,\n          dividend: undefined\n        }\n      } catch (fallbackError) {\n        console.error('Polygon fallback also failed:', fallbackError)\n        throw new Error(`Failed to fetch quote for ${symbol}`)\n      }\n    }\n  }\n\n  // Get historical candlestick data (optimized for paid plans)\n  async getHistoricalData(\n    symbol: string,\n    timespan: 'minute' | 'hour' | 'day' | 'week' | 'month' = 'day',\n    multiplier: number = 1,\n    from: string,\n    to: string\n  ): Promise<CandlestickData[]> {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`,\n        {\n          params: {\n            adjusted: 'true',\n            sort: 'asc',\n            limit: 50000, // Higher limit for paid plans\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      if (!response.data.results || response.data.results.length === 0) {\n        console.warn(`No historical data found for ${symbol}`)\n        return []\n      }\n\n      return response.data.results.map((candle: any) => ({\n        timestamp: candle.t,\n        open: candle.o,\n        high: candle.h,\n        low: candle.l,\n        close: candle.c,\n        volume: candle.v\n      }))\n    } catch (error) {\n      console.error(`Error fetching historical data for ${symbol}:`, error)\n\n      // Log the specific error for debugging\n      if (error.response) {\n        console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`)\n        console.error('Response data:', error.response.data)\n      }\n\n      throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`)\n    }\n  }\n\n  // Get company details\n  async getCompanyDetails(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results\n    } catch (error) {\n      console.error('Error fetching company details:', error)\n      return null\n    }\n  }\n\n  // Get market status\n  async getMarketStatus() {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v1/marketstatus/now`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching market status:', error)\n      return null\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers`,\n        {\n          params: {\n            search: query,\n            market: 'stocks',\n            active: 'true',\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results || []\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n}\n\n// Create a singleton instance\nexport const polygonAPI = new PolygonAPI()\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGA,MAAM,mBAAmB;AACzB,MAAM,UAAU,QAAQ,GAAG,CAAC,eAAe;AAEpC,MAAM;IACH,OAAc;IAEtB,YAAY,MAAe,CAAE;QAC3B,IAAI,CAAC,MAAM,GAAG,UAAU,WAAW;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,oFAAoF;IACpF,MAAM,cAAc,MAAc,EAAsB;QACtD,IAAI;YACF,qEAAqE;YACrE,MAAM,WAAW,MAAM,kLAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,8CAA8C,EAAE,QAAQ,EAC5E;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,MAAM,OAAO,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE;YACrC,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,QAAQ;YAC/C;YAEA,MAAM,SAAS,KAAK,KAAK,IAAI;YAC7B,MAAM,UAAU,OAAO,GAAG,IAAI,CAAC;YAC/B,MAAM,cAAc,OAAO,OAAO,IAAI,CAAC;YACvC,MAAM,YAAY,OAAO,SAAS,IAAI,CAAC;YACvC,MAAM,YAAY,OAAO,SAAS,IAAI,CAAC;YAEvC,sCAAsC;YACtC,MAAM,eAAe,UAAU,CAAC,IAAI,QAAQ,CAAC,IAAI,YAAY,CAAC;YAC9D,MAAM,YAAY,YAAY,CAAC,IAAI,QAAQ,CAAC;YAC5C,MAAM,SAAS,eAAe;YAC9B,MAAM,gBAAgB,AAAC,SAAS,YAAa;YAE7C,OAAO;gBACL,QAAQ,OAAO,WAAW;gBAC1B,MAAM,OAAO,IAAI,IAAI,OAAO,WAAW;gBACvC,OAAO;gBACP;gBACA;gBACA,QAAQ,QAAQ,CAAC,IAAI;gBACrB,WAAW,OAAO,UAAU;gBAC5B,IAAI;gBACJ,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAE1D,kDAAkD;YAClD,IAAI;gBACF,MAAM,mBAAmB,MAAM,kLAAK,CAAC,GAAG,CACtC,GAAG,iBAAiB,gBAAgB,EAAE,OAAO,KAAK,CAAC,EACnD;oBACE,QAAQ;wBACN,UAAU;wBACV,QAAQ,IAAI,CAAC,MAAM;oBACrB;gBACF;gBAGF,MAAM,OAAO,iBAAiB,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC7C,OAAO;oBACL,QAAQ,OAAO,WAAW;oBAC1B,MAAM,OAAO,WAAW;oBACxB,OAAO,KAAK,CAAC;oBACb,QAAQ,KAAK,CAAC,GAAG,KAAK,CAAC;oBACvB,eAAe,AAAC,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,GAAI;oBAC9C,QAAQ,KAAK,CAAC;oBACd,WAAW;oBACX,IAAI;oBACJ,UAAU;gBACZ;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,QAAQ;YACvD;QACF;IACF;IAEA,6DAA6D;IAC7D,MAAM,kBACJ,MAAc,EACd,WAAyD,KAAK,EAC9D,aAAqB,CAAC,EACtB,IAAY,EACZ,EAAU,EACkB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,kLAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,gBAAgB,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,EAC5F;gBACE,QAAQ;oBACN,UAAU;oBACV,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;gBAChE,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,QAAQ;gBACrD,OAAO,EAAE;YACX;YAEA,OAAO,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;oBACjD,WAAW,OAAO,CAAC;oBACnB,MAAM,OAAO,CAAC;oBACd,MAAM,OAAO,CAAC;oBACd,KAAK,OAAO,CAAC;oBACb,OAAO,OAAO,CAAC;oBACf,QAAQ,OAAO,CAAC;gBAClB,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAE/D,uCAAuC;YACvC,IAAI,MAAM,QAAQ,EAAE;gBAClB,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;gBAC1F,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACrD;YAEA,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,OAAO,EAAE,EAAE,MAAM,OAAO,EAAE;QACnF;IACF;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,MAAc,EAAE;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,kLAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,sBAAsB,EAAE,QAAQ,EACpD;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,OAAO;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,kLAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,oBAAoB,CAAC,EACzC;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,aAAa,KAAa,EAAE,QAAgB,EAAE,EAAE;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,kLAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,qBAAqB,CAAC,EAC1C;gBACE,QAAQ;oBACN,QAAQ;oBACR,QAAQ;oBACR,QAAQ;oBACR;oBACA,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,EAAE;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;AACF;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/indicators.ts"], "sourcesContent": ["import { CandlestickData, TechnicalIndicator } from '@/types/trading'\n\nexport class TechnicalIndicators {\n  // Simple Moving Average\n  static sma(data: number[], period: number): number[] {\n    const result: number[] = []\n    for (let i = period - 1; i < data.length; i++) {\n      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)\n      result.push(sum / period)\n    }\n    return result\n  }\n\n  // Exponential Moving Average\n  static ema(data: number[], period: number): number[] {\n    const result: number[] = []\n    const multiplier = 2 / (period + 1)\n    \n    // Start with SMA for first value\n    let ema = data.slice(0, period).reduce((a, b) => a + b, 0) / period\n    result.push(ema)\n    \n    for (let i = period; i < data.length; i++) {\n      ema = (data[i] * multiplier) + (ema * (1 - multiplier))\n      result.push(ema)\n    }\n    \n    return result\n  }\n\n  // Relative Strength Index\n  static rsi(data: number[], period: number = 14): number[] {\n    const gains: number[] = []\n    const losses: number[] = []\n    \n    for (let i = 1; i < data.length; i++) {\n      const change = data[i] - data[i - 1]\n      gains.push(change > 0 ? change : 0)\n      losses.push(change < 0 ? Math.abs(change) : 0)\n    }\n    \n    const avgGains = this.sma(gains, period)\n    const avgLosses = this.sma(losses, period)\n    \n    return avgGains.map((gain, i) => {\n      const rs = gain / avgLosses[i]\n      return 100 - (100 / (1 + rs))\n    })\n  }\n\n  // MACD (Moving Average Convergence Divergence)\n  static macd(data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {\n    const fastEMA = this.ema(data, fastPeriod)\n    const slowEMA = this.ema(data, slowPeriod)\n    \n    // Align arrays (slowEMA starts later)\n    const startIndex = slowPeriod - fastPeriod\n    const macdLine = fastEMA.slice(startIndex).map((fast, i) => fast - slowEMA[i])\n    \n    const signalLine = this.ema(macdLine, signalPeriod)\n    const histogram = macdLine.slice(signalPeriod - 1).map((macd, i) => macd - signalLine[i])\n    \n    return {\n      macd: macdLine,\n      signal: signalLine,\n      histogram\n    }\n  }\n\n  // Bollinger Bands\n  static bollingerBands(data: number[], period: number = 20, stdDev: number = 2) {\n    const sma = this.sma(data, period)\n    const bands = sma.map((avg, i) => {\n      const slice = data.slice(i, i + period)\n      const variance = slice.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / period\n      const standardDeviation = Math.sqrt(variance)\n      \n      return {\n        upper: avg + (standardDeviation * stdDev),\n        middle: avg,\n        lower: avg - (standardDeviation * stdDev)\n      }\n    })\n    \n    return bands\n  }\n\n  // Support and Resistance Levels\n  static findSupportResistance(candles: CandlestickData[], lookback: number = 20): { support: number[], resistance: number[] } {\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    \n    const resistance: number[] = []\n    const support: number[] = []\n    \n    for (let i = lookback; i < candles.length - lookback; i++) {\n      const currentHigh = highs[i]\n      const currentLow = lows[i]\n      \n      // Check if current high is a local maximum\n      const isResistance = highs.slice(i - lookback, i).every(h => h <= currentHigh) &&\n                          highs.slice(i + 1, i + lookback + 1).every(h => h <= currentHigh)\n      \n      // Check if current low is a local minimum\n      const isSupport = lows.slice(i - lookback, i).every(l => l >= currentLow) &&\n                       lows.slice(i + 1, i + lookback + 1).every(l => l >= currentLow)\n      \n      if (isResistance) resistance.push(currentHigh)\n      if (isSupport) support.push(currentLow)\n    }\n    \n    return { support, resistance }\n  }\n\n  // Volume analysis\n  static volumeAnalysis(candles: CandlestickData[], period: number = 20) {\n    const volumes = candles.map(c => c.volume)\n    const avgVolume = this.sma(volumes, period)\n    const currentVolume = volumes[volumes.length - 1]\n    const currentAvgVolume = avgVolume[avgVolume.length - 1]\n\n    return {\n      currentVolume,\n      averageVolume: currentAvgVolume,\n      volumeRatio: currentVolume / currentAvgVolume,\n      isHighVolume: currentVolume > currentAvgVolume * 1.5,\n      isLowVolume: currentVolume < currentAvgVolume * 0.5\n    }\n  }\n\n  // Swing Trading Analysis\n  static analyzeSwingSetup(candles: CandlestickData[]): TechnicalIndicator[] {\n    const closes = candles.map(c => c.close)\n    const indicators: TechnicalIndicator[] = []\n\n    // RSI Analysis\n    const rsi = this.rsi(closes)\n    const currentRSI = rsi[rsi.length - 1]\n\n    let rsiSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`\n\n    if (currentRSI < 30) {\n      rsiSignal = 'BUY'\n      rsiDescription += ' - Oversold condition, potential bounce'\n    } else if (currentRSI > 70) {\n      rsiSignal = 'SELL'\n      rsiDescription += ' - Overbought condition, potential pullback'\n    } else {\n      rsiDescription += ' - Neutral zone'\n    }\n\n    indicators.push({\n      name: 'RSI',\n      value: currentRSI,\n      signal: rsiSignal,\n      description: rsiDescription\n    })\n\n    // Moving Average Analysis\n    const sma20 = this.sma(closes, 20)\n    const sma50 = this.sma(closes, 50)\n    const currentPrice = closes[closes.length - 1]\n    const currentSMA20 = sma20[sma20.length - 1]\n    const currentSMA50 = sma50[sma50.length - 1]\n\n    let maSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`\n\n    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n      maSignal = 'BUY'\n      maDescription += ' - Bullish trend'\n    } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n      maSignal = 'SELL'\n      maDescription += ' - Bearish trend'\n    } else {\n      maDescription += ' - Mixed signals'\n    }\n\n    indicators.push({\n      name: 'Moving Averages',\n      value: (currentPrice / currentSMA20 - 1) * 100,\n      signal: maSignal,\n      description: maDescription\n    })\n\n    // MACD Analysis\n    const macdData = this.macd(closes)\n    const currentMACD = macdData.macd[macdData.macd.length - 1]\n    const currentSignal = macdData.signal[macdData.signal.length - 1]\n    const currentHistogram = macdData.histogram[macdData.histogram.length - 1]\n\n    let macdSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`\n\n    if (currentMACD > currentSignal && currentHistogram > 0) {\n      macdSignal = 'BUY'\n      macdDescription += ' - Bullish momentum'\n    } else if (currentMACD < currentSignal && currentHistogram < 0) {\n      macdSignal = 'SELL'\n      macdDescription += ' - Bearish momentum'\n    } else {\n      macdDescription += ' - Momentum shifting'\n    }\n\n    indicators.push({\n      name: 'MACD',\n      value: currentHistogram,\n      signal: macdSignal,\n      description: macdDescription\n    })\n\n    // Volume Analysis\n    const volumeData = this.volumeAnalysis(candles)\n    let volumeSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`\n\n    if (volumeData.isHighVolume) {\n      volumeSignal = 'BUY'\n      volumeDescription += ' - High volume confirms move'\n    } else if (volumeData.isLowVolume) {\n      volumeSignal = 'SELL'\n      volumeDescription += ' - Low volume, weak conviction'\n    } else {\n      volumeDescription += ' - Normal volume'\n    }\n\n    indicators.push({\n      name: 'Volume',\n      value: volumeData.volumeRatio,\n      signal: volumeSignal,\n      description: volumeDescription\n    })\n\n    return indicators\n  }\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM;IACX,wBAAwB;IACxB,OAAO,IAAI,IAAc,EAAE,MAAc,EAAY;QACnD,MAAM,SAAmB,EAAE;QAC3B,IAAK,IAAI,IAAI,SAAS,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAC7C,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI,SAAS,GAAG,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;YACtE,OAAO,IAAI,CAAC,MAAM;QACpB;QACA,OAAO;IACT;IAEA,6BAA6B;IAC7B,OAAO,IAAI,IAAc,EAAE,MAAc,EAAY;QACnD,MAAM,SAAmB,EAAE;QAC3B,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC;QAElC,iCAAiC;QACjC,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,QAAQ,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK;QAC7D,OAAO,IAAI,CAAC;QAEZ,IAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,MAAM,EAAE,IAAK;YACzC,MAAM,AAAC,IAAI,CAAC,EAAE,GAAG,aAAe,MAAM,CAAC,IAAI,UAAU;YACrD,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;IACT;IAEA,0BAA0B;IAC1B,OAAO,IAAI,IAAc,EAAE,SAAiB,EAAE,EAAY;QACxD,MAAM,QAAkB,EAAE;QAC1B,MAAM,SAAmB,EAAE;QAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;YACpC,MAAM,IAAI,CAAC,SAAS,IAAI,SAAS;YACjC,OAAO,IAAI,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,UAAU;QAC9C;QAEA,MAAM,WAAW,IAAI,CAAC,GAAG,CAAC,OAAO;QACjC,MAAM,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ;QAEnC,OAAO,SAAS,GAAG,CAAC,CAAC,MAAM;YACzB,MAAM,KAAK,OAAO,SAAS,CAAC,EAAE;YAC9B,OAAO,MAAO,MAAM,CAAC,IAAI,EAAE;QAC7B;IACF;IAEA,+CAA+C;IAC/C,OAAO,KAAK,IAAc,EAAE,aAAqB,EAAE,EAAE,aAAqB,EAAE,EAAE,eAAuB,CAAC,EAAE;QACtG,MAAM,UAAU,IAAI,CAAC,GAAG,CAAC,MAAM;QAC/B,MAAM,UAAU,IAAI,CAAC,GAAG,CAAC,MAAM;QAE/B,sCAAsC;QACtC,MAAM,aAAa,aAAa;QAChC,MAAM,WAAW,QAAQ,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,MAAM,IAAM,OAAO,OAAO,CAAC,EAAE;QAE7E,MAAM,aAAa,IAAI,CAAC,GAAG,CAAC,UAAU;QACtC,MAAM,YAAY,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM,IAAM,OAAO,UAAU,CAAC,EAAE;QAExF,OAAO;YACL,MAAM;YACN,QAAQ;YACR;QACF;IACF;IAEA,kBAAkB;IAClB,OAAO,eAAe,IAAc,EAAE,SAAiB,EAAE,EAAE,SAAiB,CAAC,EAAE;QAC7E,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM;QAC3B,MAAM,QAAQ,IAAI,GAAG,CAAC,CAAC,KAAK;YAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,GAAG,IAAI;YAChC,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,IAAI,KAAK;YAC/E,MAAM,oBAAoB,KAAK,IAAI,CAAC;YAEpC,OAAO;gBACL,OAAO,MAAO,oBAAoB;gBAClC,QAAQ;gBACR,OAAO,MAAO,oBAAoB;YACpC;QACF;QAEA,OAAO;IACT;IAEA,gCAAgC;IAChC,OAAO,sBAAsB,OAA0B,EAAE,WAAmB,EAAE,EAA+C;QAC3H,MAAM,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACrC,MAAM,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QAEnC,MAAM,aAAuB,EAAE;QAC/B,MAAM,UAAoB,EAAE;QAE5B,IAAK,IAAI,IAAI,UAAU,IAAI,QAAQ,MAAM,GAAG,UAAU,IAAK;YACzD,MAAM,cAAc,KAAK,CAAC,EAAE;YAC5B,MAAM,aAAa,IAAI,CAAC,EAAE;YAE1B,2CAA2C;YAC3C,MAAM,eAAe,MAAM,KAAK,CAAC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK,gBAC9C,MAAM,KAAK,CAAC,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK;YAEzE,0CAA0C;YAC1C,MAAM,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK,eAC7C,KAAK,KAAK,CAAC,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK;YAErE,IAAI,cAAc,WAAW,IAAI,CAAC;YAClC,IAAI,WAAW,QAAQ,IAAI,CAAC;QAC9B;QAEA,OAAO;YAAE;YAAS;QAAW;IAC/B;IAEA,kBAAkB;IAClB,OAAO,eAAe,OAA0B,EAAE,SAAiB,EAAE,EAAE;QACrE,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QACzC,MAAM,YAAY,IAAI,CAAC,GAAG,CAAC,SAAS;QACpC,MAAM,gBAAgB,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QACjD,MAAM,mBAAmB,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;QAExD,OAAO;YACL;YACA,eAAe;YACf,aAAa,gBAAgB;YAC7B,cAAc,gBAAgB,mBAAmB;YACjD,aAAa,gBAAgB,mBAAmB;QAClD;IACF;IAEA,yBAAyB;IACzB,OAAO,kBAAkB,OAA0B,EAAwB;QACzE,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACvC,MAAM,aAAmC,EAAE;QAE3C,eAAe;QACf,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC;QACrB,MAAM,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAEtC,IAAI,YAAwC;QAC5C,IAAI,iBAAiB,CAAC,KAAK,EAAE,WAAW,OAAO,CAAC,IAAI;QAEpD,IAAI,aAAa,IAAI;YACnB,YAAY;YACZ,kBAAkB;QACpB,OAAO,IAAI,aAAa,IAAI;YAC1B,YAAY;YACZ,kBAAkB;QACpB,OAAO;YACL,kBAAkB;QACpB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;QACf;QAEA,0BAA0B;QAC1B,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ;QAC/B,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ;QAC/B,MAAM,eAAe,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QAC9C,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC5C,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAE5C,IAAI,WAAuC;QAC3C,IAAI,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,CAAC,eAAe,eAAe,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAE9F,IAAI,eAAe,gBAAgB,eAAe,cAAc;YAC9D,WAAW;YACX,iBAAiB;QACnB,OAAO,IAAI,eAAe,gBAAgB,eAAe,cAAc;YACrE,WAAW;YACX,iBAAiB;QACnB,OAAO;YACL,iBAAiB;QACnB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO,CAAC,eAAe,eAAe,CAAC,IAAI;YAC3C,QAAQ;YACR,aAAa;QACf;QAEA,gBAAgB;QAChB,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC;QAC3B,MAAM,cAAc,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,GAAG,EAAE;QAC3D,MAAM,gBAAgB,SAAS,MAAM,CAAC,SAAS,MAAM,CAAC,MAAM,GAAG,EAAE;QACjE,MAAM,mBAAmB,SAAS,SAAS,CAAC,SAAS,SAAS,CAAC,MAAM,GAAG,EAAE;QAE1E,IAAI,aAAyC;QAC7C,IAAI,kBAAkB,CAAC,MAAM,EAAE,YAAY,OAAO,CAAC,GAAG,UAAU,EAAE,cAAc,OAAO,CAAC,IAAI;QAE5F,IAAI,cAAc,iBAAiB,mBAAmB,GAAG;YACvD,aAAa;YACb,mBAAmB;QACrB,OAAO,IAAI,cAAc,iBAAiB,mBAAmB,GAAG;YAC9D,aAAa;YACb,mBAAmB;QACrB,OAAO;YACL,mBAAmB;QACrB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;QACf;QAEA,kBAAkB;QAClB,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC;QACvC,IAAI,eAA2C;QAC/C,IAAI,oBAAoB,CAAC,QAAQ,EAAE,CAAC,WAAW,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,YAAY,CAAC;QAE1F,IAAI,WAAW,YAAY,EAAE;YAC3B,eAAe;YACf,qBAAqB;QACvB,OAAO,IAAI,WAAW,WAAW,EAAE;YACjC,eAAe;YACf,qBAAqB;QACvB,OAAO;YACL,qBAAqB;QACvB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO,WAAW,WAAW;YAC7B,QAAQ;YACR,aAAa;QACf;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(value)\n}\n\nexport function formatPercentage(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'percent',\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(value / 100)\n}\n\nexport function calculateRiskReward(\n  entryPrice: number,\n  stopLoss: number,\n  takeProfit: number\n): number {\n  const risk = Math.abs(entryPrice - stopLoss)\n  const reward = Math.abs(takeProfit - entryPrice)\n  return reward / risk\n}\n\nexport function calculatePositionSize(\n  accountSize: number,\n  riskPercentage: number,\n  entryPrice: number,\n  stopLoss: number\n): number {\n  const riskAmount = accountSize * (riskPercentage / 100)\n  const riskPerShare = Math.abs(entryPrice - stopLoss)\n  return Math.floor(riskAmount / riskPerShare)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,wMAAO,EAAC,IAAA,+KAAI,EAAC;AACtB;AAEO,SAAS,eAAe,KAAa;IAC1C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,QAAQ;AACpB;AAEO,SAAS,oBACd,UAAkB,EAClB,QAAgB,EAChB,UAAkB;IAElB,MAAM,OAAO,KAAK,GAAG,CAAC,aAAa;IACnC,MAAM,SAAS,KAAK,GAAG,CAAC,aAAa;IACrC,OAAO,SAAS;AAClB;AAEO,SAAS,sBACd,WAAmB,EACnB,cAAsB,EACtB,UAAkB,EAClB,QAAgB;IAEhB,MAAM,aAAa,cAAc,CAAC,iBAAiB,GAAG;IACtD,MAAM,eAAe,KAAK,GAAG,CAAC,aAAa;IAC3C,OAAO,KAAK,KAAK,CAAC,aAAa;AACjC", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/lib/swingAnalysis.ts"], "sourcesContent": ["import { CandlestickData, SwingTradingAnalysis, TechnicalIndicator } from '@/types/trading'\nimport { TechnicalIndicators } from './indicators'\nimport { calculateRiskReward } from './utils'\n\nexport class SwingTradingAnalyzer {\n  static analyzeSwingTrade(\n    symbol: string,\n    candles: CandlestickData[],\n    timeframe: string = '1D'\n  ): SwingTradingAnalysis {\n    if (candles.length < 50) {\n      throw new Error('Insufficient data for swing trading analysis')\n    }\n\n    const closes = candles.map(c => c.close)\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    const currentPrice = closes[closes.length - 1]\n\n    // Get technical indicators\n    const indicators = TechnicalIndicators.analyzeSwingSetup(candles)\n    \n    // Find support and resistance levels\n    const { support, resistance } = TechnicalIndicators.findSupportResistance(candles)\n    \n    // Determine trend\n    const trend = this.determineTrend(candles)\n    \n    // Calculate entry, stop loss, and take profit levels\n    const levels = this.calculateTradingLevels(candles, trend)\n    \n    // Calculate confidence score\n    const confidence = this.calculateConfidence(indicators, trend, levels)\n    \n    // Generate recommendation\n    const recommendation = this.generateRecommendation(indicators, confidence, trend)\n    \n    // Generate analysis text\n    const analysis = this.generateAnalysisText(symbol, indicators, trend, levels, confidence)\n\n    return {\n      symbol,\n      timeframe,\n      trend: trend.direction,\n      confidence,\n      entryPrice: levels.entry,\n      stopLoss: levels.stopLoss,\n      takeProfit: levels.takeProfit,\n      riskRewardRatio: calculateRiskReward(levels.entry, levels.stopLoss, levels.takeProfit),\n      indicators,\n      supportLevels: support.slice(-3), // Last 3 support levels\n      resistanceLevels: resistance.slice(-3), // Last 3 resistance levels\n      analysis,\n      recommendation\n    }\n  }\n\n  private static determineTrend(candles: CandlestickData[]): { direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS', strength: number } {\n    const closes = candles.map(c => c.close)\n    const sma20 = TechnicalIndicators.sma(closes, 20)\n    const sma50 = TechnicalIndicators.sma(closes, 50)\n    \n    const currentPrice = closes[closes.length - 1]\n    const currentSMA20 = sma20[sma20.length - 1]\n    const currentSMA50 = sma50[sma50.length - 1]\n    \n    // Calculate trend strength based on price position relative to moving averages\n    let strength = 0\n    let direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS' = 'SIDEWAYS'\n    \n    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n      direction = 'BULLISH'\n      strength = Math.min(((currentPrice / currentSMA50) - 1) * 100, 100)\n    } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n      direction = 'BEARISH'\n      strength = Math.min(((currentSMA50 / currentPrice) - 1) * 100, 100)\n    } else {\n      // Sideways trend\n      const volatility = this.calculateVolatility(closes.slice(-20))\n      strength = Math.max(0, 50 - volatility * 10)\n    }\n    \n    return { direction, strength: Math.abs(strength) }\n  }\n\n  private static calculateTradingLevels(candles: CandlestickData[], trend: { direction: string, strength: number }) {\n    const closes = candles.map(c => c.close)\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    const currentPrice = closes[closes.length - 1]\n    \n    // Calculate ATR for stop loss placement\n    const atr = this.calculateATR(candles, 14)\n    const currentATR = atr[atr.length - 1]\n    \n    let entry = currentPrice\n    let stopLoss: number\n    let takeProfit: number\n    \n    if (trend.direction === 'BULLISH') {\n      // For bullish trend, look for pullback entry\n      const recentLow = Math.min(...lows.slice(-10))\n      entry = Math.max(recentLow * 1.005, currentPrice * 0.995) // Slight pullback entry\n      stopLoss = entry - (currentATR * 2)\n      takeProfit = entry + (currentATR * 3) // 1.5:1 risk-reward\n    } else if (trend.direction === 'BEARISH') {\n      // For bearish trend, look for bounce entry (short)\n      const recentHigh = Math.max(...highs.slice(-10))\n      entry = Math.min(recentHigh * 0.995, currentPrice * 1.005) // Slight bounce entry\n      stopLoss = entry + (currentATR * 2)\n      takeProfit = entry - (currentATR * 3) // 1.5:1 risk-reward\n    } else {\n      // Sideways trend - range trading\n      const recentHigh = Math.max(...highs.slice(-20))\n      const recentLow = Math.min(...lows.slice(-20))\n      const midpoint = (recentHigh + recentLow) / 2\n      \n      if (currentPrice < midpoint) {\n        // Near support, look for bounce\n        entry = currentPrice\n        stopLoss = recentLow * 0.995\n        takeProfit = recentHigh * 0.995\n      } else {\n        // Near resistance, look for rejection\n        entry = currentPrice\n        stopLoss = recentHigh * 1.005\n        takeProfit = recentLow * 1.005\n      }\n    }\n    \n    return { entry, stopLoss, takeProfit }\n  }\n\n  private static calculateATR(candles: CandlestickData[], period: number): number[] {\n    const trueRanges: number[] = []\n    \n    for (let i = 1; i < candles.length; i++) {\n      const high = candles[i].high\n      const low = candles[i].low\n      const prevClose = candles[i - 1].close\n      \n      const tr = Math.max(\n        high - low,\n        Math.abs(high - prevClose),\n        Math.abs(low - prevClose)\n      )\n      \n      trueRanges.push(tr)\n    }\n    \n    return TechnicalIndicators.sma(trueRanges, period)\n  }\n\n  private static calculateVolatility(prices: number[]): number {\n    const returns = []\n    for (let i = 1; i < prices.length; i++) {\n      returns.push((prices[i] - prices[i - 1]) / prices[i - 1])\n    }\n    \n    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length\n    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length\n    \n    return Math.sqrt(variance) * Math.sqrt(252) // Annualized volatility\n  }\n\n  private static calculateConfidence(\n    indicators: TechnicalIndicator[],\n    trend: { direction: string, strength: number },\n    levels: { entry: number, stopLoss: number, takeProfit: number }\n  ): number {\n    let confidence = 50 // Base confidence\n    \n    // Add confidence based on indicator alignment\n    const bullishSignals = indicators.filter(ind => ind.signal === 'BUY').length\n    const bearishSignals = indicators.filter(ind => ind.signal === 'SELL').length\n    const totalSignals = indicators.length\n    \n    if (bullishSignals > bearishSignals) {\n      confidence += (bullishSignals / totalSignals) * 30\n    } else if (bearishSignals > bullishSignals) {\n      confidence += (bearishSignals / totalSignals) * 30\n    }\n    \n    // Add confidence based on trend strength\n    confidence += (trend.strength / 100) * 20\n    \n    // Add confidence based on risk-reward ratio\n    const riskReward = calculateRiskReward(levels.entry, levels.stopLoss, levels.takeProfit)\n    if (riskReward >= 2) {\n      confidence += 10\n    } else if (riskReward >= 1.5) {\n      confidence += 5\n    }\n    \n    return Math.min(Math.max(confidence, 0), 100)\n  }\n\n  private static generateRecommendation(\n    indicators: TechnicalIndicator[],\n    confidence: number,\n    trend: { direction: string, strength: number }\n  ): 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL' | 'NO_TRADE' {\n    if (confidence < 40) {\n      return 'NO_TRADE'\n    }\n    \n    const bullishSignals = indicators.filter(ind => ind.signal === 'BUY').length\n    const bearishSignals = indicators.filter(ind => ind.signal === 'SELL').length\n    \n    if (bullishSignals > bearishSignals && confidence >= 70) {\n      return 'STRONG_BUY'\n    } else if (bullishSignals > bearishSignals && confidence >= 50) {\n      return 'BUY'\n    } else if (bearishSignals > bullishSignals && confidence >= 70) {\n      return 'STRONG_SELL'\n    } else if (bearishSignals > bullishSignals && confidence >= 50) {\n      return 'SELL'\n    } else {\n      return 'HOLD'\n    }\n  }\n\n  private static generateAnalysisText(\n    symbol: string,\n    indicators: TechnicalIndicator[],\n    trend: { direction: string, strength: number },\n    levels: { entry: number, stopLoss: number, takeProfit: number },\n    confidence: number\n  ): string {\n    const riskReward = calculateRiskReward(levels.entry, levels.stopLoss, levels.takeProfit)\n    \n    let analysis = `${symbol} Swing Trading Analysis:\\n\\n`\n    \n    analysis += `Trend: ${trend.direction} (Strength: ${trend.strength.toFixed(1)}%)\\n`\n    analysis += `Confidence: ${confidence.toFixed(1)}%\\n\\n`\n    \n    analysis += `Entry: $${levels.entry.toFixed(2)}\\n`\n    analysis += `Stop Loss: $${levels.stopLoss.toFixed(2)}\\n`\n    analysis += `Take Profit: $${levels.takeProfit.toFixed(2)}\\n`\n    analysis += `Risk/Reward: ${riskReward.toFixed(2)}:1\\n\\n`\n    \n    analysis += `Technical Indicators:\\n`\n    indicators.forEach(indicator => {\n      analysis += `• ${indicator.description}\\n`\n    })\n    \n    return analysis\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAEO,MAAM;IACX,OAAO,kBACL,MAAc,EACd,OAA0B,EAC1B,YAAoB,IAAI,EACF;QACtB,IAAI,QAAQ,MAAM,GAAG,IAAI;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACvC,MAAM,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACrC,MAAM,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QACnC,MAAM,eAAe,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QAE9C,2BAA2B;QAC3B,MAAM,aAAa,iLAAmB,CAAC,iBAAiB,CAAC;QAEzD,qCAAqC;QACrC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,iLAAmB,CAAC,qBAAqB,CAAC;QAE1E,kBAAkB;QAClB,MAAM,QAAQ,IAAI,CAAC,cAAc,CAAC;QAElC,qDAAqD;QACrD,MAAM,SAAS,IAAI,CAAC,sBAAsB,CAAC,SAAS;QAEpD,6BAA6B;QAC7B,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC,YAAY,OAAO;QAE/D,0BAA0B;QAC1B,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,YAAY,YAAY;QAE3E,yBAAyB;QACzB,MAAM,WAAW,IAAI,CAAC,oBAAoB,CAAC,QAAQ,YAAY,OAAO,QAAQ;QAE9E,OAAO;YACL;YACA;YACA,OAAO,MAAM,SAAS;YACtB;YACA,YAAY,OAAO,KAAK;YACxB,UAAU,OAAO,QAAQ;YACzB,YAAY,OAAO,UAAU;YAC7B,iBAAiB,IAAA,4KAAmB,EAAC,OAAO,KAAK,EAAE,OAAO,QAAQ,EAAE,OAAO,UAAU;YACrF;YACA,eAAe,QAAQ,KAAK,CAAC,CAAC;YAC9B,kBAAkB,WAAW,KAAK,CAAC,CAAC;YACpC;YACA;QACF;IACF;IAEA,OAAe,eAAe,OAA0B,EAAuE;QAC7H,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACvC,MAAM,QAAQ,iLAAmB,CAAC,GAAG,CAAC,QAAQ;QAC9C,MAAM,QAAQ,iLAAmB,CAAC,GAAG,CAAC,QAAQ;QAE9C,MAAM,eAAe,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QAC9C,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC5C,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAE5C,+EAA+E;QAC/E,IAAI,WAAW;QACf,IAAI,YAAgD;QAEpD,IAAI,eAAe,gBAAgB,eAAe,cAAc;YAC9D,YAAY;YACZ,WAAW,KAAK,GAAG,CAAC,CAAC,AAAC,eAAe,eAAgB,CAAC,IAAI,KAAK;QACjE,OAAO,IAAI,eAAe,gBAAgB,eAAe,cAAc;YACrE,YAAY;YACZ,WAAW,KAAK,GAAG,CAAC,CAAC,AAAC,eAAe,eAAgB,CAAC,IAAI,KAAK;QACjE,OAAO;YACL,iBAAiB;YACjB,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC,OAAO,KAAK,CAAC,CAAC;YAC1D,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,aAAa;QAC3C;QAEA,OAAO;YAAE;YAAW,UAAU,KAAK,GAAG,CAAC;QAAU;IACnD;IAEA,OAAe,uBAAuB,OAA0B,EAAE,KAA8C,EAAE;QAChH,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACvC,MAAM,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACrC,MAAM,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QACnC,MAAM,eAAe,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QAE9C,wCAAwC;QACxC,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS;QACvC,MAAM,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAEtC,IAAI,QAAQ;QACZ,IAAI;QACJ,IAAI;QAEJ,IAAI,MAAM,SAAS,KAAK,WAAW;YACjC,6CAA6C;YAC7C,MAAM,YAAY,KAAK,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC;YAC1C,QAAQ,KAAK,GAAG,CAAC,YAAY,OAAO,eAAe,QAAO,wBAAwB;YAClF,WAAW,QAAS,aAAa;YACjC,aAAa,QAAS,aAAa,GAAG,oBAAoB;QAC5D,OAAO,IAAI,MAAM,SAAS,KAAK,WAAW;YACxC,mDAAmD;YACnD,MAAM,aAAa,KAAK,GAAG,IAAI,MAAM,KAAK,CAAC,CAAC;YAC5C,QAAQ,KAAK,GAAG,CAAC,aAAa,OAAO,eAAe,QAAO,sBAAsB;YACjF,WAAW,QAAS,aAAa;YACjC,aAAa,QAAS,aAAa,GAAG,oBAAoB;QAC5D,OAAO;YACL,iCAAiC;YACjC,MAAM,aAAa,KAAK,GAAG,IAAI,MAAM,KAAK,CAAC,CAAC;YAC5C,MAAM,YAAY,KAAK,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC;YAC1C,MAAM,WAAW,CAAC,aAAa,SAAS,IAAI;YAE5C,IAAI,eAAe,UAAU;gBAC3B,gCAAgC;gBAChC,QAAQ;gBACR,WAAW,YAAY;gBACvB,aAAa,aAAa;YAC5B,OAAO;gBACL,sCAAsC;gBACtC,QAAQ;gBACR,WAAW,aAAa;gBACxB,aAAa,YAAY;YAC3B;QACF;QAEA,OAAO;YAAE;YAAO;YAAU;QAAW;IACvC;IAEA,OAAe,aAAa,OAA0B,EAAE,MAAc,EAAY;QAChF,MAAM,aAAuB,EAAE;QAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,IAAI;YAC5B,MAAM,MAAM,OAAO,CAAC,EAAE,CAAC,GAAG;YAC1B,MAAM,YAAY,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK;YAEtC,MAAM,KAAK,KAAK,GAAG,CACjB,OAAO,KACP,KAAK,GAAG,CAAC,OAAO,YAChB,KAAK,GAAG,CAAC,MAAM;YAGjB,WAAW,IAAI,CAAC;QAClB;QAEA,OAAO,iLAAmB,CAAC,GAAG,CAAC,YAAY;IAC7C;IAEA,OAAe,oBAAoB,MAAgB,EAAU;QAC3D,MAAM,UAAU,EAAE;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;QAC1D;QAEA,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM;QACxE,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,GAAG,CAAC,MAAM,MAAM,IAAI,KAAK,QAAQ,MAAM;QAEhG,OAAO,KAAK,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,KAAK,wBAAwB;;IACtE;IAEA,OAAe,oBACb,UAAgC,EAChC,KAA8C,EAC9C,MAA+D,EACvD;QACR,IAAI,aAAa,GAAG,kBAAkB;;QAEtC,8CAA8C;QAC9C,MAAM,iBAAiB,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,OAAO,MAAM;QAC5E,MAAM,iBAAiB,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,QAAQ,MAAM;QAC7E,MAAM,eAAe,WAAW,MAAM;QAEtC,IAAI,iBAAiB,gBAAgB;YACnC,cAAc,AAAC,iBAAiB,eAAgB;QAClD,OAAO,IAAI,iBAAiB,gBAAgB;YAC1C,cAAc,AAAC,iBAAiB,eAAgB;QAClD;QAEA,yCAAyC;QACzC,cAAc,AAAC,MAAM,QAAQ,GAAG,MAAO;QAEvC,4CAA4C;QAC5C,MAAM,aAAa,IAAA,4KAAmB,EAAC,OAAO,KAAK,EAAE,OAAO,QAAQ,EAAE,OAAO,UAAU;QACvF,IAAI,cAAc,GAAG;YACnB,cAAc;QAChB,OAAO,IAAI,cAAc,KAAK;YAC5B,cAAc;QAChB;QAEA,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,YAAY,IAAI;IAC3C;IAEA,OAAe,uBACb,UAAgC,EAChC,UAAkB,EAClB,KAA8C,EACuB;QACrE,IAAI,aAAa,IAAI;YACnB,OAAO;QACT;QAEA,MAAM,iBAAiB,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,OAAO,MAAM;QAC5E,MAAM,iBAAiB,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,QAAQ,MAAM;QAE7E,IAAI,iBAAiB,kBAAkB,cAAc,IAAI;YACvD,OAAO;QACT,OAAO,IAAI,iBAAiB,kBAAkB,cAAc,IAAI;YAC9D,OAAO;QACT,OAAO,IAAI,iBAAiB,kBAAkB,cAAc,IAAI;YAC9D,OAAO;QACT,OAAO,IAAI,iBAAiB,kBAAkB,cAAc,IAAI;YAC9D,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAe,qBACb,MAAc,EACd,UAAgC,EAChC,KAA8C,EAC9C,MAA+D,EAC/D,UAAkB,EACV;QACR,MAAM,aAAa,IAAA,4KAAmB,EAAC,OAAO,KAAK,EAAE,OAAO,QAAQ,EAAE,OAAO,UAAU;QAEvF,IAAI,WAAW,GAAG,OAAO,4BAA4B,CAAC;QAEtD,YAAY,CAAC,OAAO,EAAE,MAAM,SAAS,CAAC,YAAY,EAAE,MAAM,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;QACnF,YAAY,CAAC,YAAY,EAAE,WAAW,OAAO,CAAC,GAAG,KAAK,CAAC;QAEvD,YAAY,CAAC,QAAQ,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QAClD,YAAY,CAAC,YAAY,EAAE,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QACzD,YAAY,CAAC,cAAc,EAAE,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QAC7D,YAAY,CAAC,aAAa,EAAE,WAAW,OAAO,CAAC,GAAG,MAAM,CAAC;QAEzD,YAAY,CAAC,uBAAuB,CAAC;QACrC,WAAW,OAAO,CAAC,CAAA;YACjB,YAAY,CAAC,EAAE,EAAE,UAAU,WAAW,CAAC,EAAE,CAAC;QAC5C;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/app/api/analysis/swing/%5Bsymbol%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { PolygonAPI } from '@/lib/polygon'\nimport { SwingTradingAnalyzer } from '@/lib/swingAnalysis'\nimport { format, subDays } from 'date-fns'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ symbol: string }> }\n) {\n  try {\n    const { symbol } = await params\n    const { searchParams } = new URL(request.url)\n    \n    const timeframe = searchParams.get('timeframe') || '1D'\n    const days = parseInt(searchParams.get('days') || '100')\n    \n    if (!symbol) {\n      return NextResponse.json(\n        { error: 'Symbol parameter is required' },\n        { status: 400 }\n      )\n    }\n\n    // Get historical data for analysis\n    const polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)\n    const to = format(new Date(), 'yyyy-MM-dd')\n    const from = format(subDays(new Date(), days), 'yyyy-MM-dd')\n    \n    const historicalData = await polygonAPI.getHistoricalData(\n      symbol.toUpperCase(),\n      'day',\n      1,\n      from,\n      to\n    )\n\n    if (historicalData.length < 50) {\n      return NextResponse.json(\n        { error: 'Insufficient historical data for analysis' },\n        { status: 400 }\n      )\n    }\n\n    // Perform swing trading analysis\n    const analysis = SwingTradingAnalyzer.analyzeSwingTrade(\n      symbol.toUpperCase(),\n      historicalData,\n      timeframe\n    )\n    \n    return NextResponse.json(analysis)\n  } catch (error) {\n    console.error('Error in swing analysis API:', error)\n    return NextResponse.json(\n      { error: 'Failed to perform swing trading analysis' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;;;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA2C;IAEnD,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;QACzB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QACnD,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAElD,IAAI,CAAC,QAAQ;YACX,OAAO,gLAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+B,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,mCAAmC;QACnC,MAAM,aAAa,IAAI,qKAAU,CAAC,QAAQ,GAAG,CAAC,eAAe;QAC7D,MAAM,KAAK,IAAA,iMAAM,EAAC,IAAI,QAAQ;QAC9B,MAAM,OAAO,IAAA,iMAAM,EAAC,IAAA,mLAAO,EAAC,IAAI,QAAQ,OAAO;QAE/C,MAAM,iBAAiB,MAAM,WAAW,iBAAiB,CACvD,OAAO,WAAW,IAClB,OACA,GACA,MACA;QAGF,IAAI,eAAe,MAAM,GAAG,IAAI;YAC9B,OAAO,gLAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4C,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,WAAW,qLAAoB,CAAC,iBAAiB,CACrD,OAAO,WAAW,IAClB,gBACA;QAGF,OAAO,gLAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gLAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2C,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
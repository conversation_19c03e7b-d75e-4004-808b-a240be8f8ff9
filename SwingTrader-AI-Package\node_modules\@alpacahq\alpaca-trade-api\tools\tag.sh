#!/usr/bin/env bash

VER_FILE="package.json"

# This script simply sets up the repo for a new tag
echo "Current version number is: "

grep '"version"' $VER_FILE


read -r -p "Please enter a new version number to bump to: " VERSION

echo -e "Updating $VER_FILE to $VERSION\n"

sed -i -e "s/\"version\": .*/\"version\": \"$VERSION\",/" $VER_FILE

read -r -n 1 -p "Would you like to git commit and tag now? (y/N): " YN

# there are better ways to get this in but 🤷
echo ""

case $YN in
  y|Y )
    echo "Okay, committing this change";
    git commit -am "Version bump to $VERSION" -m "This commit was auto generated by a tool!"
    git tag -a "v$VERSION" -m "Version bump to $VERSION" -m "This tag was auto generated by a tool!"
    git tag --sort -refname | head -n 5
    echo "Done!"
  ;;
  * )
    echo -e "Okay exiting now. $VER_FILE has still been edited to contain the new version\nFair warning!"
    exit 0
  ;;
esac

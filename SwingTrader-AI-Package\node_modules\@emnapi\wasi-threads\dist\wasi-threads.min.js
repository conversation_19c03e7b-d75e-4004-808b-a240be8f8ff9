!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).wasiThreads={})}(this,function(e){var r="undefined"!=typeof WebAssembly?WebAssembly:"undefined"!=typeof WXWebAssembly?WXWebAssembly:void 0,t="object"==typeof process&&null!==process&&"object"==typeof process.versions&&null!==process.versions&&"string"==typeof process.versions.node;function o(e){return"function"==typeof(null==e?void 0:e.postMessage)?e.postMessage:"function"==typeof postMessage?postMessage:void 0}function n(e){return"function"==typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(e)}function i(e){try{return e instanceof r.RuntimeError}catch(e){return!1}}function a(e,r){return{__emnapi__:{type:e,payload:r}}}function s(e){if(e){if(!n(e.buffer))throw new Error("Multithread features require shared wasm memory. Try to compile with `-matomics -mbulk-memory` and use `--import-memory --shared-memory` during linking, then create WebAssembly.Memory with `shared: true` option")}else if("undefined"==typeof SharedArrayBuffer)throw new Error("Current environment does not support SharedArrayBuffer, threads are not available!")}var d=0,h=function(){function e(e){var r;if(this.unusedWorkers=[],this.runningWorkers=[],this.pthreads=Object.create(null),this.wasmModule=null,this.wasmMemory=null,this.messageEvents=new WeakMap,!e)throw new TypeError("ThreadManager(): options is not provided");this._childThread="childThread"in e&&Boolean(e.childThread),this._childThread?(this._onCreateWorker=void 0,this._reuseWorker=!1,this._beforeLoad=void 0):(this._onCreateWorker=e.onCreateWorker,this._reuseWorker=function(e){var r;if("boolean"==typeof e)return!!e&&{size:0,strict:!1};if("number"==typeof e){if(!(e>=0))throw new RangeError("reuseWorker: size must be a non-negative integer");return{size:e,strict:!1}}if(!e)return!1;var t=null!==(r=Number(e.size))&&void 0!==r?r:0,o=Boolean(e.strict);if(!(t>0)&&o)throw new RangeError("reuseWorker: size must be set to positive integer if strict is set to true");return{size:t,strict:o}}(e.reuseWorker),this._beforeLoad=e.beforeLoad),this.printErr=null!==(r=e.printErr)&&void 0!==r?r:console.error.bind(console)}return Object.defineProperty(e.prototype,"nextWorkerID",{get:function(){return d},enumerable:!1,configurable:!0}),e.prototype.init=function(){this._childThread||this.initMainThread()},e.prototype.initMainThread=function(){this.preparePool()},e.prototype.preparePool=function(){if(this._reuseWorker&&this._reuseWorker.size)for(var e=this._reuseWorker.size;e--;){var r=this.allocateUnusedWorker();t&&(r.once("message",function(){}),r.unref())}},e.prototype.shouldPreloadWorkers=function(){return!this._childThread&&this._reuseWorker&&this._reuseWorker.size>0},e.prototype.loadWasmModuleToAllWorkers=function(){for(var e=this,r=Array(this.unusedWorkers.length),o=function(e){var o=n.unusedWorkers[e];t&&o.ref(),r[e]=n.loadWasmModuleToWorker(o).then(function(e){return t&&o.unref(),e},function(e){throw t&&o.unref(),e})},n=this,i=0;i<this.unusedWorkers.length;++i)o(i);return Promise.all(r).catch(function(r){throw e.terminateAllThreads(),r})},e.prototype.preloadWorkers=function(){return this.shouldPreloadWorkers()?this.loadWasmModuleToAllWorkers():Promise.resolve([])},e.prototype.setup=function(e,r){this.wasmModule=e,this.wasmMemory=r},e.prototype.markId=function(e){if(e.__emnapi_tid)return e.__emnapi_tid;var r=d+43;return d=(d+1)%536870869,this.pthreads[r]=e,e.__emnapi_tid=r,r},e.prototype.returnWorkerToPool=function(e){var r=e.__emnapi_tid;void 0!==r&&delete this.pthreads[r],this.unusedWorkers.push(e),this.runningWorkers.splice(this.runningWorkers.indexOf(e),1),delete e.__emnapi_tid,t&&e.unref()},e.prototype.loadWasmModuleToWorker=function(e,r){var o=this;if(e.whenLoaded)return e.whenLoaded;var n=this.printErr,i=this._beforeLoad,d=this;return e.whenLoaded=new Promise(function(h,u){e.onmessage=function(r){!function(r){if(r.__emnapi__){var n=r.__emnapi__.type,i=r.__emnapi__.payload;"loaded"===n?(e.loaded=!0,t&&!e.__emnapi_tid&&e.unref(),h(e)):"cleanup-thread"===n&&i.tid in o.pthreads&&o.cleanThread(e,i.tid)}}(r.data),o.fireMessageEvent(e,r)},e.onerror=function(r){var t="worker sent an error!";if(void 0!==e.__emnapi_tid&&(t="worker (tid = "+e.__emnapi_tid+") sent an error!"),"message"in r){if(n(t+" "+r.message),-1!==r.message.indexOf("RuntimeError")||-1!==r.message.indexOf("unreachable"))try{d.terminateAllThreads()}catch(e){}}else n(t);throw u(r),r},t&&(e.on("message",function(r){var t,o;null===(o=(t=e).onmessage)||void 0===o||o.call(t,{data:r})}),e.on("error",function(r){var t,o;null===(o=(t=e).onerror)||void 0===o||o.call(t,r)}),e.on("detachedExit",function(){})),"function"==typeof i&&i(e);try{e.postMessage(a("load",{wasmModule:o.wasmModule,wasmMemory:o.wasmMemory,sab:r}))}catch(e){throw s(o.wasmMemory),e}}),e.whenLoaded},e.prototype.allocateUnusedWorker=function(){var e=this._onCreateWorker;if("function"!=typeof e)throw new TypeError("`options.onCreateWorker` is not provided");var r=e({type:"thread",name:"emnapi-pthread"});return this.unusedWorkers.push(r),r},e.prototype.getNewWorker=function(e){if(this._reuseWorker){if(0===this.unusedWorkers.length){if(this._reuseWorker.strict)if(!t)return void(0,this.printErr)("Tried to spawn a new thread, but the thread pool is exhausted.\nThis might result in a deadlock unless some threads eventually exit or the code explicitly breaks out to the event loop.");var r=this.allocateUnusedWorker();this.loadWasmModuleToWorker(r,e)}return this.unusedWorkers.pop()}var o=this.allocateUnusedWorker();return this.loadWasmModuleToWorker(o,e),this.unusedWorkers.pop()},e.prototype.cleanThread=function(e,r,t){if(!t&&this._reuseWorker)this.returnWorkerToPool(e);else{delete this.pthreads[r];var o=this.runningWorkers.indexOf(e);-1!==o&&this.runningWorkers.splice(o,1),this.terminateWorker(e),delete e.__emnapi_tid}},e.prototype.terminateWorker=function(e){var r,t=this,o=e.__emnapi_tid;e.terminate(),null===(r=this.messageEvents.get(e))||void 0===r||r.clear(),this.messageEvents.delete(e),e.onmessage=function(e){e.data.__emnapi__&&(0,t.printErr)('received "'+e.data.__emnapi__.type+'" command from terminated worker: '+o)}},e.prototype.terminateAllThreads=function(){for(var e=0;e<this.runningWorkers.length;++e)this.terminateWorker(this.runningWorkers[e]);for(e=0;e<this.unusedWorkers.length;++e)this.terminateWorker(this.unusedWorkers[e]);this.unusedWorkers=[],this.runningWorkers=[],this.pthreads=Object.create(null),this.preparePool()},e.prototype.addMessageEventListener=function(e,r){var t=this.messageEvents.get(e);return t||(t=new Set,this.messageEvents.set(e,t)),t.add(r),function(){null==t||t.delete(r)}},e.prototype.fireMessageEvent=function(e,r){var t=this.messageEvents.get(e);if(t){var o=this.printErr;t.forEach(function(e){try{e(r)}catch(e){o(e.stack)}})}},e}(),u=Symbol("kIsProxy");function c(e,r){if(e[u])return e;var t=e.exports,o=function(e){for(var r=["apply","construct","defineProperty","deleteProperty","get","getOwnPropertyDescriptor","getPrototypeOf","has","isExtensible","ownKeys","preventExtensions","set","setPrototypeOf"],t={},o=function(o){var n=r[o];t[n]=function(){var r=Array.prototype.slice.call(arguments,1);return r.unshift(e),Reflect[n].apply(Reflect,r)}},n=0;n<r.length;n++)o(n);return t}(t),n=function(){},i=function(){return 0};o.get=function(e,o,a){var s;return"memory"===o?null!==(s="function"==typeof r?r():r)&&void 0!==s?s:Reflect.get(t,o,a):"_initialize"===o?o in t?n:void 0:"_start"===o?o in t?i:void 0:Reflect.get(t,o,a)},o.has=function(e,r){return"memory"===r||Reflect.has(t,r)};var a=new Proxy(Object.create(null),o);return new Proxy(e,{get:function(e,r,t){return"exports"===r?a:r===u||Reflect.get(e,r,t)}})}var l=new WeakMap,f=function(){function e(e){var n=this;if(!e)throw new TypeError("WASIThreads(): options is not provided");if(!e.wasi)throw new TypeError("WASIThreads(): options.wasi is not provided");l.set(this,new WeakSet);var d=e.wasi;!function(e,r){var t=l.get(e);if(t.has(r))return;var o=e,n=r.wasiImport;if(n){var a=n.proc_exit;n.proc_exit=function(e){return o.terminateAllThreads(),a.call(this,e)}}if(!o.childThread){var s=r.start;"function"==typeof s&&(r.start=function(e){try{return s.call(this,e)}catch(e){throw i(e)&&o.terminateAllThreads(),e}})}t.add(r)}(this,d),this.wasi=d,this.childThread="childThread"in e&&Boolean(e.childThread),this.PThread=void 0,"threadManager"in e?"function"==typeof e.threadManager?this.PThread=e.threadManager():this.PThread=e.threadManager:this.childThread||(this.PThread=new h(e),this.PThread.init());var u=!1;"waitThreadStart"in e&&(u="number"==typeof e.waitThreadStart?e.waitThreadStart:Boolean(e.waitThreadStart));var c=o(e);if(this.childThread&&"function"!=typeof c)throw new TypeError("options.postMessage is not a function");this.postMessage=c;var f=Boolean(e.wasm64),p=function(e){if(e.data.__emnapi__){var r=e.data.__emnapi__.type,t=e.data.__emnapi__.payload;"spawn-thread"===r?m(t.startArg,t.errorOrTid):"terminate-all-threads"===r&&n.terminateAllThreads()}},m=function(e,o){var i,d=void 0!==o;try{s(n.wasmMemory)}catch(e){if(null===(i=n.PThread)||void 0===i||i.printErr(e.stack),d){var h=new Int32Array(n.wasmMemory.buffer,o,2);return Atomics.store(h,0,1),Atomics.store(h,1,6),Atomics.notify(h,1),1}return-6}if(!d){var l=n.wasmInstance.exports.malloc;if(!(o=f?Number(l(BigInt(8))):l(8)))return-48}var m=n.wasmInstance.exports.free,y=f?function(e){m(BigInt(e))}:m,v=new Int32Array(n.wasmMemory.buffer,o,2);if(Atomics.store(v,0,0),Atomics.store(v,1,0),n.childThread){c(a("spawn-thread",{startArg:e,errorOrTid:o})),Atomics.wait(v,1,0);var w=Atomics.load(v,0),_=Atomics.load(v,1);return d?w:(y(o),w?-_:_)}var g,k,T,W=u||0===u;W&&(g=new Int32Array(new SharedArrayBuffer(8208)),Atomics.store(g,0,0));var A=n.PThread;try{if(!(k=A.getNewWorker(g)))throw new Error("failed to get new worker");if(A.addMessageEventListener(k,p),T=A.markId(k),t&&k.ref(),k.postMessage(a("start",{tid:T,arg:e,sab:g})),W){if("number"==typeof u){if("timed-out"===Atomics.wait(g,0,0,u)){try{A.cleanThread(k,T,!0)}catch(e){}throw new Error("Spawning thread timed out. Please check if the worker is created successfully and if message is handled properly in the worker.")}}else Atomics.wait(g,0,0);if(Atomics.load(g,0)>1){try{A.cleanThread(k,T,!0)}catch(e){}throw function(e){var t,o,n=new Int32Array(e);if(Atomics.load(n,0)<=1)return null;var i=Atomics.load(n,1),a=Atomics.load(n,2),s=Atomics.load(n,3),d=new Uint8Array(e),h=d.slice(16,16+i),u=d.slice(16+i,16+i+a),c=d.slice(16+i+a,16+i+a+s),l=(new TextDecoder).decode(h),f=(new TextDecoder).decode(u),p=(new TextDecoder).decode(c),m=new(null!==(t=globalThis[l])&&void 0!==t?t:"RuntimeError"===l&&null!==(o=r.RuntimeError)&&void 0!==o?o:Error)(f);return Object.defineProperty(m,"stack",{value:p,writable:!0,enumerable:!1,configurable:!0}),m}(g.buffer)}}}catch(e){return Atomics.store(v,0,1),Atomics.store(v,1,6),Atomics.notify(v,1),null==A||A.printErr(e.stack),d?1:(y(o),-6)}return Atomics.store(v,0,0),Atomics.store(v,1,T),Atomics.notify(v,1),A.runningWorkers.push(k),W||k.whenLoaded.catch(function(e){throw delete k.whenLoaded,A.cleanThread(k,T,!0),e}),d?0:(y(o),T)};this.threadSpawn=m}return e.prototype.getImportObject=function(){return{wasi:{"thread-spawn":this.threadSpawn}}},e.prototype.setup=function(e,r,t){null!=t||(t=e.exports.memory),this.wasmInstance=e,this.wasmMemory=t,this.PThread&&this.PThread.setup(r,t)},e.prototype.preloadWorkers=function(){return this.PThread?this.PThread.preloadWorkers():Promise.resolve([])},e.prototype.initialize=function(e,r,t){var o=e.exports;null!=t||(t=o.memory),this.childThread&&(e=c(e,t)),this.setup(e,r,t);var n=this.wasi;if("_start"in o&&"function"==typeof o._start)if(this.childThread){n.start(e);try{n[p(n,"kStarted")]=!1}catch(e){}}else!function(e,r){var t=p(e,["kInstance","kSetMemory"]),o=t[0],n=t[1];e[o]=r,e[n](r.exports.memory)}(n,e);else n.initialize(e);return e},e.prototype.start=function(e,r,t){var o=e.exports;return null!=t||(t=o.memory),this.childThread&&(e=c(e,t)),this.setup(e,r,t),{exitCode:this.wasi.start(e),instance:e}},e.prototype.terminateAllThreads=function(){var e;this.childThread?this.postMessage(a("terminate-all-threads",{})):null===(e=this.PThread)||void 0===e||e.terminateAllThreads()},e}();function p(e,r){var t=Object.getOwnPropertySymbols(e),o=function(e){return function(r){return r.description?r.description===e:r.toString()==="Symbol(".concat(e,")")}};return Array.isArray(r)?r.map(function(e){return t.filter(o(e))[0]}):t.filter(o(r))[0]}var m=function(){function e(e){var r=o(e);if("function"!=typeof r)throw new TypeError("options.postMessage is not a function");this.postMessage=r,this.onLoad=null==e?void 0:e.onLoad,this.onError="function"==typeof(null==e?void 0:e.onError)?e.onError:function(e,r){throw r},this.instance=void 0,this.messagesBeforeLoad=[]}return e.prototype.instantiate=function(e){if("function"==typeof this.onLoad)return this.onLoad(e);throw new Error("ThreadMessageHandler.prototype.instantiate is not implemented")},e.prototype.handle=function(e){var r,t=this;if(null===(r=null==e?void 0:e.data)||void 0===r?void 0:r.__emnapi__){var o=e.data.__emnapi__.type,n=e.data.__emnapi__.payload;try{"load"===o?this._load(n):"start"===o&&this.handleAfterLoad(e,function(){t._start(n)})}catch(e){this.onError(e,o)}}},e.prototype._load=function(e){var r=this;if(void 0===this.instance){var t;try{t=this.instantiate(e)}catch(r){return void this._loaded(r,null,e)}var o=t&&"then"in t?t.then:void 0;"function"==typeof o?o.call(t,function(t){r._loaded(null,t,e)},function(t){r._loaded(t,null,e)}):this._loaded(null,t,e)}},e.prototype._start=function(e){var r=this.instance.exports.wasi_thread_start;if("function"!=typeof r){var t=new TypeError("wasi_thread_start is not exported");throw y(e.sab,2,t),t}var o=this.postMessage,n=e.tid,i=e.arg;y(e.sab,1);try{r(n,i)}catch(t){if("unwind"!==t)throw t;return}o(a("cleanup-thread",{tid:n}))},e.prototype._loaded=function(e,r,t){if(e)throw y(t.sab,2,e),e;if(null==r){var o=new TypeError("onLoad should return an object");throw y(t.sab,2,o),o}var n=r.instance;if(!n){var i=new TypeError('onLoad should return an object which includes "instance"');throw y(t.sab,2,i),i}this.instance=n,(0,this.postMessage)(a("loaded",{}));var s=this.messagesBeforeLoad;this.messagesBeforeLoad=[];for(var d=0;d<s.length;d++){var h=s[d];this.handle({data:h})}},e.prototype.handleAfterLoad=function(e,r){void 0!==this.instance?r.call(this,e):this.messagesBeforeLoad.push(e.data)},e}();function y(e,r,t){e&&(!function(e,r,t){var o=new Int32Array(e);if(Atomics.store(o,0,r),r>1&&t){var n=t.name,i=t.message,a=t.stack,s=(new TextEncoder).encode(n),d=(new TextEncoder).encode(i),h=(new TextEncoder).encode(a);Atomics.store(o,1,s.length),Atomics.store(o,2,d.length),Atomics.store(o,3,h.length);var u=new Uint8Array(e);u.set(s,16),u.set(d,16+s.length),u.set(h,16+s.length+d.length)}}(e.buffer,r,t),Atomics.notify(e,0))}e.ThreadManager=h,e.ThreadMessageHandler=m,e.WASIThreads=f,e.createInstanceProxy=c,e.isSharedArrayBuffer=n,e.isTrapError=i});

{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/app/api/scanner/test/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\n// Mock test data to verify the UI works when there are valid setups\nexport async function GET(request: NextRequest) {\n  try {\n    // Simulate a successful scan with mock data\n    const mockResults = [\n      {\n        symbol: 'AAPL',\n        name: 'Apple Inc.',\n        sector: 'Technology',\n        quote: {\n          symbol: 'AAPL',\n          name: 'Apple Inc.',\n          price: 175.50,\n          change: 2.25,\n          changePercent: 1.30,\n          volume: 45000000,\n          marketCap: 2800000000000,\n          pe: 28.5,\n          dividend: 0.24\n        },\n        overnightSetup: {\n          strategy: 'overnight_momentum',\n          score: 85,\n          confidence: 'HIGH',\n          entry: 175.50,\n          stopLoss: 172.00,\n          target1: 180.00,\n          target2: 185.00,\n          riskReward: 2.57,\n          positionSize: 285,\n          riskAmount: 997.50,\n          signals: [\n            'Strong overnight gap up',\n            'Above 8 EMA',\n            'High volume confirmation',\n            'Bullish momentum'\n          ],\n          timeframe: '1-3 days',\n          marketCondition: 'Bullish trend continuation'\n        },\n        bestStrategy: 'overnight_momentum',\n        overallScore: 85,\n        rank: 1,\n        scanTime: new Date().toISOString(),\n        alerts: ['High volume breakout'],\n        riskWarnings: []\n      },\n      {\n        symbol: 'NVDA',\n        name: 'NVIDIA Corporation',\n        sector: 'Technology',\n        quote: {\n          symbol: 'NVDA',\n          name: 'NVIDIA Corporation',\n          price: 485.20,\n          change: 8.75,\n          changePercent: 1.84,\n          volume: 35000000,\n          marketCap: 1200000000000,\n          pe: 65.2,\n          dividend: 0.16\n        },\n        breakoutSetup: {\n          strategy: 'technical_breakout',\n          score: 78,\n          confidence: 'HIGH',\n          entry: 485.20,\n          stopLoss: 475.00,\n          target1: 500.00,\n          target2: 515.00,\n          riskReward: 2.93,\n          positionSize: 98,\n          riskAmount: 999.60,\n          signals: [\n            'Breakout above resistance',\n            'Volume surge',\n            'RSI momentum',\n            'Moving average support'\n          ],\n          timeframe: '2-5 days',\n          marketCondition: 'Technical breakout pattern'\n        },\n        bestStrategy: 'technical_breakout',\n        overallScore: 78,\n        rank: 2,\n        scanTime: new Date().toISOString(),\n        alerts: ['Technical breakout confirmed'],\n        riskWarnings: []\n      },\n      {\n        symbol: 'TSLA',\n        name: 'Tesla, Inc.',\n        sector: 'Consumer Discretionary',\n        quote: {\n          symbol: 'TSLA',\n          name: 'Tesla, Inc.',\n          price: 245.80,\n          change: 5.20,\n          changePercent: 2.16,\n          volume: 28000000,\n          marketCap: 780000000000,\n          pe: 45.8,\n          dividend: 0\n        },\n        overnightSetup: {\n          strategy: 'overnight_momentum',\n          score: 72,\n          confidence: 'MEDIUM',\n          entry: 245.80,\n          stopLoss: 240.00,\n          target1: 255.00,\n          target2: 265.00,\n          riskReward: 3.31,\n          positionSize: 172,\n          riskAmount: 996.80,\n          signals: [\n            'Gap up with volume',\n            'Above key moving averages',\n            'Momentum building',\n            'Sector strength'\n          ],\n          timeframe: '1-4 days',\n          marketCondition: 'Momentum continuation'\n        },\n        bestStrategy: 'overnight_momentum',\n        overallScore: 72,\n        rank: 3,\n        scanTime: new Date().toISOString(),\n        alerts: ['Strong momentum signal'],\n        riskWarnings: ['High volatility stock']\n      }\n    ]\n\n    const summary = {\n      totalScanned: 16,\n      validSetups: 3,\n      avgScore: 78.3,\n      topSector: 'Technology',\n      scanDuration: '2.1s',\n      timestamp: new Date().toISOString(),\n      results: mockResults\n    }\n\n    return NextResponse.json(summary)\n  } catch (error) {\n    console.error('Error in test scanner:', error)\n    return NextResponse.json(\n      { error: 'Failed to run test scan' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,4CAA4C;QAC5C,MAAM,cAAc;YAClB;gBACE,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,QAAQ;oBACR,WAAW;oBACX,IAAI;oBACJ,UAAU;gBACZ;gBACA,gBAAgB;oBACd,UAAU;oBACV,OAAO;oBACP,YAAY;oBACZ,OAAO;oBACP,UAAU;oBACV,SAAS;oBACT,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,SAAS;wBACP;wBACA;wBACA;wBACA;qBACD;oBACD,WAAW;oBACX,iBAAiB;gBACnB;gBACA,cAAc;gBACd,cAAc;gBACd,MAAM;gBACN,UAAU,IAAI,OAAO,WAAW;gBAChC,QAAQ;oBAAC;iBAAuB;gBAChC,cAAc,EAAE;YAClB;YACA;gBACE,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,QAAQ;oBACR,WAAW;oBACX,IAAI;oBACJ,UAAU;gBACZ;gBACA,eAAe;oBACb,UAAU;oBACV,OAAO;oBACP,YAAY;oBACZ,OAAO;oBACP,UAAU;oBACV,SAAS;oBACT,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,SAAS;wBACP;wBACA;wBACA;wBACA;qBACD;oBACD,WAAW;oBACX,iBAAiB;gBACnB;gBACA,cAAc;gBACd,cAAc;gBACd,MAAM;gBACN,UAAU,IAAI,OAAO,WAAW;gBAChC,QAAQ;oBAAC;iBAA+B;gBACxC,cAAc,EAAE;YAClB;YACA;gBACE,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,QAAQ;oBACR,WAAW;oBACX,IAAI;oBACJ,UAAU;gBACZ;gBACA,gBAAgB;oBACd,UAAU;oBACV,OAAO;oBACP,YAAY;oBACZ,OAAO;oBACP,UAAU;oBACV,SAAS;oBACT,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,SAAS;wBACP;wBACA;wBACA;wBACA;qBACD;oBACD,WAAW;oBACX,iBAAiB;gBACnB;gBACA,cAAc;gBACd,cAAc;gBACd,MAAM;gBACN,UAAU,IAAI,OAAO,WAAW;gBAChC,QAAQ;oBAAC;iBAAyB;gBAClC,cAAc;oBAAC;iBAAwB;YACzC;SACD;QAED,MAAM,UAAU;YACd,cAAc;YACd,aAAa;YACb,UAAU;YACV,WAAW;YACX,cAAc;YACd,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS;QACX;QAEA,OAAO,gLAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gLAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
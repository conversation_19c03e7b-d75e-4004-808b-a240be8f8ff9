@echo off
echo ========================================
echo SwingTrader AI - Windows Installation
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Node.js is not installed!
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo Choose the LTS version and run this script again.
    pause
    exit /b 1
)

echo ✅ Node.js found
echo.

REM Install dependencies
echo 📦 Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed
echo.

REM Check for environment file
if not exist ".env.local" (
    echo 📝 Creating environment file...
    copy ".env.local.example" ".env.local"
    echo.
    echo ⚠️  IMPORTANT: Please edit .env.local with your API keys:
    echo    - POLYGON_API_KEY=your_polygon_api_key
    echo    - FMP_API_KEY=your_fmp_api_key  
    echo    - ALPACA_API_KEY=your_alpaca_api_key
    echo    - ALPACA_SECRET_KEY=your_alpaca_secret_key
    echo.
    echo Opening .env.local file for editing...
    notepad .env.local
    echo.
    echo Press any key after you've added your API keys...
    pause >nul
)

echo ✅ Environment configured
echo.

REM Build the application
echo 🔨 Building application...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo ✅ Build successful
echo.

REM Create startup script
echo 🚀 Creating startup script...
echo @echo off > start-swingtrader.bat
echo echo SwingTrader AI is starting... >> start-swingtrader.bat
echo echo Open your browser to: http://localhost:3000 >> start-swingtrader.bat
echo echo Press Ctrl+C to stop the server >> start-swingtrader.bat
echo echo. >> start-swingtrader.bat
echo call npm start >> start-swingtrader.bat

echo ✅ Startup script created
echo.

echo 🎉 Installation Complete!
echo.
echo To start SwingTrader AI:
echo   1. Double-click "start-swingtrader.bat"
echo   2. Open your browser to http://localhost:3000
echo.
echo To share with others:
echo   1. Copy this entire folder
echo   2. Run install-windows.bat on the new computer
echo   3. Add the API keys to .env.local
echo.

pause

!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).emnapiCore={})}(this,function(e){var r="undefined"!=typeof WebAssembly?WebAssembly:"undefined"!=typeof WXWebAssembly?WXWebAssembly:void 0;function t(e){if(e&&"object"!=typeof e)throw new TypeError("imports must be an object or undefined");return!0}function n(e,a){if(!e)throw new TypeError("Invalid wasm source");t(a),a=null!=a?a:{};try{var o="object"==typeof e&&null!==e&&"then"in e?e.then:void 0;if("function"==typeof o)return o.call(e,function(e){return n(e,a)})}catch(e){}if(e instanceof ArrayBuffer||ArrayBuffer.isView(e))return r.instantiate(e,a);if(e instanceof r.Module)return r.instantiate(e,a).then(function(r){return{instance:r,module:e}});if("undefined"!=typeof Response&&e instanceof Response)return e.arrayBuffer().then(function(e){return r.instantiate(e,a)});var i="string"==typeof e;if(i||"undefined"!=typeof URL&&e instanceof URL){if(i&&"undefined"!=typeof wx&&"undefined"!=typeof __wxConfig)return r.instantiate(e,a);if("function"!=typeof fetch)throw new TypeError("wasm source can not be a string or URL in this environment");if("function"!=typeof r.instantiateStreaming)return n(fetch(e),a);try{return r.instantiateStreaming(fetch(e),a).catch(function(){return n(fetch(e),a)})}catch(r){return n(fetch(e),a)}}throw new TypeError("Invalid wasm source")}var a="undefined"!=typeof WebAssembly?WebAssembly:"undefined"!=typeof WXWebAssembly?WXWebAssembly:void 0,o="object"==typeof process&&null!==process&&"object"==typeof process.versions&&null!==process.versions&&"string"==typeof process.versions.node;function i(e){return"function"==typeof(null==e?void 0:e.postMessage)?e.postMessage:"function"==typeof postMessage?postMessage:void 0}function s(e){return"function"==typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(e)}function u(e){try{return e instanceof a.RuntimeError}catch(e){return!1}}function c(e,r){return{__emnapi__:{type:e,payload:r}}}function f(e){if(e){if(!s(e.buffer))throw new Error("Multithread features require shared wasm memory. Try to compile with `-matomics -mbulk-memory` and use `--import-memory --shared-memory` during linking, then create WebAssembly.Memory with `shared: true` option")}else if("undefined"==typeof SharedArrayBuffer)throw new Error("Current environment does not support SharedArrayBuffer, threads are not available!")}var l=0,d=function(){function e(e){var r;if(this.unusedWorkers=[],this.runningWorkers=[],this.pthreads=Object.create(null),this.wasmModule=null,this.wasmMemory=null,this.messageEvents=new WeakMap,!e)throw new TypeError("ThreadManager(): options is not provided");this._childThread="childThread"in e&&Boolean(e.childThread),this._childThread?(this._onCreateWorker=void 0,this._reuseWorker=!1,this._beforeLoad=void 0):(this._onCreateWorker=e.onCreateWorker,this._reuseWorker=function(e){var r;if("boolean"==typeof e)return!!e&&{size:0,strict:!1};if("number"==typeof e){if(!(e>=0))throw new RangeError("reuseWorker: size must be a non-negative integer");return{size:e,strict:!1}}if(!e)return!1;var t=null!==(r=Number(e.size))&&void 0!==r?r:0,n=Boolean(e.strict);if(!(t>0)&&n)throw new RangeError("reuseWorker: size must be set to positive integer if strict is set to true");return{size:t,strict:n}}(e.reuseWorker),this._beforeLoad=e.beforeLoad),this.printErr=null!==(r=e.printErr)&&void 0!==r?r:console.error.bind(console)}return Object.defineProperty(e.prototype,"nextWorkerID",{get:function(){return l},enumerable:!1,configurable:!0}),e.prototype.init=function(){this._childThread||this.initMainThread()},e.prototype.initMainThread=function(){this.preparePool()},e.prototype.preparePool=function(){if(this._reuseWorker&&this._reuseWorker.size)for(var e=this._reuseWorker.size;e--;){var r=this.allocateUnusedWorker();o&&(r.once("message",function(){}),r.unref())}},e.prototype.shouldPreloadWorkers=function(){return!this._childThread&&this._reuseWorker&&this._reuseWorker.size>0},e.prototype.loadWasmModuleToAllWorkers=function(){for(var e=this,r=Array(this.unusedWorkers.length),t=function(e){var t=n.unusedWorkers[e];o&&t.ref(),r[e]=n.loadWasmModuleToWorker(t).then(function(e){return o&&t.unref(),e},function(e){throw o&&t.unref(),e})},n=this,a=0;a<this.unusedWorkers.length;++a)t(a);return Promise.all(r).catch(function(r){throw e.terminateAllThreads(),r})},e.prototype.preloadWorkers=function(){return this.shouldPreloadWorkers()?this.loadWasmModuleToAllWorkers():Promise.resolve([])},e.prototype.setup=function(e,r){this.wasmModule=e,this.wasmMemory=r},e.prototype.markId=function(e){if(e.__emnapi_tid)return e.__emnapi_tid;var r=l+43;return l=(l+1)%536870869,this.pthreads[r]=e,e.__emnapi_tid=r,r},e.prototype.returnWorkerToPool=function(e){var r=e.__emnapi_tid;void 0!==r&&delete this.pthreads[r],this.unusedWorkers.push(e),this.runningWorkers.splice(this.runningWorkers.indexOf(e),1),delete e.__emnapi_tid,o&&e.unref()},e.prototype.loadWasmModuleToWorker=function(e,r){var t=this;if(e.whenLoaded)return e.whenLoaded;var n=this.printErr,a=this._beforeLoad,i=this;return e.whenLoaded=new Promise(function(s,u){e.onmessage=function(r){!function(r){if(r.__emnapi__){var n=r.__emnapi__.type,a=r.__emnapi__.payload;"loaded"===n?(e.loaded=!0,o&&!e.__emnapi_tid&&e.unref(),s(e)):"cleanup-thread"===n&&a.tid in t.pthreads&&t.cleanThread(e,a.tid)}}(r.data),t.fireMessageEvent(e,r)},e.onerror=function(r){var t="worker sent an error!";if(void 0!==e.__emnapi_tid&&(t="worker (tid = "+e.__emnapi_tid+") sent an error!"),"message"in r){if(n(t+" "+r.message),-1!==r.message.indexOf("RuntimeError")||-1!==r.message.indexOf("unreachable"))try{i.terminateAllThreads()}catch(e){}}else n(t);throw u(r),r},o&&(e.on("message",function(r){var t,n;null===(n=(t=e).onmessage)||void 0===n||n.call(t,{data:r})}),e.on("error",function(r){var t,n;null===(n=(t=e).onerror)||void 0===n||n.call(t,r)}),e.on("detachedExit",function(){})),"function"==typeof a&&a(e);try{e.postMessage(c("load",{wasmModule:t.wasmModule,wasmMemory:t.wasmMemory,sab:r}))}catch(e){throw f(t.wasmMemory),e}}),e.whenLoaded},e.prototype.allocateUnusedWorker=function(){var e=this._onCreateWorker;if("function"!=typeof e)throw new TypeError("`options.onCreateWorker` is not provided");var r=e({type:"thread",name:"emnapi-pthread"});return this.unusedWorkers.push(r),r},e.prototype.getNewWorker=function(e){if(this._reuseWorker){if(0===this.unusedWorkers.length){if(this._reuseWorker.strict)if(!o)return void(0,this.printErr)("Tried to spawn a new thread, but the thread pool is exhausted.\nThis might result in a deadlock unless some threads eventually exit or the code explicitly breaks out to the event loop.");var r=this.allocateUnusedWorker();this.loadWasmModuleToWorker(r,e)}return this.unusedWorkers.pop()}var t=this.allocateUnusedWorker();return this.loadWasmModuleToWorker(t,e),this.unusedWorkers.pop()},e.prototype.cleanThread=function(e,r,t){if(!t&&this._reuseWorker)this.returnWorkerToPool(e);else{delete this.pthreads[r];var n=this.runningWorkers.indexOf(e);-1!==n&&this.runningWorkers.splice(n,1),this.terminateWorker(e),delete e.__emnapi_tid}},e.prototype.terminateWorker=function(e){var r,t=this,n=e.__emnapi_tid;e.terminate(),null===(r=this.messageEvents.get(e))||void 0===r||r.clear(),this.messageEvents.delete(e),e.onmessage=function(e){e.data.__emnapi__&&(0,t.printErr)('received "'+e.data.__emnapi__.type+'" command from terminated worker: '+n)}},e.prototype.terminateAllThreads=function(){for(var e=0;e<this.runningWorkers.length;++e)this.terminateWorker(this.runningWorkers[e]);for(e=0;e<this.unusedWorkers.length;++e)this.terminateWorker(this.unusedWorkers[e]);this.unusedWorkers=[],this.runningWorkers=[],this.pthreads=Object.create(null),this.preparePool()},e.prototype.addMessageEventListener=function(e,r){var t=this.messageEvents.get(e);return t||(t=new Set,this.messageEvents.set(e,t)),t.add(r),function(){null==t||t.delete(r)}},e.prototype.fireMessageEvent=function(e,r){var t=this.messageEvents.get(e);if(t){var n=this.printErr;t.forEach(function(e){try{e(r)}catch(e){n(e.stack)}})}},e}(),p=Symbol("kIsProxy");function v(e,r){if(e[p])return e;var t=e.exports,n=function(e){for(var r=["apply","construct","defineProperty","deleteProperty","get","getOwnPropertyDescriptor","getPrototypeOf","has","isExtensible","ownKeys","preventExtensions","set","setPrototypeOf"],t={},n=function(n){var a=r[n];t[a]=function(){var r=Array.prototype.slice.call(arguments,1);return r.unshift(e),Reflect[a].apply(Reflect,r)}},a=0;a<r.length;a++)n(a);return t}(t),a=function(){},o=function(){return 0};n.get=function(e,n,i){var s;return"memory"===n?null!==(s="function"==typeof r?r():r)&&void 0!==s?s:Reflect.get(t,n,i):"_initialize"===n?n in t?a:void 0:"_start"===n?n in t?o:void 0:Reflect.get(t,n,i)},n.has=function(e,r){return"memory"===r||Reflect.has(t,r)};var i=new Proxy(Object.create(null),n);return new Proxy(e,{get:function(e,r,t){return"exports"===r?i:r===p||Reflect.get(e,r,t)}})}var h=new WeakMap,y=function(){function e(e){var r=this;if(!e)throw new TypeError("WASIThreads(): options is not provided");if(!e.wasi)throw new TypeError("WASIThreads(): options.wasi is not provided");h.set(this,new WeakSet);var t=e.wasi;!function(e,r){var t=h.get(e);if(t.has(r))return;var n=e,a=r.wasiImport;if(a){var o=a.proc_exit;a.proc_exit=function(e){return n.terminateAllThreads(),o.call(this,e)}}if(!n.childThread){var i=r.start;"function"==typeof i&&(r.start=function(e){try{return i.call(this,e)}catch(e){throw u(e)&&n.terminateAllThreads(),e}})}t.add(r)}(this,t),this.wasi=t,this.childThread="childThread"in e&&Boolean(e.childThread),this.PThread=void 0,"threadManager"in e?"function"==typeof e.threadManager?this.PThread=e.threadManager():this.PThread=e.threadManager:this.childThread||(this.PThread=new d(e),this.PThread.init());var n=!1;"waitThreadStart"in e&&(n="number"==typeof e.waitThreadStart?e.waitThreadStart:Boolean(e.waitThreadStart));var s=i(e);if(this.childThread&&"function"!=typeof s)throw new TypeError("options.postMessage is not a function");this.postMessage=s;var l=Boolean(e.wasm64),p=function(e){if(e.data.__emnapi__){var t=e.data.__emnapi__.type,n=e.data.__emnapi__.payload;"spawn-thread"===t?v(n.startArg,n.errorOrTid):"terminate-all-threads"===t&&r.terminateAllThreads()}},v=function(e,t){var i,u=void 0!==t;try{f(r.wasmMemory)}catch(e){if(null===(i=r.PThread)||void 0===i||i.printErr(e.stack),u){var d=new Int32Array(r.wasmMemory.buffer,t,2);return Atomics.store(d,0,1),Atomics.store(d,1,6),Atomics.notify(d,1),1}return-6}if(!u){var v=r.wasmInstance.exports.malloc;if(!(t=l?Number(v(BigInt(8))):v(8)))return-48}var h=r.wasmInstance.exports.free,y=l?function(e){h(BigInt(e))}:h,g=new Int32Array(r.wasmMemory.buffer,t,2);if(Atomics.store(g,0,0),Atomics.store(g,1,0),r.childThread){s(c("spawn-thread",{startArg:e,errorOrTid:t})),Atomics.wait(g,1,0);var _=Atomics.load(g,0),E=Atomics.load(g,1);return u?_:(y(t),_?-E:E)}var w,m,L,b=n||0===n;b&&(w=new Int32Array(new SharedArrayBuffer(8208)),Atomics.store(w,0,0));var S=r.PThread;try{if(!(m=S.getNewWorker(w)))throw new Error("failed to get new worker");if(S.addMessageEventListener(m,p),L=S.markId(m),o&&m.ref(),m.postMessage(c("start",{tid:L,arg:e,sab:w})),b){if("number"==typeof n){if("timed-out"===Atomics.wait(w,0,0,n)){try{S.cleanThread(m,L,!0)}catch(e){}throw new Error("Spawning thread timed out. Please check if the worker is created successfully and if message is handled properly in the worker.")}}else Atomics.wait(w,0,0);if(Atomics.load(w,0)>1){try{S.cleanThread(m,L,!0)}catch(e){}throw function(e){var r,t,n=new Int32Array(e);if(Atomics.load(n,0)<=1)return null;var o=Atomics.load(n,1),i=Atomics.load(n,2),s=Atomics.load(n,3),u=new Uint8Array(e),c=u.slice(16,16+o),f=u.slice(16+o,16+o+i),l=u.slice(16+o+i,16+o+i+s),d=(new TextDecoder).decode(c),p=(new TextDecoder).decode(f),v=(new TextDecoder).decode(l),h=new(null!==(r=globalThis[d])&&void 0!==r?r:"RuntimeError"===d&&null!==(t=a.RuntimeError)&&void 0!==t?t:Error)(p);return Object.defineProperty(h,"stack",{value:v,writable:!0,enumerable:!1,configurable:!0}),h}(w.buffer)}}}catch(e){return Atomics.store(g,0,1),Atomics.store(g,1,6),Atomics.notify(g,1),null==S||S.printErr(e.stack),u?1:(y(t),-6)}return Atomics.store(g,0,0),Atomics.store(g,1,L),Atomics.notify(g,1),S.runningWorkers.push(m),b||m.whenLoaded.catch(function(e){throw delete m.whenLoaded,S.cleanThread(m,L,!0),e}),u?0:(y(t),L)};this.threadSpawn=v}return e.prototype.getImportObject=function(){return{wasi:{"thread-spawn":this.threadSpawn}}},e.prototype.setup=function(e,r,t){null!=t||(t=e.exports.memory),this.wasmInstance=e,this.wasmMemory=t,this.PThread&&this.PThread.setup(r,t)},e.prototype.preloadWorkers=function(){return this.PThread?this.PThread.preloadWorkers():Promise.resolve([])},e.prototype.initialize=function(e,r,t){var n=e.exports;null!=t||(t=n.memory),this.childThread&&(e=v(e,t)),this.setup(e,r,t);var a=this.wasi;if("_start"in n&&"function"==typeof n._start)if(this.childThread){a.start(e);try{a[g(a,"kStarted")]=!1}catch(e){}}else!function(e,r){var t=g(e,["kInstance","kSetMemory"]),n=t[0],a=t[1];e[n]=r,e[a](r.exports.memory)}(a,e);else a.initialize(e);return e},e.prototype.start=function(e,r,t){var n=e.exports;return null!=t||(t=n.memory),this.childThread&&(e=v(e,t)),this.setup(e,r,t),{exitCode:this.wasi.start(e),instance:e}},e.prototype.terminateAllThreads=function(){var e;this.childThread?this.postMessage(c("terminate-all-threads",{})):null===(e=this.PThread)||void 0===e||e.terminateAllThreads()},e}();function g(e,r){var t=Object.getOwnPropertySymbols(e),n=function(e){return function(r){return r.description?r.description===e:r.toString()==="Symbol(".concat(e,")")}};return Array.isArray(r)?r.map(function(e){return t.filter(n(e))[0]}):t.filter(n(r))[0]}var _=function(){function e(e){var r=i(e);if("function"!=typeof r)throw new TypeError("options.postMessage is not a function");this.postMessage=r,this.onLoad=null==e?void 0:e.onLoad,this.onError="function"==typeof(null==e?void 0:e.onError)?e.onError:function(e,r){throw r},this.instance=void 0,this.messagesBeforeLoad=[]}return e.prototype.instantiate=function(e){if("function"==typeof this.onLoad)return this.onLoad(e);throw new Error("ThreadMessageHandler.prototype.instantiate is not implemented")},e.prototype.handle=function(e){var r,t=this;if(null===(r=null==e?void 0:e.data)||void 0===r?void 0:r.__emnapi__){var n=e.data.__emnapi__.type,a=e.data.__emnapi__.payload;try{"load"===n?this._load(a):"start"===n&&this.handleAfterLoad(e,function(){t._start(a)})}catch(e){this.onError(e,n)}}},e.prototype._load=function(e){var r=this;if(void 0===this.instance){var t;try{t=this.instantiate(e)}catch(r){return void this._loaded(r,null,e)}var n=t&&"then"in t?t.then:void 0;"function"==typeof n?n.call(t,function(t){r._loaded(null,t,e)},function(t){r._loaded(t,null,e)}):this._loaded(null,t,e)}},e.prototype._start=function(e){var r=this.instance.exports.wasi_thread_start;if("function"!=typeof r){var t=new TypeError("wasi_thread_start is not exported");throw E(e.sab,2,t),t}var n=this.postMessage,a=e.tid,o=e.arg;E(e.sab,1);try{r(a,o)}catch(t){if("unwind"!==t)throw t;return}n(c("cleanup-thread",{tid:a}))},e.prototype._loaded=function(e,r,t){if(e)throw E(t.sab,2,e),e;if(null==r){var n=new TypeError("onLoad should return an object");throw E(t.sab,2,n),n}var a=r.instance;if(!a){var o=new TypeError('onLoad should return an object which includes "instance"');throw E(t.sab,2,o),o}this.instance=a,(0,this.postMessage)(c("loaded",{}));var i=this.messagesBeforeLoad;this.messagesBeforeLoad=[];for(var s=0;s<i.length;s++){var u=i[s];this.handle({data:u})}},e.prototype.handleAfterLoad=function(e,r){void 0!==this.instance?r.call(this,e):this.messagesBeforeLoad.push(e.data)},e}();function E(e,r,t){e&&(!function(e,r,t){var n=new Int32Array(e);if(Atomics.store(n,0,r),r>1&&t){var a=t.name,o=t.message,i=t.stack,s=(new TextEncoder).encode(a),u=(new TextEncoder).encode(o),c=(new TextEncoder).encode(i);Atomics.store(n,1,s.length),Atomics.store(n,2,u.length),Atomics.store(n,3,c.length);var f=new Uint8Array(e);f.set(s,16),f.set(u,16+s.length),f.set(c,16+s.length+u.length)}}(e.buffer,r,t),Atomics.notify(e,0))}function w(e){var t=function(){var t,n,a,o,i,s="object"==typeof process&&null!==process&&"object"==typeof process.versions&&null!==process.versions&&"string"==typeof process.versions.node,u=Boolean(e.childThread),c="number"==typeof e.waitThreadStart?e.waitThreadStart:Boolean(e.waitThreadStart);function f(e){if("function"==typeof r.RuntimeError)throw new r.RuntimeError(e);throw Error(e)}var l,p,v,h={imports:{env:{},napi:{},emnapi:{}},exports:{},emnapi:{},loaded:!1,filename:"",childThread:u,initWorker:void 0,executeAsyncWork:void 0,waitThreadStart:c,PThread:void 0,init:function(e){if(h.loaded)return h.exports;if(!e)throw new TypeError("Invalid napi init options");var s=e.instance;if(!(null==s?void 0:s.exports))throw new TypeError("Invalid wasm instance");t=s;var u=s.exports,c=e.module,d=e.memory||u.memory,v=e.table||u.__indirect_function_table;if(!(c instanceof r.Module))throw new TypeError("Invalid wasm module");if(!(d instanceof r.Memory))throw new TypeError("Invalid wasm memory");if(!(v instanceof r.Table))throw new TypeError("Invalid wasm table");if(n=d,a=v,"function"!=typeof u.malloc)throw new TypeError("malloc is not exported");if("function"!=typeof u.free)throw new TypeError("free is not exported");if(o=u.malloc,i=u.free,!h.childThread){var y=8,g=s.exports.node_api_module_get_api_version_v1;"function"==typeof g&&(y=g());var _=h.envObject||(h.envObject=l.createEnv(h.filename,y,function(e){return a.get(e)},function(e){return a.get(e)},f,p)),E=l.openScope(_);try{_.callIntoModule(function(e){var r=h.exports,t=E.add(r),n=(0,s.exports.napi_register_wasm_v1)(e.id,t.id);h.exports=n?l.handleStore.get(n).value:r})}catch(e){if("unwind"!==e)throw e}finally{l.closeScope(_,E)}return h.loaded=!0,delete h.envObject,h.exports}}},y=void 0;if(u){l=null==e?void 0:e.context;var g="function"==typeof e.postMessage?e.postMessage:"function"==typeof postMessage?postMessage:void 0;if("function"!=typeof g)throw new TypeError("No postMessage found");h.postMessage=g}else{var _=e.context;if("object"!=typeof _||null===_)throw new TypeError("Invalid `options.context`. Use `import { getDefaultContext } from '@emnapi/runtime'`");l=_}if("string"==typeof e.filename&&(h.filename=e.filename),"function"==typeof e.onCreateWorker&&(y=e.onCreateWorker),"function"==typeof e.print?e.print:console.log.bind(console),v="function"==typeof e.printErr?e.printErr:console.warn.bind(console),"nodeBinding"in e){var E=e.nodeBinding;if("object"!=typeof E||null===E)throw new TypeError("Invalid `options.nodeBinding`. Use @emnapi/node-binding package");p=E}var w=0;if("asyncWorkPoolSize"in e){if("number"!=typeof e.asyncWorkPoolSize)throw new TypeError("options.asyncWorkPoolSize must be a integer");(w=e.asyncWorkPoolSize|0)>1024?w=1024:w<-1024&&(w=-1024)}var m=!u&&w<=0;function L(){return Math.abs(w)}function b(e){if(!e)return!1;if(e._emnapiSendListener)return!0;var r=function(e){var r=(s?e:e.data).__emnapi__;if(r&&"async-send"===r.type)if(u){(0,h.postMessage)({__emnapi__:r})}else{var t=r.payload.callback;a.get(t)(r.payload.data)}};return e._emnapiSendListener={handler:r,dispose:function(){s?e.off("message",r):e.removeEventListener("message",r,!1),delete e._emnapiSendListener}},s?e.on("message",r):e.addEventListener("message",r,!1),!0}h.imports.env._emnapi_async_work_pool_size=L,h.emnapi.addSendListener=b;var S=new d(u?{printErr:v,childThread:!0}:{printErr:v,beforeLoad:function(e){b(e)},reuseWorker:e.reuseWorker,onCreateWorker:y});function C(e,r){l.feature.setImmediate(function(){a.get(e)(r)})}function A(e,r){Promise.resolve().then(function(){a.get(e)(r)})}function k(e,r){var t,a=[r>>>0,(t=r,+Math.abs(t)>=1?t>0?(0|Math.min(+Math.floor(t/4294967296),4294967295))>>>0:~~+Math.ceil((t-+(~~t>>>0))/4294967296)>>>0:0)],o=new DataView(n.buffer);o.setInt32(e,a[0],!0),o.setInt32(e+4,a[1],!0)}h.PThread=S;var I,T=Object.freeze({__proto__:null,$emnapiSetValueI64:k,_emnapi_call_finalizer:function(e,r,t,n,a){l.envStore.get(r).callFinalizerInternal(e,t,n,a)},_emnapi_callback_into_module:function(e,r,t,n,o){var i=l.envStore.get(r),s=l.openScope(i);try{i.callbackIntoModule(Boolean(e),function(){a.get(t)(r,n)})}catch(e){throw l.closeScope(i,s),o&&l.closeScope(i),e}l.closeScope(i,s)},_emnapi_close_handle_scope:function(e){return l.closeScope()},_emnapi_ctx_decrease_waiting_request_counter:function(){l.decreaseWaitingRequestCounter()},_emnapi_ctx_increase_waiting_request_counter:function(){l.increaseWaitingRequestCounter()},_emnapi_get_node_version:function(e,r,t){var a="object"==typeof process&&null!==process&&"object"==typeof process.versions&&null!==process.versions&&"string"==typeof process.versions.node?process.versions.node.split(".").map(function(e){return Number(e)}):[0,0,0],o=new DataView(n.buffer);o.setUint32(e,a[0],!0),o.setUint32(r,a[1],!0),o.setUint32(t,a[2],!0)},_emnapi_get_now:function(){return performance.timeOrigin+performance.now()},_emnapi_is_main_browser_thread:function(){return"undefined"==typeof window||"undefined"==typeof document||s?0:1},_emnapi_is_main_runtime_thread:function(){return u?0:1},_emnapi_next_tick:A,_emnapi_open_handle_scope:function(){return l.openScope().id},_emnapi_runtime_keepalive_pop:function(){},_emnapi_runtime_keepalive_push:function(){},_emnapi_set_immediate:C,_emnapi_unwind:function(){throw"unwind"},napi_clear_last_error:function(e){return l.envStore.get(e).clearLastError()},napi_set_last_error:function(e,r,t,n){return l.envStore.get(e).setLastError(r,t,n)}});function V(e){var r=new DataView(n.buffer).getInt32(e+20,!0);return S.pthreads[r]}var U=new Promise(function(e){I=function(){U.ready=!0,e()}});U.ready=!1;var B=Object.freeze({__proto__:null,_emnapi_after_uvthreadpool_ready:function(e,r,t){U.ready?a.get(e)(r,t):U.then(function(){a.get(e)(r,t)})},_emnapi_async_send_js:function(e,r,t){if(u)(0,h.postMessage)({__emnapi__:{type:"async-send",payload:{callback:r,data:t}}});else switch(e){case 0:C(r,t);break;case 1:A(r,t)}},_emnapi_emit_async_thread_ready:function(){u&&(0,h.postMessage)({__emnapi__:{type:"async-thread-ready",payload:{}}})},_emnapi_tell_js_uvthreadpool:function(e,r){for(var t=[],a=new DataView(n.buffer),o=function(r){var n=V(a.getUint32(e+4*r,!0));t.push(new Promise(function(e){var r=function(t){var a=(s?t:t.data).__emnapi__;a&&"async-thread-ready"===a.type&&(e(),n&&"function"==typeof n.unref&&n.unref(),s?n.off("message",r):n.removeEventListener("message",r))};s?n.on("message",r):n.addEventListener("message",r)}))},i=0;i<r;i++)o(i);Promise.all(t).then(I)},_emnapi_worker_unref:function(e){if(!u){var r=V(e);r&&"function"==typeof r.unref&&r.unref()}}});var D=Object.freeze({__proto__:null,napi_adjust_external_memory:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(!t)return a.setLastError(1);var o=Number(r);if(o<0)return a.setLastError(1);var i=n.buffer.byteLength,s=i+o;return s+=(65536-s%65536)%65536,-1===n.grow(s-i+65535>>16)?a.setLastError(9):(l.feature.supportBigInt?new DataView(n.buffer).setBigInt64(t,BigInt(n.buffer.byteLength),!0):k(t,n.buffer.byteLength),a.clearLastError())}}),x={idGen:{},values:[void 0],queued:new Set,pending:[],init:function(){var e={nextId:1,list:[],generate:function(){var r;return e.list.length?r=e.list.shift():(r=e.nextId,e.nextId++),r},reuse:function(r){e.list.push(r)}};x.idGen=e,x.values=[void 0],x.queued=new Set,x.pending=[]},create:function(e,r,t,n,a,o){var i=0,s=0;if(p){var u=p.node.emitAsyncInit(r,t,-1);i=u.asyncId,s=u.triggerAsyncId}var c=x.idGen.generate();return x.values[c]={env:e,id:c,resource:r,asyncId:i,triggerAsyncId:s,status:0,execute:n,complete:a,data:o},c},callComplete:function(e,r){var t=e.complete,n=e.env,o=e.data,i=function(){if(t){var e=l.envStore.get(n),i=l.openScope(e);try{e.callbackIntoModule(!0,function(){a.get(t)(n,r,o)})}finally{l.closeScope(e,i)}}};p?p.node.makeCallback(e.resource,i,[],{asyncId:e.asyncId,triggerAsyncId:e.triggerAsyncId}):i()},queue:function(e){var r=x.values[e];if(r&&0===r.status){if(r.status=1,x.queued.size>=(Math.abs(w)||4))return void x.pending.push(e);x.queued.add(e);var t=r.env,n=r.data,o=r.execute;r.status=2,l.feature.setImmediate(function(){if(a.get(o)(t,n),x.queued.delete(e),r.status=3,l.feature.setImmediate(function(){x.callComplete(r,0)}),x.pending.length>0){var i=x.pending.shift();x.values[i].status=0,x.queue(i)}})}},cancel:function(e){var r=x.pending.indexOf(e);if(-1!==r){var t=x.values[e];return t&&1===t.status?(t.status=4,x.pending.splice(r,1),l.feature.setImmediate(function(){x.callComplete(t,11)}),0):9}return 9},remove:function(e){var r=x.values[e];r&&(p&&p.node.emitAsyncDestroy({asyncId:r.asyncId,triggerAsyncId:r.triggerAsyncId}),x.values[e]=void 0,x.idGen.reuse(e))}};function M(e,r,t,a){if(p){var o=l.handleStore.get(e).value,i=l.handleStore.get(r).value,s=p.node.emitAsyncInit(o,i,t),u=s.asyncId,c=s.triggerAsyncId;if(a){var f=new DataView(n.buffer);f.setFloat64(a,u,!0),f.setFloat64(a+8,c,!0)}}}function W(e,r){p&&p.node.emitAsyncDestroy({asyncId:e,triggerAsyncId:r})}var j=Object.freeze({__proto__:null,_emnapi_async_destroy_js:function(e){if(!p)return 9;var r=new DataView(n.buffer),t=r.getInt32(e,!0),a=r.getInt32(e+4,!0),o=BigInt(t>>>0)|BigInt(a)<<BigInt(32),i=p.napi.asyncDestroy(o);return 0!==i.status?i.status:0},_emnapi_async_init_js:function(e,r,t){if(!p)return 9;var a;e&&(a=Object(l.handleStore.get(e).value));var o=l.handleStore.get(r).value,i=p.napi.asyncInit(a,o);if(0!==i.status)return i.status;var s=i.value;s>=BigInt(-1)*(BigInt(1)<<BigInt(63))&&s<BigInt(1)<<BigInt(63)||(s&=(BigInt(1)<<BigInt(64))-BigInt(1))>=BigInt(1)<<BigInt(63)&&(s-=BigInt(1)<<BigInt(64));var u=Number(s&BigInt(4294967295)),c=Number(s>>BigInt(32)),f=new DataView(n.buffer);return f.setInt32(t,u,!0),f.setInt32(t+4,c,!0),0},_emnapi_env_check_gc_access:function(e){l.envStore.get(e).checkGCAccess()},_emnapi_node_emit_async_destroy:W,_emnapi_node_emit_async_init:M,_emnapi_node_make_callback:function(e,r,t,a,o,i,s,u){var c,f=0;if(p){var d=l.handleStore.get(r).value,v=l.handleStore.get(t).value;o>>>=0;for(var h=Array(o),y=new DataView(n.buffer);f<o;f++){var g=y.getUint32(a+4*f,!0);h[f]=l.handleStore.get(g).value}var _=p.node.makeCallback(d,v,h,{asyncId:i,triggerAsyncId:s});if(u)c=l.envStore.get(e).ensureHandleId(_),y.setUint32(u,c,!0)}},napi_close_callback_scope:function(e,r){throw new Error("napi_close_callback_scope has not been implemented yet")},napi_make_callback:function(e,r,t,a,o,i,s){var u,c=0;if(!e)return 1;var f=l.envStore.get(e);if(f.checkGCAccess(),!f.tryCatch.isEmpty())return f.setLastError(10);if(!f.canCallIntoJs())return f.setLastError(f.moduleApiVersion>=10?23:10);f.clearLastError();try{if(!p)return f.setLastError(9);if(!t)return f.setLastError(1);if(o>0&&!i)return f.setLastError(1);var d=Object(l.handleStore.get(t).value),v=l.handleStore.get(a).value;if("function"!=typeof v)return f.setLastError(1);var h=new DataView(n.buffer),y=h.getInt32(r,!0),g=h.getInt32(r+4,!0),_=BigInt(y>>>0)|BigInt(g)<<BigInt(32);o>>>=0;for(var E=Array(o);c<o;c++){var w=h.getUint32(i+4*c,!0);E[c]=l.handleStore.get(w).value}var m=p.napi.makeCallback(_,d,v,E);if(m.error)throw m.error;return 0!==m.status?f.setLastError(m.status):(s&&(u=f.ensureHandleId(m.value),h.setUint32(s,u,!0)),f.getReturnStatus())}catch(e){return f.tryCatch.setError(e),f.setLastError(10)}},napi_open_callback_scope:function(e,r,t,n){throw new Error("napi_open_callback_scope has not been implemented yet")}}),R={offset:{resource:0,async_id:8,trigger_async_id:16,queue_size:24,queue:28,thread_count:32,is_closing:36,dispatch_state:40,context:44,max_queue_size:48,ref:52,env:56,finalize_data:60,finalize_cb:64,call_js_cb:68,handles_closing:72,async_ref:76,mutex:80,cond:84,end:88},init:function(){if(void 0!==S){S.unusedWorkers.forEach(R.addListener),S.runningWorkers.forEach(R.addListener);var e=S.getNewWorker;S.getNewWorker=function(){var r=e.apply(this,arguments);return R.addListener(r),r}}},addListener:function(e){if(!e)return!1;if(e._emnapiTSFNListener)return!0;var r=function(e){var r=(s?e:e.data).__emnapi__;if(r){var t=r.type,n=r.payload;"tsfn-send"===t&&R.dispatch(n.tsfn)}};return e._emnapiTSFNListener={handler:r,dispose:function(){s?e.off("message",r):e.removeEventListener("message",r,!1),delete e._emnapiTSFNListener}},s?e.on("message",r):e.addEventListener("message",r,!1),!0},initQueue:function(e){var r=o(8);return!!r&&(new Uint8Array(n.buffer,r,8).fill(0),R.storeSizeTypeValue(e+R.offset.queue,r,!1),!0)},destroyQueue:function(e){var r=R.loadSizeTypeValue(e+R.offset.queue,!1);r&&i(r)},pushQueue:function(e,r){var t=R.loadSizeTypeValue(e+R.offset.queue,!1),n=R.loadSizeTypeValue(t,!1),a=R.loadSizeTypeValue(t+4,!1),i=o(8);if(!i)throw new Error("OOM");R.storeSizeTypeValue(i,r,!1),R.storeSizeTypeValue(i+4,0,!1),0===n&&0===a?(R.storeSizeTypeValue(t,i,!1),R.storeSizeTypeValue(t+4,i,!1)):(R.storeSizeTypeValue(a+4,i,!1),R.storeSizeTypeValue(t+4,i,!1)),R.addQueueSize(e)},shiftQueue:function(e){var r=R.loadSizeTypeValue(e+R.offset.queue,!1),t=R.loadSizeTypeValue(r,!1);if(0===t)return 0;var n=t,a=R.loadSizeTypeValue(t+4,!1);R.storeSizeTypeValue(r,a,!1),0===a&&R.storeSizeTypeValue(r+4,0,!1),R.storeSizeTypeValue(n+4,0,!1);var o=R.loadSizeTypeValue(n,!1);return i(n),R.subQueueSize(e),o},push:function(e,r,t){var n=R.getMutex(e),a=R.getCond(e),o=function(){var r=R.getQueueSize(e),t=R.getMaxQueueSize(e),n=R.getIsClosing(e);return r>=t&&t>0&&!n},i="undefined"!=typeof window&&"undefined"!=typeof document&&!s;return n.execute(function(){for(;o();){if(0===t)return 15;if(i)return 21;a.wait()}return R.getIsClosing(e)?0===R.getThreadCount(e)?1:(R.subThreadCount(e),16):(R.pushQueue(e,r),R.send(e),0)})},getMutex:function(e){var r=e+R.offset.mutex,t={lock:function(){var e="undefined"!=typeof window&&"undefined"!=typeof document&&!s,t=new Int32Array(n.buffer,r,1);if(e)for(;;){if(0===Atomics.compareExchange(t,0,0,1))return}else for(;;){if(0===Atomics.compareExchange(t,0,0,1))return;Atomics.wait(t,0,1)}},unlock:function(){var e=new Int32Array(n.buffer,r,1);if(1!==Atomics.compareExchange(e,0,1,0))throw new Error("Tried to unlock while not holding the mutex");Atomics.notify(e,0,1)},execute:function(e){t.lock();try{return e()}finally{t.unlock()}}};return t},getCond:function(e){var r=e+R.offset.cond,t=R.getMutex(e);return{wait:function(){var e=new Int32Array(n.buffer,r,1),a=Atomics.load(e,0);t.unlock(),Atomics.wait(e,0,a),t.lock()},signal:function(){var e=new Int32Array(n.buffer,r,1);Atomics.add(e,0,1),Atomics.notify(e,0,1)}}},getQueueSize:function(e){return R.loadSizeTypeValue(e+R.offset.queue_size,!0)},addQueueSize:function(e){var r,t,a=R.offset.queue_size;r=new Uint32Array(n.buffer),t=e+a>>2,Atomics.add(r,t,1)},subQueueSize:function(e){var r,t,a=R.offset.queue_size;r=new Uint32Array(n.buffer),t=e+a>>2,Atomics.sub(r,t,1)},getThreadCount:function(e){return R.loadSizeTypeValue(e+R.offset.thread_count,!0)},addThreadCount:function(e){var r,t,a=R.offset.thread_count;r=new Uint32Array(n.buffer),t=e+a>>2,Atomics.add(r,t,1)},subThreadCount:function(e){var r,t,a=R.offset.thread_count;r=new Uint32Array(n.buffer),t=e+a>>2,Atomics.sub(r,t,1)},getIsClosing:function(e){return Atomics.load(new Int32Array(n.buffer),e+R.offset.is_closing>>2)},setIsClosing:function(e,r){Atomics.store(new Int32Array(n.buffer),e+R.offset.is_closing>>2,r)},getHandlesClosing:function(e){return Atomics.load(new Int32Array(n.buffer),e+R.offset.handles_closing>>2)},setHandlesClosing:function(e,r){Atomics.store(new Int32Array(n.buffer),e+R.offset.handles_closing>>2,r)},getDispatchState:function(e){return Atomics.load(new Uint32Array(n.buffer),e+R.offset.dispatch_state>>2)},getContext:function(e){return R.loadSizeTypeValue(e+R.offset.context,!1)},getMaxQueueSize:function(e){return R.loadSizeTypeValue(e+R.offset.max_queue_size,!0)},getEnv:function(e){return R.loadSizeTypeValue(e+R.offset.env,!1)},getCallJSCb:function(e){return R.loadSizeTypeValue(e+R.offset.call_js_cb,!1)},getRef:function(e){return R.loadSizeTypeValue(e+R.offset.ref,!1)},getResource:function(e){return R.loadSizeTypeValue(e+R.offset.resource,!1)},getFinalizeCb:function(e){return R.loadSizeTypeValue(e+R.offset.finalize_cb,!1)},getFinalizeData:function(e){return R.loadSizeTypeValue(e+R.offset.finalize_data,!1)},loadSizeTypeValue:function(e,r){var t;return r?(t=new Uint32Array(n.buffer),Atomics.load(t,e>>2)):(t=new Int32Array(n.buffer),Atomics.load(t,e>>2))},storeSizeTypeValue:function(e,r,t){var a;return t?(a=new Uint32Array(n.buffer),void Atomics.store(a,e>>2,r)):(a=new Int32Array(n.buffer),void Atomics.store(a,e>>2,r>>>0))},destroy:function(e){R.destroyQueue(e);var r=R.getEnv(e),t=l.envStore.get(r),a=R.getRef(e);a&&l.refStore.get(a).dispose(),l.removeCleanupHook(t,R.cleanup,e),t.unref();var o=e+R.offset.async_ref>>2,s=new Int32Array(n.buffer);Atomics.load(s,o)&&(Atomics.store(s,o,0),l.decreaseWaitingRequestCounter());var u=R.getResource(e);if(l.refStore.get(u).dispose(),p){var c=new DataView(n.buffer);W(c.getFloat64(e+R.offset.async_id,!0),c.getFloat64(e+R.offset.trigger_async_id,!0))}i(e)},emptyQueueAndDelete:function(e){for(var r,t=R.getCallJSCb(e),n=R.getContext(e);R.getQueueSize(e)>0;)r=R.shiftQueue(e),t&&a.get(t)(0,0,n,r);R.destroy(e)},finalize:function(e){var r=R.getEnv(e),t=l.envStore.get(r);l.openScope(t);var a=R.getFinalizeCb(e),o=R.getFinalizeData(e),i=R.getContext(e),s=function(){t.callFinalizerInternal(0,a,o,i)};try{if(a)if(p){var u=R.getResource(e),c=l.refStore.get(u).get(),f=l.handleStore.get(c).value,d=new DataView(n.buffer),v=d.getFloat64(e+R.offset.async_id,!0),h=d.getFloat64(e+R.offset.trigger_async_id,!0);p.node.makeCallback(f,s,[],{asyncId:v,triggerAsyncId:h})}else s();R.emptyQueueAndDelete(e)}finally{l.closeScope(t)}},cleanup:function(e){R.closeHandlesAndMaybeDelete(e,1)},closeHandlesAndMaybeDelete:function(e,r){var t=R.getEnv(e),n=l.envStore.get(t);l.openScope(n);try{if(r&&R.getMutex(e).execute(function(){R.setIsClosing(e,1),R.getMaxQueueSize(e)>0&&R.getCond(e).signal()}),R.getHandlesClosing(e))return;R.setHandlesClosing(e,1),l.feature.setImmediate(function(){R.finalize(e)})}finally{l.closeScope(n)}},dispatchOne:function(e){var r=0,t=!1,o=!1,i=R.getMutex(e),s=R.getCond(e);if(i.execute(function(){if(R.getIsClosing(e))R.closeHandlesAndMaybeDelete(e,0);else{var n=R.getQueueSize(e);if(n>0){r=R.shiftQueue(e),t=!0;var a=R.getMaxQueueSize(e);n===a&&a>0&&s.signal(),n--}0===n?0===R.getThreadCount(e)&&(R.setIsClosing(e,1),R.getMaxQueueSize(e)>0&&s.signal(),R.closeHandlesAndMaybeDelete(e,0)):o=!0}}),t){var u=R.getEnv(e),c=l.envStore.get(u);l.openScope(c);var f=function(){c.callbackIntoModule(!1,function(){var t=R.getCallJSCb(e),n=R.getRef(e),o=n?l.refStore.get(n).get():0;if(t){var i=R.getContext(e);a.get(t)(u,o,i,r)}else{var s=o?l.handleStore.get(o).value:null;"function"==typeof s&&s()}})};try{if(p){var d=R.getResource(e),v=l.refStore.get(d).get(),h=l.handleStore.get(v).value,y=new DataView(n.buffer);p.node.makeCallback(h,f,[],{asyncId:y.getFloat64(e+R.offset.async_id,!0),triggerAsyncId:y.getFloat64(e+R.offset.trigger_async_id,!0)})}else f()}finally{l.closeScope(c)}}return o},dispatch:function(e){for(var r=!0,t=1e3,a=new Uint32Array(n.buffer),o=e+R.offset.dispatch_state>>2;r&&0!==--t;)Atomics.store(a,o,1),r=R.dispatchOne(e),1!==Atomics.exchange(a,o,0)&&(r=!0);r&&R.send(e)},send:function(e){1&~Atomics.or(new Uint32Array(n.buffer),e+R.offset.dispatch_state>>2,2)&&(void 0!==u&&u?postMessage({__emnapi__:{type:"tsfn-send",payload:{tsfn:e}}}):l.feature.setImmediate(function(){R.dispatch(e)}))}};var O={unusedWorkers:[],runningWorkers:[],workQueue:[],workerReady:null,offset:{resource:0,async_id:8,trigger_async_id:16,env:24,data:28,execute:32,complete:36,end:40},init:function(){O.unusedWorkers=[],O.runningWorkers=[],O.workQueue=[],O.workerReady=null},addListener:function(e){if(!e)return!1;if(e._emnapiAWMTListener)return!0;var r=function(r){var t=(s?r:r.data).__emnapi__;if(t){var n=t.type,a=t.payload;"async-work-complete"===n?(l.decreaseWaitingRequestCounter(),O.runningWorkers.splice(O.runningWorkers.indexOf(e),1),O.unusedWorkers.push(e),O.checkIdleWorker(),O.callComplete(a.work,0)):"async-work-queue"===n?O.scheduleWork(a.work):"async-work-cancel"===n&&O.cancelWork(a.work)}};return e._emnapiAWMTListener={handler:r,dispose:function(){s?e.off("message",r):e.removeEventListener("message",r,!1),delete e._emnapiAWMTListener}},s?e.on("message",r):e.addEventListener("message",r,!1),!0},initWorkers:function(e){if(u)return O.workerReady||(O.workerReady=Promise.resolve());if(O.workerReady)return O.workerReady;if("function"!=typeof y)throw new TypeError("`options.onCreateWorker` is not a function");var r=[],n=[];if(!("emnapi_async_worker_create"in t.exports))throw new TypeError("`emnapi_async_worker_create` is not exported, please try to add `--export=emnapi_async_worker_create` to linker flags");for(var a=0;a<e;++a)n.push(t.exports.emnapi_async_worker_create());try{var o=function(e){var t=y({type:"async-work",name:"emnapi-async-worker"}),a=S.loadWasmModuleToWorker(t);O.addListener(t),r.push(a.then(function(){"function"==typeof t.unref&&t.unref()})),O.unusedWorkers.push(t);var o=n[e];t.threadBlockBase=o,t.postMessage({__emnapi__:{type:"async-worker-init",payload:{arg:o}}})};for(a=0;a<e;++a)o(a)}catch(r){for(a=0;a<e;++a){var s=n[a];i(s)}throw r}return O.workerReady=Promise.all(r),O.workerReady},checkIdleWorker:function(){if(O.unusedWorkers.length>0&&O.workQueue.length>0){var e=O.unusedWorkers.shift(),r=O.workQueue.shift();O.runningWorkers.push(e),e.postMessage({__emnapi__:{type:"async-work-execute",payload:{work:r}}})}},getResource:function(e){return R.loadSizeTypeValue(e+O.offset.resource,!1)},getExecute:function(e){return R.loadSizeTypeValue(e+O.offset.execute,!1)},getComplete:function(e){return R.loadSizeTypeValue(e+O.offset.complete,!1)},getEnv:function(e){return R.loadSizeTypeValue(e+O.offset.env,!1)},getData:function(e){return R.loadSizeTypeValue(e+O.offset.data,!1)},scheduleWork:function(e){var r;if(u)(0,h.postMessage)({__emnapi__:{type:"async-work-queue",payload:{work:e}}});else if(l.increaseWaitingRequestCounter(),O.workQueue.push(e),null===(r=O.workerReady)||void 0===r?void 0:r.ready)O.checkIdleWorker();else{var t=function(e){throw l.decreaseWaitingRequestCounter(),e};try{O.initWorkers(L()).then(function(){O.workerReady.ready=!0,O.checkIdleWorker()},t)}catch(e){t(e)}}},cancelWork:function(e){if(u)return(0,h.postMessage)({__emnapi__:{type:"async-work-cancel",payload:{work:e}}}),0;var r=O.workQueue.indexOf(e);return-1!==r?(O.workQueue.splice(r,1),l.feature.setImmediate(function(){l.decreaseWaitingRequestCounter(),O.checkIdleWorker(),O.callComplete(e,11)}),0):9},callComplete:function(e,r){var t=O.getComplete(e),o=O.getEnv(e),i=O.getData(e),s=l.envStore.get(o),u=l.openScope(s),c=function(){t&&s.callbackIntoModule(!0,function(){a.get(t)(o,r,i)})};try{if(p){var f=O.getResource(e),d=l.refStore.get(f).get(),v=l.handleStore.get(d).value,h=new DataView(n.buffer),y=h.getFloat64(e+O.offset.async_id,!0),g=h.getFloat64(e+O.offset.trigger_async_id,!0);p.node.makeCallback(v,c,[],{asyncId:y,triggerAsyncId:g})}else c()}finally{l.closeScope(s,u)}}},z=m?function(e,r,t,a,o,i,s){if(!e)return 1;var u,c=l.envStore.get(e);if(c.checkGCAccess(),!a)return c.setLastError(1);if(!s)return c.setLastError(1);if(u=r?Object(l.handleStore.get(r).value):{},!t)return c.setLastError(1);var f=String(l.handleStore.get(t).value),d=x.create(e,u,f,a,o,i);return new DataView(n.buffer).setUint32(s,d,!0),c.clearLastError()}:function(e,r,t,a,i,s,u){if(!e)return 1;var c,f=l.envStore.get(e);if(f.checkGCAccess(),!a)return f.setLastError(1);if(!u)return f.setLastError(1);if(c=r?Object(l.handleStore.get(r).value):{},!t)return f.setLastError(1);var d=O.offset.end,p=o(d);if(!p)return f.setLastError(9);new Uint8Array(n.buffer).subarray(p,p+d).fill(0);var v=f.ensureHandleId(c),h=l.createReference(f,v,1,1).id,y=new DataView(n.buffer);return y.setUint32(p,h,!0),M(v,t,-1,p+O.offset.async_id),y.setUint32(p+O.offset.env,e,!0),y.setUint32(p+O.offset.execute,a,!0),y.setUint32(p+O.offset.complete,i,!0),y.setUint32(p+O.offset.data,s,!0),y.setUint32(u,p,!0),f.clearLastError()},G=m?function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?(x.remove(r),t.clearLastError()):t.setLastError(1)}:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=O.getResource(r);if(l.refStore.get(a).dispose(),p){var o=new DataView(n.buffer);W(o.getFloat64(r+O.offset.async_id,!0),o.getFloat64(r+O.offset.trigger_async_id,!0))}return i(r),t.clearLastError()},F=m?function(e,r){if(!e)return 1;var t=l.envStore.get(e);return r?(x.queue(r),t.clearLastError()):t.setLastError(1)}:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return r?(O.scheduleWork(r),t.clearLastError()):t.setLastError(1)},P=m?function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(!r)return t.setLastError(1);var n=x.cancel(r);return 0===n?t.clearLastError():t.setLastError(n)}:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(!r)return t.setLastError(1);var n=O.cancelWork(r);return 0===n?t.clearLastError():t.setLastError(n)};h.initWorker=function(e){if(!h.childThread)throw new Error("startThread is only available in child threads");if("function"!=typeof t.exports.emnapi_async_worker_init)throw new TypeError("`emnapi_async_worker_init` is not exported, please try to add `--export=emnapi_async_worker_init` to linker flags");t.exports.emnapi_async_worker_init(e)},h.executeAsyncWork=function(e){if(u){var r=O.getExecute(e),t=O.getEnv(e),n=O.getData(e);a.get(r)(t,n),(0,h.postMessage)({__emnapi__:{type:"async-work-complete",payload:{work:e}}})}};var N=Object.freeze({__proto__:null,napi_cancel_async_work:P,napi_create_async_work:z,napi_delete_async_work:G,napi_queue_async_work:F}),J={registry:"function"==typeof FinalizationRegistry?new FinalizationRegistry(function(e){i(e)}):void 0,table:new WeakMap,wasmMemoryViewTable:new WeakMap,init:function(){J.registry="function"==typeof FinalizationRegistry?new FinalizationRegistry(function(e){i(e)}):void 0,J.table=new WeakMap,J.wasmMemoryViewTable=new WeakMap},isDetachedArrayBuffer:function(e){if(0===e.byteLength)try{new Uint8Array(e)}catch(e){return!0}return!1},getArrayBufferPointer:function(e,r){var t,a={address:0,ownership:0,runtimeAllocated:0};if(e===n.buffer)return a;var i=J.isDetachedArrayBuffer(e);if(J.table.has(e)){var s=J.table.get(e);return i?(s.address=0,s):(r&&0===s.ownership&&1===s.runtimeAllocated&&new Uint8Array(n.buffer).set(new Uint8Array(e),s.address),s)}if(i||0===e.byteLength)return a;if(!r)return a;var u=o(e.byteLength);if(!u)throw new Error("Out of memory");return new Uint8Array(n.buffer).set(new Uint8Array(e),u),a.address=u,a.ownership=J.registry?0:1,a.runtimeAllocated=1,J.table.set(e,a),null===(t=J.registry)||void 0===t||t.register(e,u),a},getOrUpdateMemoryView:function(e){if(e.buffer===n.buffer)return J.wasmMemoryViewTable.has(e)||J.wasmMemoryViewTable.set(e,{Ctor:e.constructor,address:e.byteOffset,length:e instanceof DataView?e.byteLength:e.length,ownership:1,runtimeAllocated:0}),e;if((J.isDetachedArrayBuffer(e.buffer)||"function"==typeof SharedArrayBuffer&&e.buffer instanceof SharedArrayBuffer)&&J.wasmMemoryViewTable.has(e)){var r=J.wasmMemoryViewTable.get(e),t=r.Ctor,a=void 0,o=l.feature.Buffer;return a="function"==typeof o&&t===o?o.from(n.buffer,r.address,r.length):new t(n.buffer,r.address,r.length),J.wasmMemoryViewTable.set(a,r),a}return e},getViewPointer:function(e,r){if((e=J.getOrUpdateMemoryView(e)).buffer===n.buffer){if(J.wasmMemoryViewTable.has(e)){var t=J.wasmMemoryViewTable.get(e);return{address:t.address,ownership:t.ownership,runtimeAllocated:t.runtimeAllocated,view:e}}return{address:e.byteOffset,ownership:1,runtimeAllocated:0,view:e}}var a=J.getArrayBufferPointer(e.buffer,r),o=a.address,i=a.ownership,s=a.runtimeAllocated;return{address:0===o?0:o+e.byteOffset,ownership:i,runtimeAllocated:s,view:e}}},q={utf8Decoder:void 0,utf16Decoder:void 0,init:function(){var e,r={decode:function(e){for(var r=0,t=Math.min(4096,e.length+1),n=new Uint16Array(t),a=[],o=0;;){var i=r<e.length;if(!i||o>=t-1){var s=n.subarray(0,o);if(a.push(String.fromCharCode.apply(null,s)),!i)return a.join("");e=e.subarray(r),r=0,o=0}var u=e[r++];if(128&u){if(192==(224&u)){var c=63&e[r++];n[o++]=(31&u)<<6|c}else if(224==(240&u)){c=63&e[r++];var f=63&e[r++];n[o++]=(31&u)<<12|c<<6|f}else if(240==(248&u)){var l=(7&u)<<18|(c=63&e[r++])<<12|(f=63&e[r++])<<6|63&e[r++];l>65535&&(l-=65536,n[o++]=l>>>10&1023|55296,l=56320|1023&l),n[o++]=l}}else n[o++]=u}}};e="function"==typeof TextDecoder?new TextDecoder:r,q.utf8Decoder=e;var t,n={decode:function(e){var r=new Uint16Array(e.buffer,e.byteOffset,e.byteLength/2);if(r.length<=4096)return String.fromCharCode.apply(null,r);for(var t=[],n=0,a=0;n<r.length;n+=a)a=Math.min(4096,r.length-n),t.push(String.fromCharCode.apply(null,r.subarray(n,n+a)));return t.join("")}};t="function"==typeof TextDecoder?new TextDecoder("utf-16le"):n,q.utf16Decoder=t},lengthBytesUTF8:function(e){for(var r,t=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<=127?t++:r<=2047?t+=2:r>=55296&&r<=57343?(t+=4,++n):t+=3;return t},UTF8ToString:function(e,r){if(!e||!r)return"";e>>>=0;var t=new Uint8Array(n.buffer),a=e;if(-1===r)for(;t[a];)++a;else a=e+(r>>>0);if((r=a-e)<=16){for(var o=e,i="";o<a;){var s=t[o++];if(128&s){var u=63&t[o++];if(192!=(224&s)){var c=63&t[o++];if((s=224==(240&s)?(15&s)<<12|u<<6|c:(7&s)<<18|u<<12|c<<6|63&t[o++])<65536)i+=String.fromCharCode(s);else{var f=s-65536;i+=String.fromCharCode(55296|f>>10,56320|1023&f)}}else i+=String.fromCharCode((31&s)<<6|u)}else i+=String.fromCharCode(s)}return i}return q.utf8Decoder.decode("function"==typeof SharedArrayBuffer&&t.buffer instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(t.buffer)?t.slice(e,a):t.subarray(e,a))},stringToUTF8:function(e,r,t){var a=new Uint8Array(n.buffer),o=r;if(!(t>0))return 0;for(var i=o>>>=0,s=o+t-1,u=0;u<e.length;++u){var c=e.charCodeAt(u);if(c>=55296&&c<=57343)c=65536+((1023&c)<<10)|1023&e.charCodeAt(++u);if(c<=127){if(o>=s)break;a[o++]=c}else if(c<=2047){if(o+1>=s)break;a[o++]=192|c>>6,a[o++]=128|63&c}else if(c<=65535){if(o+2>=s)break;a[o++]=224|c>>12,a[o++]=128|c>>6&63,a[o++]=128|63&c}else{if(o+3>=s)break;a[o++]=240|c>>18,a[o++]=128|c>>12&63,a[o++]=128|c>>6&63,a[o++]=128|63&c}}return a[o]=0,o-i},UTF16ToString:function(e,r){if(!e||!r)return"";var t=e>>>=0;if(-1===r){for(var a=t>>1,o=new Uint16Array(n.buffer);o[a];)++a;t=a<<1}else t=e+2*(r>>>0);if((r=t-e)<=32)return String.fromCharCode.apply(null,new Uint16Array(n.buffer,e,r/2));var i=new Uint8Array(n.buffer);return q.utf16Decoder.decode("function"==typeof SharedArrayBuffer&&i.buffer instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(i.buffer)?i.slice(e,t):i.subarray(e,t))},stringToUTF16:function(e,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var a=r,o=(t-=2)<2*e.length?t/2:e.length,i=new DataView(n.buffer),s=0;s<o;++s){var u=e.charCodeAt(s);i.setInt16(r,u,!0),r+=2}return i.setInt16(r,0,!0),r-a},newString:function(e,r,t,a,o){if(!e)return 1;var i=l.envStore.get(e);i.checkGCAccess();var s=-1===t,u=t>>>0;if(0!==t&&!r)return i.setLastError(1);if(!a)return i.setLastError(1);if(!(s||u<=2147483647))return i.setLastError(1);var c=o(r,s,u),f=l.addToCurrentScope(c).id;return new DataView(n.buffer).setUint32(a,f,!0),i.clearLastError()},newExternalString:function(e,r,t,a,o,i,s,u,c){if(!e)return 1;var f=l.envStore.get(e);f.checkGCAccess();var d=-1===t,p=t>>>0;if(0!==t&&!r)return f.setLastError(1);if(!i)return f.setLastError(1);if(!(d||p<=2147483647))return f.setLastError(1);var v=u(e,r,t,i);if(0===v){if(s)new DataView(n.buffer).setInt8(s,1,!0);a&&f.callFinalizer(a,r,o)}return v}};function H(e,r,t,a,o,i,s){if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!r)return u.setLastError(1);var c=l.handleStore.get(r);if(!c.isTypedArray())return u.setLastError(1);var f,d=c.value,p=new DataView(n.buffer);if(t){var v=void 0;if(d instanceof Int8Array)v=0;else if(d instanceof Uint8Array)v=1;else if(d instanceof Uint8ClampedArray)v=2;else if(d instanceof Int16Array)v=3;else if(d instanceof Uint16Array)v=4;else if(d instanceof Int32Array)v=5;else if(d instanceof Uint32Array)v=6;else if(d instanceof Float32Array)v=7;else if(d instanceof Float64Array)v=8;else if(d instanceof BigInt64Array)v=9;else{if(!(d instanceof BigUint64Array))return u.setLastError(9);v=10}p.setInt32(t,v,!0)}if(a&&p.setUint32(a,d.length,!0),o||i){if(f=d.buffer,o){var h=J.getViewPointer(d,!0).address;p.setUint32(o,h,!0)}if(i){var y=u.ensureHandleId(f);p.setUint32(i,y,!0)}}return s&&p.setUint32(s,d.byteOffset,!0),u.clearLastError()}function Q(e,r,t,a,o,i){if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!r)return s.setLastError(1);var u=l.handleStore.get(r);if(!u.isDataView())return s.setLastError(1);var c,f=u.value,d=new DataView(n.buffer);if(t&&d.setUint32(t,f.byteLength,!0),a||o){if(c=f.buffer,a){var p=J.getViewPointer(f,!0).address;d.setUint32(a,p,!0)}if(o){var v=s.ensureHandleId(c);d.setUint32(o,v,!0)}}return i&&d.setUint32(i,f.byteOffset,!0),s.clearLastError()}var $=Object.freeze({__proto__:null,napi_get_array_length:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if(!o.isArray())return a.setLastError(8);var i=o.value.length>>>0;return new DataView(n.buffer).setUint32(t,i,!0),a.getReturnStatus()}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_get_arraybuffer_info:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!r)return o.setLastError(1);var i=l.handleStore.get(r);if(!i.isArrayBuffer())return o.setLastError(1);var s=new DataView(n.buffer);if(t){var u=J.getArrayBufferPointer(i.value,!0).address;s.setUint32(t,u,!0)}return a&&s.setUint32(a,i.value.byteLength,!0),o.clearLastError()},napi_get_buffer_info:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);var o=l.handleStore.get(r);return o.isBuffer(l.feature.Buffer)?o.isDataView()?Q(e,r,n,t,0,0):H(e,r,0,n,t,0,0):a.setLastError(1)},napi_get_dataview_info:Q,napi_get_date_value:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);var i=l.handleStore.get(r);return i.isDate()?(a=i.value.valueOf(),new DataView(n.buffer).setFloat64(t,a,!0),o.getReturnStatus()):o.setLastError(1)}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_get_prototype:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if(null==o.value)throw new TypeError("Cannot convert undefined or null to object");var i=void 0;try{i=o.isObject()||o.isFunction()?o.value:Object(o.value)}catch(e){return a.setLastError(2)}var s=a.ensureHandleId(Object.getPrototypeOf(i));return new DataView(n.buffer).setUint32(t,s,!0),a.getReturnStatus()}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_get_typedarray_info:H,napi_get_value_bigint_int64:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!l.feature.supportBigInt)return o.setLastError(9);if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);if(!a)return o.setLastError(1);var i=l.handleStore.get(r).value;if("bigint"!=typeof i)return o.setLastError(6);var s=new DataView(n.buffer);i>=BigInt(-1)*(BigInt(1)<<BigInt(63))&&i<BigInt(1)<<BigInt(63)?s.setInt8(a,1,!0):(s.setInt8(a,0,!0),(i&=(BigInt(1)<<BigInt(64))-BigInt(1))>=BigInt(1)<<BigInt(63)&&(i-=BigInt(1)<<BigInt(64)));var u=Number(i&BigInt(4294967295)),c=Number(i>>BigInt(32));return s.setInt32(t,u,!0),s.setInt32(t+4,c,!0),o.clearLastError()},napi_get_value_bigint_uint64:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!l.feature.supportBigInt)return o.setLastError(9);if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);if(!a)return o.setLastError(1);var i=l.handleStore.get(r).value;if("bigint"!=typeof i)return o.setLastError(6);var s=new DataView(n.buffer);i>=BigInt(0)&&i<BigInt(1)<<BigInt(64)?s.setInt8(a,1,!0):(s.setInt8(a,0,!0),i&=(BigInt(1)<<BigInt(64))-BigInt(1));var u=Number(i&BigInt(4294967295)),c=Number(i>>BigInt(32));return s.setUint32(t,u,!0),s.setUint32(t+4,c,!0),o.clearLastError()},napi_get_value_bigint_words:function(e,r,t,a,o){if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!l.feature.supportBigInt)return i.setLastError(9);if(!r)return i.setLastError(1);if(!a)return i.setLastError(1);var s=l.handleStore.get(r);if(!s.isBigInt())return i.setLastError(17);for(var u=s.value<BigInt(0),c=new DataView(n.buffer),f=c.getUint32(a,!0),d=0,p=u?s.value*BigInt(-1):s.value;p!==BigInt(0);)d++,p>>=BigInt(64);if(p=u?s.value*BigInt(-1):s.value,t||o){if(!t)return i.setLastError(1);if(!o)return i.setLastError(1);for(var v=[];p!==BigInt(0);){var h=p&(BigInt(1)<<BigInt(64))-BigInt(1);v.push(h),p>>=BigInt(64)}for(var y=Math.min(f,v.length),g=0;g<y;g++){var _=Number(v[g]&BigInt(4294967295)),E=Number(v[g]>>BigInt(32));c.setUint32(o+8*g,_,!0),c.setUint32(o+(8*g+4),E,!0)}c.setInt32(t,u?1:0,!0),c.setUint32(a,y,!0)}else f=d,c.setUint32(a,f,!0);return i.clearLastError()},napi_get_value_bool:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if("boolean"!=typeof o.value)return a.setLastError(7);var i=o.value?1:0;return new DataView(n.buffer).setInt8(t,i,!0),a.clearLastError()},napi_get_value_double:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if("number"!=typeof o.value)return a.setLastError(6);var i=o.value;return new DataView(n.buffer).setFloat64(t,i,!0),a.clearLastError()},napi_get_value_external:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if(!o.isExternal())return a.setLastError(1);var i=o.data();return new DataView(n.buffer).setUint32(t,i,!0),a.clearLastError()},napi_get_value_int32:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if("number"!=typeof o.value)return a.setLastError(6);var i=new Int32Array([o.value])[0];return new DataView(n.buffer).setInt32(t,i,!0),a.clearLastError()},napi_get_value_int64:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if("number"!=typeof o.value)return a.setLastError(6);var i=o.value,s=new DataView(n.buffer);return i===Number.POSITIVE_INFINITY||i===Number.NEGATIVE_INFINITY||isNaN(i)?(s.setInt32(t,0,!0),s.setInt32(t+4,0,!0)):i<-0x8000000000000000?(s.setInt32(t,0,!0),s.setInt32(t+4,2147483648,!0)):i>=0x8000000000000000?(s.setUint32(t,4294967295,!0),s.setUint32(t+4,2147483647,!0)):k(t,Math.trunc(i)),a.clearLastError()},napi_get_value_string_latin1:function(e,r,t,a,o){if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!r)return i.setLastError(1);a>>>=0;var s=l.handleStore.get(r);if("string"!=typeof s.value)return i.setLastError(3);var u=new DataView(n.buffer);if(t)if(0!==a){for(var c=0,f=void 0,d=0;d<a-1;++d)f=255&s.value.charCodeAt(d),u.setUint8(t+d,f,!0),c++;u.setUint8(t+c,0,!0),o&&u.setUint32(o,c,!0)}else o&&u.setUint32(o,0,!0);else{if(!o)return i.setLastError(1);u.setUint32(o,s.value.length,!0)}return i.clearLastError()},napi_get_value_string_utf16:function(e,r,t,a,o){if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!r)return i.setLastError(1);a>>>=0;var s=l.handleStore.get(r);if("string"!=typeof s.value)return i.setLastError(3);var u=new DataView(n.buffer);if(t)if(0!==a){var c=q.stringToUTF16(s.value,t,2*a);o&&u.setUint32(o,c/2,!0)}else o&&u.setUint32(o,0,!0);else{if(!o)return i.setLastError(1);u.setUint32(o,s.value.length,!0)}return i.clearLastError()},napi_get_value_string_utf8:function(e,r,t,a,o){if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!r)return i.setLastError(1);a>>>=0;var s=l.handleStore.get(r);if("string"!=typeof s.value)return i.setLastError(3);var u=new DataView(n.buffer);if(t)if(0!==a){var c=q.stringToUTF8(s.value,t,a);o&&u.setUint32(o,c,!0)}else o&&u.setUint32(o,0,!0);else{if(!o)return i.setLastError(1);var f=q.lengthBytesUTF8(s.value);u.setUint32(o,f,!0)}return i.clearLastError()},napi_get_value_uint32:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if("number"!=typeof o.value)return a.setLastError(6);var i=new Uint32Array([o.value])[0];return new DataView(n.buffer).setUint32(t,i,!0),a.clearLastError()}});function Y(e,r,t,a){return q.newString(e,r,t,a,function(e,r,t){var a="",o=0,i=new DataView(n.buffer);if(r)for(;;){if(!(s=i.getUint8(e,!0)))break;a+=String.fromCharCode(s),e++}else for(;o<t;){var s;if(!(s=i.getUint8(e,!0)))break;a+=String.fromCharCode(s),o++,e++}return a})}function X(e,r,t,n){return q.newString(e,r,t,n,function(e){return q.UTF16ToString(e,t)})}function Z(e,r,t,n){return q.newString(e,r,t,n,function(e){return q.UTF8ToString(e,t)})}var K=Object.freeze({__proto__:null,napi_create_bigint_int64:function(e,r,t,a){if(!e)return 1;var o,i=l.envStore.get(e);if(i.checkGCAccess(),!l.feature.supportBigInt)return i.setLastError(9);if(!t)return i.setLastError(1);o=r;var s=l.addToCurrentScope(o).id;return new DataView(n.buffer).setUint32(t,s,!0),i.clearLastError()},napi_create_bigint_uint64:function(e,r,t,a){if(!e)return 1;var o,i=l.envStore.get(e);if(i.checkGCAccess(),!l.feature.supportBigInt)return i.setLastError(9);if(!t)return i.setLastError(1);o=r&(BigInt(1)<<BigInt(64))-BigInt(1);var s=l.addToCurrentScope(o).id;return new DataView(n.buffer).setUint32(t,s,!0),i.clearLastError()},napi_create_bigint_words:function(e,r,t,a,o){var i,s;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!l.feature.supportBigInt)return u.setLastError(9);if(!o)return u.setLastError(1);if((t>>>=0)>2147483647)return u.setLastError(1);if(t>16384)throw new RangeError("Maximum BigInt size exceeded");var c=BigInt(0),f=new DataView(n.buffer);for(s=0;s<t;s++){var d=f.getUint32(a+8*s,!0),p=f.getUint32(a+(8*s+4),!0);c+=(BigInt(d)|BigInt(p)<<BigInt(32))<<BigInt(64*s)}return c*=BigInt(r)%BigInt(2)===BigInt(0)?BigInt(1):BigInt(-1),i=l.addToCurrentScope(c).id,f.setUint32(o,i,!0),u.getReturnStatus()}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}},napi_create_double:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);var o=l.addToCurrentScope(r).id;return new DataView(n.buffer).setUint32(t,o,!0),a.clearLastError()},napi_create_int32:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);var o=l.addToCurrentScope(r).id;return new DataView(n.buffer).setUint32(t,o,!0),a.clearLastError()},napi_create_int64:function(e,r,t,a){if(!e)return 1;var o,i=l.envStore.get(e);if(i.checkGCAccess(),!t)return i.setLastError(1);o=Number(r);var s=l.addToCurrentScope(o).id;return new DataView(n.buffer).setUint32(t,s,!0),i.clearLastError()},napi_create_string_latin1:Y,napi_create_string_utf16:X,napi_create_string_utf8:Z,napi_create_uint32:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);var o=l.addToCurrentScope(r>>>0).id;return new DataView(n.buffer).setUint32(t,o,!0),a.clearLastError()},node_api_create_external_string_latin1:function(e,r,t,n,a,o,i){return q.newExternalString(e,r,t,n,a,o,i,Y,void 0)},node_api_create_external_string_utf16:function(e,r,t,n,a,o,i){return q.newExternalString(e,r,t,n,a,o,i,X,void 0)},node_api_create_property_key_latin1:function(e,r,t,n){return Y(e,r,t,n)},node_api_create_property_key_utf16:function(e,r,t,n){return X(e,r,t,n)},node_api_create_property_key_utf8:function(e,r,t,n){return Z(e,r,t,n)}});function ee(e,r,t,n,o){var i,s=r&&t?q.UTF8ToString(r,t):"",u=a.get(n),c=function(e){return u(e.id,e.ctx.scopeStore.currentScope.id)},f=function(e,r){return function(){var t=e.ctx.openScope(e),n=t.callbackInfo;n.data=o,n.args=arguments,n.thiz=this,n.fn=i;try{var a=e.callIntoModule(r);return a?e.ctx.handleStore.get(a).value:void 0}finally{n.data=0,n.args=void 0,n.thiz=void 0,n.fn=void 0,e.ctx.closeScope(e,t)}}};if(""===s)return{status:0,f:i=f(e,c)};if(!/^[_$a-zA-Z][_$a-zA-Z0-9]*$/.test(s))return{status:1,f:void 0};if(l.feature.supportNewFunction){var d=f(e,c);try{i=new Function("_","return function "+s+'(){"use strict";return _.apply(this,arguments);};')(d)}catch(r){i=f(e,c),l.feature.canSetFunctionName&&Object.defineProperty(i,"name",{value:s})}}else i=f(e,c),l.feature.canSetFunctionName&&Object.defineProperty(i,"name",{value:s});return{status:0,f:i}}function re(e,r,t,n,a,o,i,s,u){if(a||o){var c=void 0,f=void 0;a&&(c=ee(e,0,0,a,u).f),o&&(f=ee(e,0,0,o,u).f);var d={configurable:!!(4&s),enumerable:!!(2&s),get:c,set:f};Object.defineProperty(r,t,d)}else if(n){d={configurable:!!(4&s),enumerable:!!(2&s),writable:!!(1&s),value:ee(e,0,0,n,u).f};Object.defineProperty(r,t,d)}else{d={configurable:!!(4&s),enumerable:!!(2&s),writable:!!(1&s),value:l.handleStore.get(i).value};Object.defineProperty(r,t,d)}}function te(e){var r=l.handleStore.get(e);return r.isObject()||r.isFunction()?(void 0!==J&&ArrayBuffer.isView(r.value)&&J.wasmMemoryViewTable.has(r.value)&&(r=l.addToCurrentScope(J.wasmMemoryViewTable.get(r.value))),{status:0,handle:r}):{status:1}}function ne(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!r)return i.setLastError(1);if(0===a&&!t)return i.setLastError(1);var s=l.handleStore.get(r);if(!s.isObject()&&!s.isFunction())return i.setLastError(1);var u=i.getObjectBinding(s.value),c=u.wrapped,f=l.refStore.get(c);if(!f)return i.setLastError(1);if(t)o=f.data(),new DataView(n.buffer).setUint32(t,o,!0);return 1===a&&(u.wrapped=0,1===f.ownership()?f.resetFinalizer():f.dispose()),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}}function ae(e,r,t,a,o,i){if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!l.feature.supportFinalizer)return s.setLastError(9);if(!r)return s.setLastError(1);if(!a)return s.setLastError(1);var u=te(r);if(0!==u.status)return s.setLastError(u.status);var c=u.handle,f=i?1:0,d=l.createReferenceWithFinalizer(s,c.id,0,f,a,t,o);if(i){var p=d.id;new DataView(n.buffer).setUint32(i,p,!0)}return s.clearLastError()}var oe=Object.freeze({__proto__:null,napi_add_finalizer:ae,napi_check_object_type_tag:function(e,r,t,a){var o=!0;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!r)return i.setLastError(i.tryCatch.hasCaught()?10:1);var s=l.handleStore.get(r);if(!s.isObject()&&!s.isFunction())return i.setLastError(i.tryCatch.hasCaught()?10:2);if(!t)return i.setLastError(i.tryCatch.hasCaught()?10:1);if(!a)return i.setLastError(i.tryCatch.hasCaught()?10:1);var u=i.getObjectBinding(s.value);if(null!==u.tag){var c=u.tag,f=new Uint32Array(n.buffer,t,4);o=c[0]===f[0]&&c[1]===f[1]&&c[2]===f[2]&&c[3]===f[3]}else o=!1;return new DataView(n.buffer).setInt8(a,o?1:0,!0),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_define_class:function(e,r,t,a,o,i,s,u){var c,f,d;if(!e)return 1;var p=l.envStore.get(e);if(p.checkGCAccess(),!p.tryCatch.isEmpty())return p.setLastError(10);if(!p.canCallIntoJs())return p.setLastError(p.moduleApiVersion>=10?23:10);p.clearLastError();try{if(!u)return p.setLastError(1);if(!a)return p.setLastError(1);if((i>>>=0)>0&&!s)return p.setLastError(1);if(t<-1||t>2147483647||!r)return p.setLastError(1);var v=ee(p,r,t,a,o);if(0!==v.status)return p.setLastError(v.status);for(var h=v.f,y=void 0,g=new DataView(n.buffer),_=0;_<i;_++){c=s+32*_;var E=g.getUint32(c,!0),w=g.getUint32(c+4,!0),m=g.getUint32(c+8,!0),L=g.getUint32(c+12,!0),b=g.getUint32(c+16,!0),S=g.getUint32(c+20,!0);d=g.getInt32(c+24,!0);var C=g.getUint32(c+28,!0);if(E)y=q.UTF8ToString(E,-1);else{if(!w)return p.setLastError(4);if("string"!=typeof(y=l.handleStore.get(w).value)&&"symbol"!=typeof y)return p.setLastError(4)}1024&d?re(p,h,y,m,L,b,S,d,C):re(p,h.prototype,y,m,L,b,S,d,C)}return f=l.addToCurrentScope(h).id,g.setUint32(u,f,!0),p.getReturnStatus()}catch(e){return p.tryCatch.setError(e),p.setLastError(10)}},napi_remove_wrap:function(e,r,t){return ne(e,r,t,1)},napi_type_tag_object:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!r)return a.setLastError(a.tryCatch.hasCaught()?10:1);var o=l.handleStore.get(r);if(!o.isObject()&&!o.isFunction())return a.setLastError(a.tryCatch.hasCaught()?10:2);if(!t)return a.setLastError(a.tryCatch.hasCaught()?10:1);var i=a.getObjectBinding(o.value);if(null!==i.tag)return a.setLastError(a.tryCatch.hasCaught()?10:1);var s=new Uint8Array(16);return s.set(new Uint8Array(n.buffer,t,16)),i.tag=new Uint32Array(s.buffer),a.getReturnStatus()}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_unwrap:function(e,r,t){return ne(e,r,t,0)},napi_wrap:function(e,r,t,a,o,i){return function(e,r,t,a,o,i){var s;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!l.feature.supportFinalizer){if(a)throw l.createNotSupportWeakRefError("napi_wrap",'Parameter "finalize_cb" must be 0(NULL)');if(i)throw l.createNotSupportWeakRefError("napi_wrap",'Parameter "result" must be 0(NULL)')}if(!r)return u.setLastError(1);var c=te(r);if(0!==c.status)return u.setLastError(c.status);var f=c.handle;if(0!==u.getObjectBinding(f.value).wrapped)return u.setLastError(1);var d=void 0;if(i){if(!a)return u.setLastError(1);s=(d=l.createReferenceWithFinalizer(u,f.id,0,1,a,t,o)).id,new DataView(n.buffer).setUint32(i,s,!0)}else d=a?l.createReferenceWithFinalizer(u,f.id,0,0,a,t,o):l.createReferenceWithData(u,f.id,0,0,t);return u.getObjectBinding(f.value).wrapped=d.id,u.getReturnStatus()}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}}(e,r,t,a,o,i)},node_api_post_finalizer:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);return a.enqueueFinalizer(l.createTrackedFinalizer(a,r,t,n)),a.clearLastError()}});function ie(e,r,t,a,o,i,s){var u;if(!e)return 1;var c=l.envStore.get(e);if(c.checkGCAccess(),!c.tryCatch.isEmpty())return c.setLastError(10);if(!c.canCallIntoJs())return c.setLastError(c.moduleApiVersion>=10?23:10);c.clearLastError();try{if(!s)return c.setLastError(1);if(a>>>=0,t||(a=0),a>2147483647)throw new RangeError("Cannot create a memory view larger than 2147483647 bytes");if(t+a>n.buffer.byteLength)throw new RangeError("Memory out of range");if(!l.feature.supportFinalizer&&o)throw l.createNotSupportWeakRefError("emnapi_create_memory_view",'Parameter "finalize_cb" must be 0(NULL)');var f=void 0;switch(r){case 0:f={Ctor:Int8Array,address:t,length:a,ownership:1,runtimeAllocated:0};break;case 1:f={Ctor:Uint8Array,address:t,length:a,ownership:1,runtimeAllocated:0};break;case 2:f={Ctor:Uint8ClampedArray,address:t,length:a,ownership:1,runtimeAllocated:0};break;case 3:f={Ctor:Int16Array,address:t,length:a>>1,ownership:1,runtimeAllocated:0};break;case 4:f={Ctor:Uint16Array,address:t,length:a>>1,ownership:1,runtimeAllocated:0};break;case 5:f={Ctor:Int32Array,address:t,length:a>>2,ownership:1,runtimeAllocated:0};break;case 6:f={Ctor:Uint32Array,address:t,length:a>>2,ownership:1,runtimeAllocated:0};break;case 7:f={Ctor:Float32Array,address:t,length:a>>2,ownership:1,runtimeAllocated:0};break;case 8:f={Ctor:Float64Array,address:t,length:a>>3,ownership:1,runtimeAllocated:0};break;case 9:f={Ctor:BigInt64Array,address:t,length:a>>3,ownership:1,runtimeAllocated:0};break;case 10:f={Ctor:BigUint64Array,address:t,length:a>>3,ownership:1,runtimeAllocated:0};break;case-1:f={Ctor:DataView,address:t,length:a,ownership:1,runtimeAllocated:0};break;case-2:if(!l.feature.Buffer)throw l.createNotSupportBufferError("emnapi_create_memory_view","");f={Ctor:l.feature.Buffer,address:t,length:a,ownership:1,runtimeAllocated:0};break;default:return c.setLastError(1)}var d=f.Ctor,p=-2===r?l.feature.Buffer.from(n.buffer,f.address,f.length):new d(n.buffer,f.address,f.length),v=l.addToCurrentScope(p);if(J.wasmMemoryViewTable.set(p,f),o){var h=ae(e,v.id,t,o,i,0);if(10===h){var y=c.tryCatch.extractException();throw c.clearLastError(),y}if(0!==h)return c.setLastError(h)}return u=v.id,new DataView(n.buffer).setUint32(s,u,!0),c.getReturnStatus()}catch(y){return c.tryCatch.setError(y),c.setLastError(10)}}function se(e,r,t,a){var o;if(t=null!=t?t:0,t>>>=0,r instanceof ArrayBuffer){if(!(s=J.getArrayBufferPointer(r,!1).address))throw new Error("Unknown ArrayBuffer address");if("number"==typeof a&&-1!==a||(a=r.byteLength-t),0===(a>>>=0))return r;o=new Uint8Array(r,t,a);var i=new Uint8Array(n.buffer);return e?i.set(o,s):o.set(i.subarray(s,s+a)),r}if(ArrayBuffer.isView(r)){var s,u=J.getViewPointer(r,!1),c=u.view;if(!(s=u.address))throw new Error("Unknown ArrayBuffer address");if("number"==typeof a&&-1!==a||(a=c.byteLength-t),0===(a>>>=0))return c;o=new Uint8Array(c.buffer,c.byteOffset+t,a);i=new Uint8Array(n.buffer);return e?i.set(o,s):o.set(i.subarray(s,s+a)),c}throw new TypeError("emnapiSyncMemory expect ArrayBuffer or ArrayBufferView as first parameter")}function ue(e){var r,t=e instanceof ArrayBuffer,n=e instanceof DataView,a=ArrayBuffer.isView(e)&&!n;if(!t&&!a&&!n)throw new TypeError("emnapiGetMemoryAddress expect ArrayBuffer or ArrayBufferView as first parameter");return{address:(r=t?J.getArrayBufferPointer(e,!1):J.getViewPointer(e,!1)).address,ownership:r.ownership,runtimeAllocated:r.runtimeAllocated}}var ce=Object.freeze({__proto__:null,$emnapiGetMemoryAddress:ue,$emnapiSyncMemory:se,emnapi_create_memory_view:ie,emnapi_get_memory_address:function(e,r,t,a,o){var i,s,u,c;if(!e)return 1;var f=l.envStore.get(e);if(f.checkGCAccess(),!f.tryCatch.isEmpty())return f.setLastError(10);if(!f.canCallIntoJs())return f.setLastError(f.moduleApiVersion>=10?23:10);f.clearLastError();try{if(!r)return f.setLastError(1);if(!t&&!a&&!o)return f.setLastError(1);i=(c=ue(f.ctx.handleStore.get(r).value)).address;var d=new DataView(n.buffer);return t&&d.setUint32(t,i,!0),a&&(u=c.ownership,d.setInt32(a,u,!0)),o&&(s=c.runtimeAllocated,d.setInt8(o,s,!0)),f.getReturnStatus()}catch(e){return f.tryCatch.setError(e),f.setLastError(10)}},emnapi_get_runtime_version:function(e,r){if(!e)return 1;var t,a=l.envStore.get(e);if(!r)return a.setLastError(1);try{t=l.getRuntimeVersions().version}catch(e){return a.setLastError(9)}var o=t.split(".").map(function(e){return Number(e)}),i=new DataView(n.buffer);return i.setUint32(r,o[0],!0),i.setUint32(r+4,o[1],!0),i.setUint32(r+8,o[2],!0),a.clearLastError()},emnapi_is_node_binding_available:function(){return p?1:0},emnapi_is_support_bigint:function(){return l.feature.supportBigInt?1:0},emnapi_is_support_weakref:function(){return l.feature.supportFinalizer?1:0},emnapi_sync_memory:function(e,r,t,a,o){var i;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!t)return s.setLastError(1);var u=new DataView(n.buffer),c=u.getUint32(t,!0),f=s.ctx.handleStore.get(c);if(!f.isArrayBuffer()&&!f.isTypedArray()&&!f.isDataView())return s.setLastError(1);var d=se(Boolean(r),f.value,a,o);return f.value!==d&&(i=s.ensureHandleId(d),u.setUint32(t,i,!0)),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}}});function fe(e,r){e>>>=0;var t=new ArrayBuffer(e);if(r){var a=J.getArrayBufferPointer(t,!0).address;new DataView(n.buffer).setUint32(r,a,!0)}return t}var le=Object.freeze({__proto__:null,napi_create_array:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=l.addToCurrentScope([]).id;return new DataView(n.buffer).setUint32(r,a,!0),t.clearLastError()},napi_create_array_with_length:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);r>>>=0;var o=l.addToCurrentScope(new Array(r)).id;return new DataView(n.buffer).setUint32(t,o,!0),a.clearLastError()},napi_create_arraybuffer:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!a)return i.setLastError(1);var s=fe(r,t);return o=l.addToCurrentScope(s).id,new DataView(n.buffer).setUint32(a,o,!0),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_create_buffer:function(e,r,t,a){var i,s,u;if(!e)return 1;var c=l.envStore.get(e);if(c.checkGCAccess(),!c.tryCatch.isEmpty())return c.setLastError(10);if(!c.canCallIntoJs())return c.setLastError(c.moduleApiVersion>=10?23:10);c.clearLastError();try{if(!a)return c.setLastError(1);var f=l.feature.Buffer;if(!f)throw l.createNotSupportBufferError("napi_create_buffer","");var d=void 0;r>>>=0;var p=new DataView(n.buffer);if(t&&0!==r){if(!(u=o(r)))throw new Error("Out of memory");new Uint8Array(n.buffer).subarray(u,u+r).fill(0);var v=f.from(n.buffer,u,r),h={Ctor:f,address:u,length:r,ownership:J.registry?0:1,runtimeAllocated:1};J.wasmMemoryViewTable.set(v,h),null===(i=J.registry)||void 0===i||i.register(h,u),s=l.addToCurrentScope(v).id,p.setUint32(a,s,!0),p.setUint32(t,u,!0)}else d=f.alloc(r),s=l.addToCurrentScope(d).id,p.setUint32(a,s,!0);return c.getReturnStatus()}catch(e){return c.tryCatch.setError(e),c.setLastError(10)}},napi_create_buffer_copy:function(e,r,t,a,o){var i;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!o)return s.setLastError(1);var u=l.feature.Buffer;if(!u)throw l.createNotSupportBufferError("napi_create_buffer_copy","");var c=fe(r,a),f=u.from(c);return f.set(new Uint8Array(n.buffer).subarray(t,t+r)),i=l.addToCurrentScope(f).id,new DataView(n.buffer).setUint32(o,i,!0),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_create_dataview:function(e,r,t,a,o){var i;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!t)return s.setLastError(1);if(!o)return s.setLastError(1);r>>>=0,a>>>=0;var u=l.handleStore.get(t);if(!u.isArrayBuffer())return s.setLastError(1);var c=u.value;if(r+a>c.byteLength){var f=new RangeError("byte_offset + byte_length should be less than or equal to the size in bytes of the array passed in");throw f.code="ERR_NAPI_INVALID_DATAVIEW_ARGS",f}var d=new DataView(c,a,r);return c===n.buffer&&(J.wasmMemoryViewTable.has(d)||J.wasmMemoryViewTable.set(d,{Ctor:DataView,address:a,length:r,ownership:1,runtimeAllocated:0})),i=l.addToCurrentScope(d).id,new DataView(n.buffer).setUint32(o,i,!0),s.getReturnStatus()}catch(f){return s.tryCatch.setError(f),s.setLastError(10)}},napi_create_date:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{return t?(a=l.addToCurrentScope(new Date(r)).id,new DataView(n.buffer).setUint32(t,a,!0),o.getReturnStatus()):o.setLastError(1)}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_create_external:function(e,r,t,a,o){var i;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!o)return s.setLastError(1);if(!l.feature.supportFinalizer&&t)throw l.createNotSupportWeakRefError("napi_create_external",'Parameter "finalize_cb" must be 0(NULL)');var u=l.getCurrentScope().addExternal(r);return t&&l.createReferenceWithFinalizer(s,u.id,0,0,t,r,a),i=u.id,new DataView(n.buffer).setUint32(o,i,!0),s.clearLastError()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_create_external_arraybuffer:function(e,r,t,a,o,i){var s;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!i)return u.setLastError(1);if(t>>>=0,r||(t=0),r+t>n.buffer.byteLength)throw new RangeError("Memory out of range");if(!l.feature.supportFinalizer&&a)throw l.createNotSupportWeakRefError("napi_create_external_arraybuffer",'Parameter "finalize_cb" must be 0(NULL)');var c=new ArrayBuffer(t);if(0===t)try{(new(0,l.feature.MessageChannel)).port1.postMessage(c,[c])}catch(e){}else new Uint8Array(c).set(new Uint8Array(n.buffer).subarray(r,r+t)),J.table.set(c,{address:r,ownership:1,runtimeAllocated:0});var f=l.addToCurrentScope(c);if(a){var d=ae(e,f.id,r,a,o,0);if(10===d){var p=u.tryCatch.extractException();throw u.clearLastError(),p}if(0!==d)return u.setLastError(d)}return s=f.id,new DataView(n.buffer).setUint32(i,s,!0),u.getReturnStatus()}catch(p){return u.tryCatch.setError(p),u.setLastError(10)}},napi_create_external_buffer:function(e,r,t,n,a,o){return ie(e,-2,t,r,n,a,o)},napi_create_object:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=l.addToCurrentScope({}).id;return new DataView(n.buffer).setUint32(r,a,!0),t.clearLastError()},napi_create_symbol:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);var o=new DataView(n.buffer);if(r){var i=l.handleStore.get(r).value;if("string"!=typeof i)return a.setLastError(3);var s=l.addToCurrentScope(Symbol(i)).id;o.setUint32(t,s,!0)}else{var u=l.addToCurrentScope(Symbol()).id;o.setUint32(t,u,!0)}return a.clearLastError()},napi_create_typedarray:function(e,r,t,a,o,i){var s;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!a)return u.setLastError(1);if(!i)return u.setLastError(1);var c=l.handleStore.get(a);if(!c.isArrayBuffer())return u.setLastError(1);var f=c.value,d=function(e,r,t,a,o,u){var c,f;if((o>>>=0,u>>>=0,t>1)&&o%t!==0)return(f=new RangeError("start offset of ".concat(null!==(c=r.name)&&void 0!==c?c:""," should be a multiple of ").concat(t))).code="ERR_NAPI_INVALID_TYPEDARRAY_ALIGNMENT",e.tryCatch.setError(f),e.setLastError(9);if(u*t+o>a.byteLength)return(f=new RangeError("Invalid typed array length")).code="ERR_NAPI_INVALID_TYPEDARRAY_LENGTH",e.tryCatch.setError(f),e.setLastError(9);var d=new r(a,o,u);return a===n.buffer&&(J.wasmMemoryViewTable.has(d)||J.wasmMemoryViewTable.set(d,{Ctor:r,address:o,length:u,ownership:1,runtimeAllocated:0})),s=l.addToCurrentScope(d).id,new DataView(n.buffer).setUint32(i,s,!0),e.getReturnStatus()};switch(r){case 0:return d(u,Int8Array,1,f,o,t);case 1:return d(u,Uint8Array,1,f,o,t);case 2:return d(u,Uint8ClampedArray,1,f,o,t);case 3:return d(u,Int16Array,2,f,o,t);case 4:return d(u,Uint16Array,2,f,o,t);case 5:return d(u,Int32Array,4,f,o,t);case 6:return d(u,Uint32Array,4,f,o,t);case 7:return d(u,Float32Array,4,f,o,t);case 8:return d(u,Float64Array,8,f,o,t);case 9:return d(u,BigInt64Array,8,f,o,t);case 10:return d(u,BigUint64Array,8,f,o,t);default:return u.setLastError(1)}}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}},node_api_create_buffer_from_arraybuffer:function(e,r,t,a,o){var i;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!r)return s.setLastError(1);if(!o)return s.setLastError(1);t>>>=0,a>>>=0;var u=l.handleStore.get(r);if(!u.isArrayBuffer())return s.setLastError(1);var c=u.value;if(a+t>c.byteLength){var f=new RangeError("The byte offset + length is out of range");throw f.code="ERR_OUT_OF_RANGE",f}var d=l.feature.Buffer;if(!d)throw l.createNotSupportBufferError("node_api_create_buffer_from_arraybuffer","");var p=d.from(c,t,a);return c===n.buffer&&(J.wasmMemoryViewTable.has(p)||J.wasmMemoryViewTable.set(p,{Ctor:d,address:t,length:a,ownership:1,runtimeAllocated:0})),i=l.addToCurrentScope(p).id,new DataView(n.buffer).setUint32(o,i,!0),s.getReturnStatus()}catch(f){return s.tryCatch.setError(f),s.setLastError(10)}},node_api_symbol_for:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!a)return o.setLastError(1);var i=-1===t,s=t>>>0;if(0!==t&&!r)return o.setLastError(1);if(!(i||s<=2147483647))return o.setLastError(1);var u=q.UTF8ToString(r,t),c=l.addToCurrentScope(Symbol.for(u)).id;return new DataView(n.buffer).setUint32(a,c,!0),o.clearLastError()}});var de=Object.freeze({__proto__:null,napi_get_boolean:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);var o=0===r?3:4;return new DataView(n.buffer).setUint32(t,o,!0),a.clearLastError()},napi_get_global:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?(new DataView(n.buffer).setUint32(r,5,!0),t.clearLastError()):t.setLastError(1)},napi_get_null:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?(new DataView(n.buffer).setUint32(r,2,!0),t.clearLastError()):t.setLastError(1)},napi_get_undefined:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?(new DataView(n.buffer).setUint32(r,1,!0),t.clearLastError()):t.setLastError(1)}});var pe=Object.freeze({__proto__:null,napi_get_instance_data:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(!r)return t.setLastError(1);var a=t.getInstanceData();return new DataView(n.buffer).setUint32(r,a,!0),t.clearLastError()},napi_set_instance_data:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);return a.setInstanceData(r,t,n),a.clearLastError()}});var ve=Object.freeze({__proto__:null,_emnapi_get_last_error_info:function(e,r,t,a){var o=l.envStore.get(e).lastError,i=o.errorCode,s=o.engineErrorCode>>>0,u=o.engineReserved,c=new DataView(n.buffer);c.setInt32(r,i,!0),c.setUint32(t,s,!0),c.setUint32(a,u,!0)},napi_create_error:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!a)return o.setLastError(1);var i=l.handleStore.get(t).value;if("string"!=typeof i)return o.setLastError(3);var s=new Error(i);if(r){var u=l.handleStore.get(r).value;if("string"!=typeof u)return o.setLastError(3);s.code=u}var c=l.addToCurrentScope(s).id;return new DataView(n.buffer).setUint32(a,c,!0),o.clearLastError()},napi_create_range_error:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!a)return o.setLastError(1);var i=l.handleStore.get(t).value;if("string"!=typeof i)return o.setLastError(3);var s=new RangeError(i);if(r){var u=l.handleStore.get(r).value;if("string"!=typeof u)return o.setLastError(3);s.code=u}var c=l.addToCurrentScope(s).id;return new DataView(n.buffer).setUint32(a,c,!0),o.clearLastError()},napi_create_type_error:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!a)return o.setLastError(1);var i=l.handleStore.get(t).value;if("string"!=typeof i)return o.setLastError(3);var s=new TypeError(i);if(r){var u=l.handleStore.get(r).value;if("string"!=typeof u)return o.setLastError(3);s.code=u}var c=l.addToCurrentScope(s).id;return new DataView(n.buffer).setUint32(a,c,!0),o.clearLastError()},napi_fatal_error:function(e,r,t,n){var a=q.UTF8ToString(e,r),o=q.UTF8ToString(t,n);p?p.napi.fatalError(a,o):f("FATAL ERROR: "+a+" "+o)},napi_fatal_exception:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{if(!r)return t.setLastError(1);var n=t.ctx.handleStore.get(r);try{t.triggerFatalException(n.value)}catch(e){return t.setLastError(9)}return t.clearLastError()}catch(r){return t.tryCatch.setError(r),t.setLastError(10)}},napi_get_and_clear_last_exception:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=new DataView(n.buffer);if(!t.tryCatch.hasCaught())return a.setUint32(r,1,!0),t.clearLastError();var o=t.tryCatch.exception(),i=t.ensureHandleId(o);return a.setUint32(r,i,!0),t.tryCatch.reset(),t.clearLastError()},napi_is_exception_pending:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=t.tryCatch.hasCaught();return new DataView(n.buffer).setInt8(r,a?1:0,!0),t.clearLastError()},napi_throw:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{return r?(t.tryCatch.setError(l.handleStore.get(r).value),t.clearLastError()):t.setLastError(1)}catch(e){return t.tryCatch.setError(e),t.setLastError(10)}},napi_throw_error:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new Error(q.UTF8ToString(t,-1));return r&&(a.code=q.UTF8ToString(r,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}},napi_throw_range_error:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new RangeError(q.UTF8ToString(t,-1));return r&&(a.code=q.UTF8ToString(r,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}},napi_throw_type_error:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new TypeError(q.UTF8ToString(t,-1));return r&&(a.code=q.UTF8ToString(r,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}},node_api_create_syntax_error:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!a)return o.setLastError(1);var i=l.handleStore.get(t).value;if("string"!=typeof i)return o.setLastError(3);var s=new SyntaxError(i);if(r){var u=l.handleStore.get(r).value;if("string"!=typeof u)return o.setLastError(3);s.code=u}var c=l.addToCurrentScope(s).id;return new DataView(n.buffer).setUint32(a,c,!0),o.clearLastError()},node_api_throw_syntax_error:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new SyntaxError(q.UTF8ToString(t,-1));return r&&(a.code=q.UTF8ToString(r,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}}});var he=Object.freeze({__proto__:null,napi_call_function:function(e,r,t,a,o,i){var s,u=0;if(!e)return 1;var c=l.envStore.get(e);if(c.checkGCAccess(),!c.tryCatch.isEmpty())return c.setLastError(10);if(!c.canCallIntoJs())return c.setLastError(c.moduleApiVersion>=10?23:10);c.clearLastError();try{if(!r)return c.setLastError(1);if((a>>>=0)>0&&!o)return c.setLastError(1);var f=l.handleStore.get(r).value;if(!t)return c.setLastError(1);var d=l.handleStore.get(t).value;if("function"!=typeof d)return c.setLastError(1);for(var p=[],v=new DataView(n.buffer);u<a;u++){var h=v.getUint32(o+4*u,!0);p.push(l.handleStore.get(h).value)}var y=d.apply(f,p);return i&&(s=c.ensureHandleId(y),v.setUint32(i,s,!0)),c.clearLastError()}catch(e){return c.tryCatch.setError(e),c.setLastError(10)}},napi_create_function:function(e,r,t,a,o,i){var s;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!i)return u.setLastError(1);if(!a)return u.setLastError(1);var c=ee(u,r,t,a,o);if(0!==c.status)return u.setLastError(c.status);var f=c.f;return s=l.addToCurrentScope(f).id,new DataView(n.buffer).setUint32(i,s,!0),u.getReturnStatus()}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}},napi_get_cb_info:function(e,r,t,a,o,i){if(!e)return 1;var s=l.envStore.get(e);if(!r)return s.setLastError(1);var u=l.scopeStore.get(r).callbackInfo,c=new DataView(n.buffer);if(a){if(!t)return s.setLastError(1);for(var f=c.getUint32(t,!0),d=u.args.length,p=f<d?f:d,v=0;v<p;v++){var h=s.ensureHandleId(u.args[v]);c.setUint32(a+4*v,h,!0)}if(v<f)for(;v<f;v++)c.setUint32(a+4*v,1,!0)}if(t&&c.setUint32(t,u.args.length,!0),o){var y=s.ensureHandleId(u.thiz);c.setUint32(o,y,!0)}return i&&c.setUint32(i,u.data,!0),s.clearLastError()},napi_get_new_target:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.scopeStore.get(r).callbackInfo,i=o.thiz,s=o.fn,u=null==i||null==i.constructor?0:i instanceof s?a.ensureHandleId(i.constructor):0;return new DataView(n.buffer).setUint32(t,u,!0),a.clearLastError()},napi_new_instance:function(e,r,t,a,o){var i,s;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!r)return u.setLastError(1);if((t>>>=0)>0&&!a)return u.setLastError(1);if(!o)return u.setLastError(1);var c=l.handleStore.get(r).value;if("function"!=typeof c)return u.setLastError(1);var f=void 0,d=new DataView(n.buffer);if(l.feature.supportReflect){var p=Array(t);for(i=0;i<t;i++){var v=d.getUint32(a+4*i,!0);p[i]=l.handleStore.get(v).value}f=Reflect.construct(c,p,c)}else{var h=Array(t+1);for(h[0]=void 0,i=0;i<t;i++){v=d.getUint32(a+4*i,!0);h[i+1]=l.handleStore.get(v).value}f=new(c.bind.apply(c,h))}return o&&(s=u.ensureHandleId(f),d.setUint32(o,s,!0)),u.getReturnStatus()}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}}});var ye=Object.freeze({__proto__:null,_emnapi_env_ref:function(e){l.envStore.get(e).ref()},_emnapi_env_unref:function(e){l.envStore.get(e).unref()},napi_add_env_cleanup_hook:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);return r?(l.addCleanupHook(n,r,t),0):n.setLastError(1)},napi_close_escapable_handle_scope:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?0===t.openHandleScopes?13:(l.closeScope(t),t.clearLastError()):t.setLastError(1)},napi_close_handle_scope:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?0===t.openHandleScopes?13:(l.closeScope(t),t.clearLastError()):t.setLastError(1)},napi_create_reference:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!r)return o.setLastError(1);if(!a)return o.setLastError(1);var i=l.handleStore.get(r);if(o.moduleApiVersion<10&&!(i.isObject()||i.isFunction()||i.isSymbol()))return o.setLastError(1);var s=l.createReference(o,i.id,t>>>0,1);return new DataView(n.buffer).setUint32(a,s.id,!0),o.clearLastError()},napi_delete_reference:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return r?(l.refStore.get(r).dispose(),t.clearLastError()):t.setLastError(1)},napi_escape_handle:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!r)return o.setLastError(1);if(!t)return o.setLastError(1);if(!a)return o.setLastError(1);var i=l.scopeStore.get(r);if(!i.escapeCalled()){var s=i.escape(t),u=s?s.id:0;return new DataView(n.buffer).setUint32(a,u,!0),o.clearLastError()}return o.setLastError(12)},napi_get_reference_value:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.refStore.get(r).get(a);return new DataView(n.buffer).setUint32(t,o,!0),a.clearLastError()},napi_open_escapable_handle_scope:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=l.openScope(t);return new DataView(n.buffer).setUint32(r,a.id,!0),t.clearLastError()},napi_open_handle_scope:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=l.openScope(t);return new DataView(n.buffer).setUint32(r,a.id,!0),t.clearLastError()},napi_reference_ref:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);var o=l.refStore.get(r).ref();return t&&new DataView(n.buffer).setUint32(t,o,!0),a.clearLastError()},napi_reference_unref:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);var o=l.refStore.get(r);if(0===o.refcount())return a.setLastError(9);var i=o.unref();return t&&new DataView(n.buffer).setUint32(t,i,!0),a.clearLastError()},napi_remove_env_cleanup_hook:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);return r?(l.removeCleanupHook(n,r,t),0):n.setLastError(1)}});var ge=Object.freeze({__proto__:null,_emnapi_get_filename:function(e,r,t){var n=l.envStore.get(e).filename;return r?q.stringToUTF8(n,r,t):q.lengthBytesUTF8(n)}});var _e=Object.freeze({__proto__:null,napi_create_promise:function(e,r,t){var a,o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!r)return i.setLastError(1);if(!t)return i.setLastError(1);var s=new DataView(n.buffer),u=new Promise(function(e,t){var n=l.createDeferred({resolve:e,reject:t});a=n.id,s.setUint32(r,a,!0)});return o=l.addToCurrentScope(u).id,s.setUint32(t,o,!0),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_is_promise:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isPromise()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_reject_deferred:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{return r&&t?(l.deferredStore.get(r).reject(l.handleStore.get(t).value),n.getReturnStatus()):n.setLastError(1)}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}},napi_resolve_deferred:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{return r&&t?(l.deferredStore.get(r).resolve(l.handleStore.get(t).value),n.getReturnStatus()):n.setLastError(1)}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}}});function Ee(e,r,t,a,o,i){var s;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!i)return u.setLastError(1);if(!r)return u.setLastError(1);var c=l.handleStore.get(r);if(null==c.value)throw new TypeError("Cannot convert undefined or null to object");var f=void 0;try{f=c.isObject()||c.isFunction()?c.value:Object(c.value)}catch(e){return u.setLastError(2)}if(0!==t&&1!==t)return u.setLastError(1);if(0!==o&&1!==o)return u.setLastError(1);var d=[],p=void 0,v=void 0,h=void 0,y=!0,g=/^(0|[1-9][0-9]*)$/;do{for(p=Object.getOwnPropertyNames(f),v=Object.getOwnPropertySymbols(f),h=0;h<p.length;h++)d.push({name:g.test(p[h])?Number(p[h]):p[h],desc:Object.getOwnPropertyDescriptor(f,p[h]),own:y});for(h=0;h<v.length;h++)d.push({name:v[h],desc:Object.getOwnPropertyDescriptor(f,v[h]),own:y});if(1===t)break;f=Object.getPrototypeOf(f),y=!1}while(f);var _=[],E=function(e,r,t,n){if(-1===e.indexOf(r))if(0===n)e.push(r);else if(1===n){var a="number"==typeof r?String(r):r;"string"==typeof a&&8&t||e.push(a)}};for(h=0;h<d.length;h++){var w=d[h],m=w.name,L=w.desc;if(0===a)E(_,m,a,o);else{if(8&a&&"string"==typeof m)continue;if(16&a&&"symbol"==typeof m)continue;var b=!0;switch(7&a){case 1:b=Boolean(L.writable);break;case 2:b=Boolean(L.enumerable);break;case 3:b=Boolean(L.writable&&L.enumerable);break;case 4:b=Boolean(L.configurable);break;case 5:b=Boolean(L.configurable&&L.writable);break;case 6:b=Boolean(L.configurable&&L.enumerable);break;case 7:b=Boolean(L.configurable&&L.enumerable&&L.writable)}b&&E(_,m,a,o)}}return s=l.addToCurrentScope(_).id,new DataView(n.buffer).setUint32(i,s,!0),u.getReturnStatus()}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}}var we=Object.freeze({__proto__:null,napi_define_properties:function(e,r,t,a){var o,i;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if((t>>>=0)>0&&!a)return s.setLastError(1);if(!r)return s.setLastError(1);var u=l.handleStore.get(r),c=u.value;if(!u.isObject()&&!u.isFunction())return s.setLastError(2);for(var f=void 0,d=new DataView(n.buffer),p=0;p<t;p++){o=a+32*p;var v=d.getUint32(o,!0),h=d.getUint32(o+4,!0),y=d.getUint32(o+8,!0),g=d.getUint32(o+12,!0),_=d.getUint32(o+16,!0),E=d.getUint32(o+20,!0);i=d.getInt32(o+24,!0);var w=d.getUint32(o+28,!0);if(v)f=q.UTF8ToString(v,-1);else{if(!h)return s.setLastError(4);if("string"!=typeof(f=l.handleStore.get(h).value)&&"symbol"!=typeof f)return s.setLastError(4)}re(s,c,f,y,g,_,E,i,w)}return s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_delete_element:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!r)return i.setLastError(1);var s=l.handleStore.get(r);if(!s.isObject()&&!s.isFunction())return i.setLastError(2);if(l.feature.supportReflect)o=Reflect.deleteProperty(s.value,t>>>0);else try{o=delete s.value[t>>>0]}catch(e){o=!1}if(a)new DataView(n.buffer).setInt8(a,o?1:0,!0);return i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_delete_property:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!t)return i.setLastError(1);if(!r)return i.setLastError(1);var s=l.handleStore.get(r);if(!s.isObject()&&!s.isFunction())return i.setLastError(2);var u=l.handleStore.get(t).value;if(l.feature.supportReflect)o=Reflect.deleteProperty(s.value,u);else try{o=delete s.value[u]}catch(e){o=!1}if(a)new DataView(n.buffer).setInt8(a,o?1:0,!0);return i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_get_all_property_names:Ee,napi_get_element:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!a)return i.setLastError(1);if(!r)return i.setLastError(1);var s=l.handleStore.get(r);if(null==s.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=s.isObject()||s.isFunction()?s.value:Object(s.value)}catch(e){return i.setLastError(2)}return o=i.ensureHandleId(u[t>>>0]),new DataView(n.buffer).setUint32(a,o,!0),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_get_named_property:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!a)return i.setLastError(1);if(!r)return i.setLastError(1);if(!t)return i.setLastError(1);var s=l.handleStore.get(r);if(null==s.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=s.isObject()||s.isFunction()?s.value:Object(s.value)}catch(e){return i.setLastError(2)}return o=i.ensureHandleId(u[q.UTF8ToString(t,-1)]),new DataView(n.buffer).setUint32(a,o,!0),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_get_property:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!t)return i.setLastError(1);if(!a)return i.setLastError(1);if(!r)return i.setLastError(1);var s=l.handleStore.get(r);if(null==s.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=s.isObject()||s.isFunction()?s.value:Object(s.value)}catch(e){return i.setLastError(2)}return o=i.ensureHandleId(u[l.handleStore.get(t).value]),new DataView(n.buffer).setUint32(a,o,!0),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_get_property_names:function(e,r,t){return Ee(e,r,0,18,1,t)},napi_has_element:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!a)return i.setLastError(1);if(!r)return i.setLastError(1);var s=l.handleStore.get(r);if(null==s.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=s.isObject()||s.isFunction()?s.value:Object(s.value)}catch(e){return i.setLastError(2)}return o=t>>>0 in u?1:0,new DataView(n.buffer).setInt8(a,o,!0),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_has_named_property:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!a)return i.setLastError(1);if(!r)return i.setLastError(1);if(!t)return i.setLastError(1);var s=l.handleStore.get(r);if(null==s.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=s.isObject()||s.isFunction()?s.value:Object(s.value)}catch(e){return i.setLastError(2)}return o=q.UTF8ToString(t,-1)in u,new DataView(n.buffer).setInt8(a,o?1:0,!0),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_has_own_property:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!t)return i.setLastError(1);if(!a)return i.setLastError(1);if(!r)return i.setLastError(1);var s=l.handleStore.get(r);if(null==s.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=s.isObject()||s.isFunction()?s.value:Object(s.value)}catch(e){return i.setLastError(2)}var c=l.handleStore.get(t).value;return"string"!=typeof c&&"symbol"!=typeof c?i.setLastError(4):(o=Object.prototype.hasOwnProperty.call(u,l.handleStore.get(t).value),new DataView(n.buffer).setInt8(a,o?1:0,!0),i.getReturnStatus())}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_has_property:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!t)return i.setLastError(1);if(!a)return i.setLastError(1);if(!r)return i.setLastError(1);var s=l.handleStore.get(r);if(null==s.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=s.isObject()||s.isFunction()?s.value:Object(s.value)}catch(e){return i.setLastError(2)}return o=l.handleStore.get(t).value in u?1:0,new DataView(n.buffer).setInt8(a,o,!0),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_object_freeze:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{if(!r)return t.setLastError(1);var n=l.handleStore.get(r),a=n.value;return n.isObject()||n.isFunction()?(Object.freeze(a),t.getReturnStatus()):t.setLastError(2)}catch(e){return t.tryCatch.setError(e),t.setLastError(10)}},napi_object_seal:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{if(!r)return t.setLastError(1);var n=l.handleStore.get(r),a=n.value;return n.isObject()||n.isFunction()?(Object.seal(a),t.getReturnStatus()):t.setLastError(2)}catch(e){return t.tryCatch.setError(e),t.setLastError(10)}},napi_set_element:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!n)return a.setLastError(1);if(!r)return a.setLastError(1);var o=l.handleStore.get(r);return o.isObject()||o.isFunction()?(o.value[t>>>0]=l.handleStore.get(n).value,a.getReturnStatus()):a.setLastError(2)}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_set_named_property:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!n)return a.setLastError(1);if(!r)return a.setLastError(1);var o=l.handleStore.get(r);return o.isObject()||o.isFunction()?t?(l.handleStore.get(r).value[q.UTF8ToString(t,-1)]=l.handleStore.get(n).value,a.getReturnStatus()):a.setLastError(1):a.setLastError(2)}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_set_property:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!t)return a.setLastError(1);if(!n)return a.setLastError(1);if(!r)return a.setLastError(1);var o=l.handleStore.get(r);return o.isObject()||o.isFunction()?(o.value[l.handleStore.get(t).value]=l.handleStore.get(n).value,a.getReturnStatus()):a.setLastError(2)}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}}});var me=Object.freeze({__proto__:null,napi_run_script:function(e,r,t){var a,o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!r)return i.setLastError(1);if(!t)return i.setLastError(1);var s=l.handleStore.get(r);if(!s.isString())return i.setLastError(3);var u=l.handleStore.get(5).value.eval(s.value);o=i.ensureHandleId(u),new DataView(n.buffer).setUint32(t,o,!0),a=i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}return a}});var Le=Object.freeze({__proto__:null,napi_coerce_to_bool:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{return r&&t?(a=l.handleStore.get(r).value?4:3,new DataView(n.buffer).setUint32(t,a,!0),o.getReturnStatus()):o.setLastError(1)}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_coerce_to_number:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);var i=l.handleStore.get(r);if(i.isBigInt())throw new TypeError("Cannot convert a BigInt value to a number");return a=l.addToCurrentScope(Number(i.value)).id,new DataView(n.buffer).setUint32(t,a,!0),o.getReturnStatus()}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_coerce_to_object:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);var i=l.handleStore.get(r);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");return a=o.ensureHandleId(Object(i.value)),new DataView(n.buffer).setUint32(t,a,!0),o.getReturnStatus()}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_coerce_to_string:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);var i=l.handleStore.get(r);if(i.isSymbol())throw new TypeError("Cannot convert a Symbol value to a string");return a=l.addToCurrentScope(String(i.value)).id,new DataView(n.buffer).setUint32(t,a,!0),o.getReturnStatus()}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_detach_arraybuffer:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var n=l.handleStore.get(r).value;if(!(n instanceof ArrayBuffer))return"function"==typeof SharedArrayBuffer&&n instanceof SharedArrayBuffer?t.setLastError(20):t.setLastError(19);try{(new(0,l.feature.MessageChannel)).port1.postMessage(n,[n])}catch(e){return t.setLastError(9)}return t.clearLastError()},napi_instanceof:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!r)return i.setLastError(1);if(!a)return i.setLastError(1);if(!t)return i.setLastError(1);var s=new DataView(n.buffer);s.setInt8(a,0,!0);var u=l.handleStore.get(t);return u.isFunction()?(o=l.handleStore.get(r).value instanceof u.value?1:0,s.setInt8(a,o,!0),i.getReturnStatus()):i.setLastError(5)}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_is_array:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isArray()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_arraybuffer:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isArrayBuffer()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_buffer:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isBuffer(l.feature.Buffer)?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_dataview:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isDataView()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_date:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isDate()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_detached_arraybuffer:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r),i=new DataView(n.buffer);if(o.isArrayBuffer()&&0===o.value.byteLength)try{new Uint8Array(o.value)}catch(e){return i.setInt8(t,1,!0),a.getReturnStatus()}return i.setInt8(t,0,!0),a.getReturnStatus()}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_is_error:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).value instanceof Error?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_typedarray:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isTypedArray()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_strict_equals:function(e,r,t,a){var o;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{return r&&(t&&a)?(o=l.handleStore.get(r).value===l.handleStore.get(t).value?1:0,new DataView(n.buffer).setInt8(a,o,!0),i.getReturnStatus()):i.setLastError(1)}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_typeof:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o,i=l.handleStore.get(r);if(i.isNumber())o=3;else if(i.isBigInt())o=9;else if(i.isString())o=4;else if(i.isFunction())o=7;else if(i.isExternal())o=8;else if(i.isObject())o=6;else if(i.isBoolean())o=2;else if(i.isUndefined())o=0;else if(i.isSymbol())o=5;else{if(!i.isNull())return a.setLastError(1);o=1}return new DataView(n.buffer).setInt32(t,o,!0),a.clearLastError()}});var be=Object.freeze({__proto__:null,napi_get_version:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return r?(new DataView(n.buffer).setUint32(r,10,!0),t.clearLastError()):t.setLastError(1)}});function Se(e){for(var r=Object.keys(e),t=0;t<r.length;++t){var n=r[t];0!==n.indexOf("$")&&(0===n.indexOf("emnapi_")?h.imports.emnapi[n]=e[n]:0===n.indexOf("_emnapi_")||"napi_set_last_error"===n||"napi_clear_last_error"===n?h.imports.env[n]=e[n]:h.imports.napi[n]=e[n])}}return x.init(),J.init(),q.init(),R.init(),S.init(),h.emnapi.syncMemory=se,h.emnapi.getMemoryAddress=ue,Se(B),Se(D),Se(N),Se(T),Se($),Se(K),Se(le),Se(de),Se(oe),Se(pe),Se(ce),Se(ve),Se(he),Se(ye),Se(ge),Se(j),Se(_e),Se(we),Se(me),Se(Le),Se(be),h.imports.napi.napi_create_threadsafe_function=function(e,r,t,a,s,u,c,f,d,p,v){if(!e)return 1;var h=l.envStore.get(e);if(h.checkGCAccess(),!a)return h.setLastError(1);if(s>>>=0,0===(u>>>=0))return h.setLastError(1);if(!v)return h.setLastError(1);var y,g=0;if(r){if("function"!=typeof l.handleStore.get(r).value)return h.setLastError(1);g=l.createReference(h,r,1,1).id}else if(!p)return h.setLastError(1);if(t){if(null==(y=l.handleStore.get(t).value))return h.setLastError(2);y=Object(y)}else y={};var _=h.ensureHandleId(y),E=l.handleStore.get(a).value;if("symbol"==typeof E)return h.setLastError(3);E=String(E);var w=h.ensureHandleId(E),m=R.offset.end,L=o(m);if(!L)return h.setLastError(9);new Uint8Array(n.buffer).subarray(L,L+m).fill(0);var b=l.createReference(h,_,1,1),S=b.id,C=new DataView(n.buffer);return C.setUint32(L,S,!0),R.initQueue(L)?(M(_,w,-1,L+R.offset.async_id),C.setUint32(L+R.offset.thread_count,u,!0),C.setUint32(L+R.offset.context,d,!0),C.setUint32(L+R.offset.max_queue_size,s,!0),C.setUint32(L+R.offset.ref,g,!0),C.setUint32(L+R.offset.env,e,!0),C.setUint32(L+R.offset.finalize_data,c,!0),C.setUint32(L+R.offset.finalize_cb,f,!0),C.setUint32(L+R.offset.call_js_cb,p,!0),l.addCleanupHook(h,R.cleanup,L),h.ref(),l.increaseWaitingRequestCounter(),C.setInt32(L+R.offset.async_ref,1,!0),C.setUint32(v,L,!0),h.clearLastError()):(i(L),b.dispose(),h.setLastError(9))},h.imports.napi.napi_get_threadsafe_function_context=function(e,r){if(!e||!r)return f(),1;var t=R.getContext(e);return new DataView(n.buffer).setUint32(r,t,!0),0},h.imports.napi.napi_call_threadsafe_function=function(e,r,t){return e?R.push(e,r,t):(f(),1)},h.imports.napi.napi_acquire_threadsafe_function=function(e){return e?R.getMutex(e).execute(function(){return R.getIsClosing(e)?16:(R.addThreadCount(e),0)}):(f(),1)},h.imports.napi.napi_release_threadsafe_function=function(e,r){if(!e)return f(),1;var t=R.getMutex(e),n=R.getCond(e);return t.execute(function(){if(0===R.getThreadCount(e))return 1;if((R.subThreadCount(e),0===R.getThreadCount(e)||1===r)&&!R.getIsClosing(e)){var t=1===r?1:0;R.setIsClosing(e,t),t&&R.getMaxQueueSize(e)>0&&n.signal(),R.send(e)}return 0})},h.imports.napi.napi_unref_threadsafe_function=function(e,r){if(!r)return f(),1;var t=r+R.offset.async_ref>>2,a=new Int32Array(n.buffer);return Atomics.load(a,t)&&(Atomics.store(a,t,0),l.decreaseWaitingRequestCounter()),0},h.imports.napi.napi_ref_threadsafe_function=function(e,r){if(!r)return f(),1;var t=r+R.offset.async_ref>>2,a=new Int32Array(n.buffer);return Atomics.load(a,t)||(Atomics.store(a,t,1),l.increaseWaitingRequestCounter()),0},h}();return t}function m(e,r,t,n){var a,o=(n=null!=n?n:{}).getMemory,i=n.getTable,s=n.beforeInit;if(null!=o&&"function"!=typeof o)throw new TypeError("options.getMemory is not a function");if(null!=i&&"function"!=typeof i)throw new TypeError("options.getTable is not a function");if(null!=s&&"function"!=typeof s)throw new TypeError("options.beforeInit is not a function");var u="object"==typeof r&&null!==r;if(u){if(r.loaded)throw new Error("napiModule has already loaded");a=r}else a=w(n);var c,f=n.wasi,l={env:a.imports.env,napi:a.imports.napi,emnapi:a.imports.emnapi};f&&(c=new y(a.childThread?{wasi:f,childThread:!0,postMessage:a.postMessage}:{wasi:f,threadManager:a.PThread,waitThreadStart:a.waitThreadStart}),Object.assign(l,"function"==typeof f.getImportObject?f.getImportObject():{wasi_snapshot_preview1:f.wasiImport}),Object.assign(l,c.getImportObject()));var d=n.overwriteImports;if("function"==typeof d){var p=d(l);"object"==typeof p&&null!==p&&(l=p)}return e(t,l,function(r,t){if(r)throw r;var n=t.instance,d=n,p=n.exports,v="memory"in p,h="memory"in l.env,y=o?o(p):v?p.memory:h?l.env.memory:void 0;if(!y)throw new Error("memory is neither exported nor imported");var g=i?i(p):p.__indirect_function_table;if(f&&!v){var _=Object.create(null);Object.assign(_,p,{memory:y}),d={exports:_}}var E=t.module;f?d=c.initialize(d,E,y):a.PThread.setup(E,y);var w=function(){s&&s({instance:n,module:E}),a.init({instance:d,module:E,memory:y,table:g});var e={instance:n,module:E,usedInstance:d};return u||(e.napiModule=a),e};if(a.PThread.shouldPreloadWorkers()){var m=a.PThread.loadWasmModuleToAllWorkers();if(e===L)return m.then(w);throw new Error("Synchronous loading is not supported with worker pool (reuseWorker.size > 0)")}return w()})}function L(e,r,t){return n(e,r).then(function(e){return t(null,e)},function(e){return t(e)})}function b(e,n,a){var o;try{o=function(e,n){if(!e)throw new TypeError("Invalid wasm source");var a;if(t(n),n=null!=n?n:{},e instanceof ArrayBuffer||ArrayBuffer.isView(e))a=new r.Module(e);else{if(!(e instanceof WebAssembly.Module))throw new TypeError("Invalid wasm source");a=e}return{instance:new r.Instance(a,n),module:a}}(e,n)}catch(e){return a(e)}return a(null,o)}var S=function(e,r){return S=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])},S(e,r)};var C=function(){return C=Object.assign||function(e){for(var r,t=1,n=arguments.length;t<n;t++)for(var a in r=arguments[t])Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a]);return e},C.apply(this,arguments)};"function"==typeof SuppressedError&&SuppressedError;var A=function(e){function r(r){var t=this;if("function"!=typeof r.onLoad)throw new TypeError("options.onLoad is not a function");var n=r.onError;return(t=e.call(this,C(C({},r),{onError:function(e,r){var a,o=null===(a=t.instance)||void 0===a?void 0:a.exports.emnapi_thread_crashed;if("function"==typeof o&&o(),"function"!=typeof n)throw e;n(e,r)}}))||this).napiModule=void 0,t}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function t(){this.constructor=e}S(e,r),e.prototype=null===r?Object.create(r):(t.prototype=r.prototype,new t)}(r,e),r.prototype.instantiate=function(e){var r=this,t=this.onLoad(e);return"function"==typeof t.then?t.then(function(e){return r.napiModule=e.napiModule,e}):(this.napiModule=t.napiModule,t)},r.prototype.handle=function(r){var t,n=this;if(e.prototype.handle.call(this,r),null===(t=null==r?void 0:r.data)||void 0===t?void 0:t.__emnapi__){var a=r.data.__emnapi__.type,o=r.data.__emnapi__.payload;try{"async-worker-init"===a?this.handleAfterLoad(r,function(){n.napiModule.initWorker(o.arg)}):"async-work-execute"===a&&this.handleAfterLoad(r,function(){n.napiModule.executeAsyncWork(o.work)})}catch(e){this.onError(e,a)}}},r}(_);e.MessageHandler=A,e.ThreadManager=d,e.ThreadMessageHandler=_,e.WASIThreads=y,e.createInstanceProxy=v,e.createNapiModule=w,e.instantiateNapiModule=function(e,r){return m(L,void 0,e,r)},e.instantiateNapiModuleSync=function(e,r){return m(b,void 0,e,r)},e.isSharedArrayBuffer=s,e.isTrapError=u,e.loadNapiModule=function(e,r,t){if("object"!=typeof e||null===e)throw new TypeError("Invalid napiModule");return m(L,e,r,t)},e.loadNapiModuleSync=function(e,r,t){if("object"!=typeof e||null===e)throw new TypeError("Invalid napiModule");return m(b,e,r,t)},e.version="1.5.0"});

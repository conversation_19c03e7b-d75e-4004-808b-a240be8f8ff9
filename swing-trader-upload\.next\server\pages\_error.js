var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/pages/_error.js")
R.c("server/chunks/ssr/7b731_491db09e._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e6a4d965._.js")
R.c("server/chunks/ssr/7b731_next_395e4191._.js")
R.c("server/chunks/ssr/7b731_bf69dce4._.js")
R.c("server/chunks/ssr/[externals]_next_dist_shared_lib_no-fallback-error_external_59b92b38.js")
R.m("[project]/SwingTrader-AI-Package/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/SwingTrader-AI-Package/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/SwingTrader-AI-Package/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/SwingTrader-AI-Package/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)")
module.exports=R.m("[project]/SwingTrader-AI-Package/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/SwingTrader-AI-Package/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/SwingTrader-AI-Package/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/SwingTrader-AI-Package/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)").exports

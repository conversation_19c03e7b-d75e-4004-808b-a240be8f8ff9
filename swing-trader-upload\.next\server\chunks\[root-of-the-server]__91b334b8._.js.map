{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/app/api/analysis/ai-setup/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport OpenAI from 'openai'\nimport { EnhancedScanResult } from '@/lib/enhancedSwingScanner'\nimport { AIAnalysis } from '@/types/paperTrading'\n\nconst HARDCODED_API_KEY = '********************************************************************************************************************************************************************'\n\nconsole.log('🔑 Hardcoded API Key prefix:', HARDCODED_API_KEY.substring(0, 15))\nconsole.log('🔑 Environment API Key prefix:', process.env.OPENAI_API_KEY?.substring(0, 15))\n\nconst openai = new OpenAI({\n  apiKey: HARDCODED_API_KEY,\n})\n\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('🚀 AI Analysis endpoint called')\n    console.log('🔑 Using hardcoded API key:', HARDCODED_API_KEY.substring(0, 15))\n\n    const { scanResult }: { scanResult: EnhancedScanResult } = await request.json()\n\n    if (!scanResult) {\n      return NextResponse.json({ error: 'Scan result is required' }, { status: 400 })\n    }\n\n    // Prepare context for AI analysis\n    const setup = scanResult.overnightSetup || scanResult.breakoutSetup\n    if (!setup) {\n      return NextResponse.json({ error: 'No trading setup found' }, { status: 400 })\n    }\n\n    const strategyType = scanResult.overnightSetup ? 'overnight momentum' : 'technical breakout'\n    const currentPrice = scanResult.quote.price\n    const changePercent = scanResult.quote.changePercent || 0\n\n    const prompt = `As a professional swing trading analyst, provide a comprehensive analysis for ${scanResult.symbol} (${scanResult.name}) in the ${scanResult.sector} sector.\n\nCURRENT SETUP:\n- Strategy: ${strategyType}\n- Current Price: $${currentPrice}\n- Daily Change: ${changePercent.toFixed(2)}%\n- Entry Price: $${setup.entryPrice}\n- Stop Loss: $${setup.stopLoss}\n- Target: $${setup.targets[0]}\n- Confidence: ${setup.confidence}%\n- Overall Score: ${scanResult.overallScore}/100\n\nTRADING ALERTS: ${scanResult.alerts.join(', ') || 'None'}\nRISK WARNINGS: ${scanResult.riskWarnings.join(', ') || 'None'}\n\nPlease provide a JSON response with the following structure:\n{\n  \"setupExplanation\": \"Clear explanation of why this setup meets swing trading criteria\",\n  \"catalysts\": [\"List of potential market catalysts supporting this trade\"],\n  \"riskAssessment\": \"Detailed risk analysis and what could go wrong\",\n  \"keyLevels\": [\"Important price levels to watch\"],\n  \"timeframe\": \"Expected timeframe for trade to play out\",\n  \"confidence\": number between 1-100\n}\n\nFocus on:\n1. Technical analysis backing the setup\n2. Market conditions and sector trends\n3. Risk/reward analysis\n4. Specific entry/exit strategy\n5. Potential catalysts or headwinds`\n\n    const completion = await openai.chat.completions.create({\n      model: \"gpt-4\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are a professional swing trading analyst with expertise in technical analysis, market dynamics, and risk management. Provide actionable, data-driven insights.\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      temperature: 0.3,\n      max_tokens: 1000,\n    })\n\n    const aiResponse = completion.choices[0]?.message?.content\n    if (!aiResponse) {\n      throw new Error('No response from AI')\n    }\n\n    // Parse the JSON response\n    let analysis: Partial<AIAnalysis>\n    try {\n      analysis = JSON.parse(aiResponse)\n    } catch (parseError) {\n      // Fallback if JSON parsing fails\n      analysis = {\n        setupExplanation: aiResponse.substring(0, 300) + '...',\n        catalysts: ['AI analysis available - see full response'],\n        riskAssessment: 'Standard swing trading risks apply',\n        keyLevels: [`Entry: $${setup.entryPrice}`, `Stop: $${setup.stopLoss}`, `Target: $${setup.targets[0]}`],\n        timeframe: '3-10 trading days',\n        confidence: Math.round(setup.confidence)\n      }\n    }\n\n    const fullAnalysis: AIAnalysis = {\n      symbol: scanResult.symbol,\n      setupExplanation: analysis.setupExplanation || 'Setup analysis not available',\n      catalysts: analysis.catalysts || [],\n      riskAssessment: analysis.riskAssessment || 'Standard risks apply',\n      keyLevels: analysis.keyLevels || [],\n      timeframe: analysis.timeframe || '3-10 days',\n      confidence: analysis.confidence || Math.round(setup.confidence),\n      lastUpdated: new Date().toISOString()\n    }\n\n    return NextResponse.json(fullAnalysis)\n\n  } catch (error) {\n    console.error('AI Analysis error:', error)\n    return NextResponse.json(\n      { error: 'Failed to generate AI analysis' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAIA,MAAM,oBAAoB;AAE1B,QAAQ,GAAG,CAAC,gCAAgC,kBAAkB,SAAS,CAAC,GAAG;AAC3E,QAAQ,GAAG,CAAC,kCAAkC,QAAQ,GAAG,CAAC,cAAc,EAAE,UAAU,GAAG;AAEvF,MAAM,SAAS,IAAI,mLAAM,CAAC;IACxB,QAAQ;AACV;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,+BAA+B,kBAAkB,SAAS,CAAC,GAAG;QAE1E,MAAM,EAAE,UAAU,EAAE,GAAuC,MAAM,QAAQ,IAAI;QAE7E,IAAI,CAAC,YAAY;YACf,OAAO,gLAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,kCAAkC;QAClC,MAAM,QAAQ,WAAW,cAAc,IAAI,WAAW,aAAa;QACnE,IAAI,CAAC,OAAO;YACV,OAAO,gLAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAyB,GAAG;gBAAE,QAAQ;YAAI;QAC9E;QAEA,MAAM,eAAe,WAAW,cAAc,GAAG,uBAAuB;QACxE,MAAM,eAAe,WAAW,KAAK,CAAC,KAAK;QAC3C,MAAM,gBAAgB,WAAW,KAAK,CAAC,aAAa,IAAI;QAExD,MAAM,SAAS,CAAC,8EAA8E,EAAE,WAAW,MAAM,CAAC,EAAE,EAAE,WAAW,IAAI,CAAC,SAAS,EAAE,WAAW,MAAM,CAAC;;;YAG3J,EAAE,aAAa;kBACT,EAAE,aAAa;gBACjB,EAAE,cAAc,OAAO,CAAC,GAAG;gBAC3B,EAAE,MAAM,UAAU,CAAC;cACrB,EAAE,MAAM,QAAQ,CAAC;WACpB,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;cAChB,EAAE,MAAM,UAAU,CAAC;iBAChB,EAAE,WAAW,YAAY,CAAC;;gBAE3B,EAAE,WAAW,MAAM,CAAC,IAAI,CAAC,SAAS,OAAO;eAC1C,EAAE,WAAW,YAAY,CAAC,IAAI,CAAC,SAAS,OAAO;;;;;;;;;;;;;;;;;mCAiB3B,CAAC;QAEhC,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,YAAY;QACd;QAEA,MAAM,aAAa,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;QACnD,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,MAAM;QAClB;QAEA,0BAA0B;QAC1B,IAAI;QACJ,IAAI;YACF,WAAW,KAAK,KAAK,CAAC;QACxB,EAAE,OAAO,YAAY;YACnB,iCAAiC;YACjC,WAAW;gBACT,kBAAkB,WAAW,SAAS,CAAC,GAAG,OAAO;gBACjD,WAAW;oBAAC;iBAA4C;gBACxD,gBAAgB;gBAChB,WAAW;oBAAC,CAAC,QAAQ,EAAE,MAAM,UAAU,EAAE;oBAAE,CAAC,OAAO,EAAE,MAAM,QAAQ,EAAE;oBAAE,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE;iBAAC;gBACtG,WAAW;gBACX,YAAY,KAAK,KAAK,CAAC,MAAM,UAAU;YACzC;QACF;QAEA,MAAM,eAA2B;YAC/B,QAAQ,WAAW,MAAM;YACzB,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,WAAW,SAAS,SAAS,IAAI,EAAE;YACnC,gBAAgB,SAAS,cAAc,IAAI;YAC3C,WAAW,SAAS,SAAS,IAAI,EAAE;YACnC,WAAW,SAAS,SAAS,IAAI;YACjC,YAAY,SAAS,UAAU,IAAI,KAAK,KAAK,CAAC,MAAM,UAAU;YAC9D,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,OAAO,gLAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gLAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
// Test script to verify AI analysis mode switching functionality
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Sample scan result for testing
const sampleScanResult = {
  symbol: 'MP',
  name: 'MP Materials Corp',
  sector: 'Materials',
  quote: {
    price: 75.02,
    changePercent: 2.5
  },
  overnightSetup: {
    entryPrice: 74.76,
    stopLoss: 73.94,
    targets: [77.01],
    confidence: 85,
    currentPrice: 75.02,
    momentum: 2.5
  },
  overallScore: 82,
  alerts: ['Strong momentum', 'Volume spike'],
  riskWarnings: []
};

async function testModeSwitch() {
  console.log('🧪 Testing AI Analysis Mode Switching...\n');

  try {
    // Test SUMMARY mode
    console.log('📊 Testing SUMMARY mode...');
    const summaryResponse = await axios.post(
      `${BASE_URL}/api/analysis/ai-setup?mode=SUMMARY`,
      { scanResult: sampleScanResult },
      { 
        headers: { 'Content-Type': 'application/json' },
        timeout: 30000 
      }
    );

    console.log('✅ SUMMARY Response:');
    console.log(JSON.stringify(summaryResponse.data, null, 2));
    console.log('\n' + '='.repeat(50) + '\n');

    // Test FULL mode
    console.log('📈 Testing FULL mode...');
    const fullResponse = await axios.post(
      `${BASE_URL}/api/analysis/ai-setup?mode=FULL`,
      { scanResult: sampleScanResult },
      { 
        headers: { 'Content-Type': 'application/json' },
        timeout: 30000 
      }
    );

    console.log('✅ FULL Response:');
    console.log(JSON.stringify(fullResponse.data, null, 2));
    console.log('\n' + '='.repeat(50) + '\n');

    // Test default mode (should be SUMMARY)
    console.log('🔄 Testing default mode (should be SUMMARY)...');
    const defaultResponse = await axios.post(
      `${BASE_URL}/api/analysis/ai-setup`,
      { scanResult: sampleScanResult },
      { 
        headers: { 'Content-Type': 'application/json' },
        timeout: 30000 
      }
    );

    console.log('✅ Default Response:');
    console.log(JSON.stringify(defaultResponse.data, null, 2));

    // Verify the differences
    console.log('\n🔍 Mode Comparison:');
    console.log(`SUMMARY fields: ${Object.keys(summaryResponse.data).length}`);
    console.log(`FULL fields: ${Object.keys(fullResponse.data).length}`);
    console.log(`Default fields: ${Object.keys(defaultResponse.data).length}`);

    if (Object.keys(summaryResponse.data).length < Object.keys(fullResponse.data).length) {
      console.log('✅ Mode switching working correctly - SUMMARY has fewer fields than FULL');
    } else {
      console.log('❌ Mode switching may not be working - field counts are similar');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testModeSwitch();

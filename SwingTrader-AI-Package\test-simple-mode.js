// Simple test to verify mode switching without timeout issues
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Sample scan result for testing
const sampleScanResult = {
  symbol: 'AAPL',
  name: 'Apple Inc',
  sector: 'Technology',
  quote: {
    price: 150.00,
    changePercent: 1.5
  },
  overnightSetup: {
    entryPrice: 149.50,
    stopLoss: 147.00,
    targets: [155.00],
    confidence: 80,
    currentPrice: 150.00,
    momentum: 1.5
  },
  overallScore: 78,
  alerts: ['Bullish momentum'],
  riskWarnings: []
};

async function testSingleMode(mode) {
  console.log(`🧪 Testing ${mode} mode...`);
  
  try {
    const response = await axios.post(
      `${BASE_URL}/api/analysis/ai-setup?mode=${mode}`,
      { scanResult: sampleScanResult },
      { 
        headers: { 'Content-Type': 'application/json' },
        timeout: 60000 // 60 second timeout
      }
    );

    console.log(`✅ ${mode} Response received:`);
    console.log(`📊 Fields: ${Object.keys(response.data).length}`);
    console.log(`📝 Field names: ${Object.keys(response.data).join(', ')}`);
    
    if (response.data.setupExplanation) {
      console.log(`📄 Setup explanation length: ${response.data.setupExplanation.length} chars`);
    }
    
    return response.data;
    
  } catch (error) {
    console.error(`❌ ${mode} mode failed:`, error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    return null;
  }
}

async function runTest() {
  console.log('🚀 Starting Mode Switching Test\n');
  
  // Test SUMMARY mode first
  const summaryResult = await testSingleMode('SUMMARY');
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Wait a bit between requests
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test FULL mode
  const fullResult = await testSingleMode('FULL');
  
  // Compare results
  if (summaryResult && fullResult) {
    console.log('\n🔍 Comparison:');
    console.log(`SUMMARY fields: ${Object.keys(summaryResult).length}`);
    console.log(`FULL fields: ${Object.keys(fullResult).length}`);
    
    if (Object.keys(summaryResult).length < Object.keys(fullResult).length) {
      console.log('✅ Mode switching working - SUMMARY is more concise');
    } else {
      console.log('⚠️  Both modes returned similar field counts');
    }
  }
}

runTest();

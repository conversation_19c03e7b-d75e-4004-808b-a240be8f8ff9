'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Search, TrendingUp, TrendingDown, Minus, Target, Shield, BarChart3 } from 'lucide-react'
import { ScanResult, ScanSummary } from '@/lib/swingScanner'
import { formatCurrency, formatPercentage } from '@/lib/utils'

interface ScannerProps {
  autoScan?: boolean
}

export function SwingScanner({ autoScan = false }: ScannerProps) {
  const [isScanning, setIsScanning] = useState(false)
  const [scanResults, setScanResults] = useState<ScanSummary | null>(null)
  const [selectedScan, setSelectedScan] = useState<'quick' | 'full' | 'sector'>('quick')
  const [selectedSector, setSelectedSector] = useState<string>('Technology')
  const [error, setError] = useState<string | null>(null)

  const sectors = [
    'Technology', 'Financial Services', 'Healthcare', 'Industrial', 
    'Materials', 'Consumer', 'Communication Services', 'Energy'
  ]

  // Auto-scan on component mount if enabled
  useEffect(() => {
    if (autoScan) {
      handleQuickScan()
    }
  }, [autoScan])

  const handleQuickScan = async () => {
    setIsScanning(true)
    setError(null)
    setScanResults(null)

    try {
      const response = await fetch('/api/scanner/quick?limit=15')
      if (!response.ok) throw new Error('Failed to fetch scan results')
      
      const data = await response.json()
      
      // Convert to ScanSummary format
      const summary: ScanSummary = {
        totalScanned: data.totalScanned,
        successfulScans: data.results.length,
        failedScans: data.totalScanned - data.results.length,
        topOpportunities: data.results,
        sectorBreakdown: {},
        scanDuration: 0
      }
      
      setScanResults(summary)
    } catch (err) {
      setError('Failed to perform quick scan. Please try again.')
      console.error('Quick scan error:', err)
    } finally {
      setIsScanning(false)
    }
  }

  const handleFullScan = async () => {
    setIsScanning(true)
    setError(null)
    setScanResults(null)

    try {
      const response = await fetch('/api/scanner/full?limit=25&concurrent=3')
      if (!response.ok) throw new Error('Failed to fetch scan results')
      
      const data = await response.json()
      setScanResults(data)
    } catch (err) {
      setError('Failed to perform full scan. Please try again.')
      console.error('Full scan error:', err)
    } finally {
      setIsScanning(false)
    }
  }

  const handleSectorScan = async () => {
    setIsScanning(true)
    setError(null)
    setScanResults(null)

    try {
      const response = await fetch(`/api/scanner/sector/${encodeURIComponent(selectedSector)}?limit=15`)
      if (!response.ok) throw new Error('Failed to fetch scan results')
      
      const data = await response.json()
      setScanResults(data)
    } catch (err) {
      setError('Failed to perform sector scan. Please try again.')
      console.error('Sector scan error:', err)
    } finally {
      setIsScanning(false)
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'BULLISH':
        return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'BEARISH':
        return <TrendingDown className="h-4 w-4 text-red-400" />
      default:
        return <Minus className="h-4 w-4 text-yellow-400" />
    }
  }

  const getRecommendationColor = (recommendation: string) => {
    if (recommendation.includes('BUY')) return 'bg-green-500/20 text-green-400'
    if (recommendation.includes('SELL')) return 'bg-red-500/20 text-red-400'
    return 'bg-yellow-500/20 text-yellow-400'
  }

  return (
    <div className="space-y-6">
      {/* Scanner Controls */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Search className="mr-2 h-5 w-5 text-blue-400" />
            Swing Trading Scanner
          </CardTitle>
          <CardDescription className="text-slate-300">
            Automatically scan stocks for the best swing trading opportunities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-4">
            <Button
              onClick={handleQuickScan}
              disabled={isScanning}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isScanning && selectedScan === 'quick' ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Quick Scan (Top 16)
            </Button>
            
            <Button
              onClick={handleFullScan}
              disabled={isScanning}
              variant="outline"
              className="border-slate-600 text-slate-300 hover:bg-slate-800"
            >
              {isScanning && selectedScan === 'full' ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Full Scan (All 70+ Stocks)
            </Button>

            <div className="flex gap-2">
              <select
                value={selectedSector}
                onChange={(e) => setSelectedSector(e.target.value)}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm"
                disabled={isScanning}
              >
                {sectors.map(sector => (
                  <option key={sector} value={sector}>{sector}</option>
                ))}
              </select>
              <Button
                onClick={handleSectorScan}
                disabled={isScanning}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-800"
              >
                {isScanning && selectedScan === 'sector' ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                Scan Sector
              </Button>
            </div>
          </div>

          {isScanning && (
            <div className="text-center py-4">
              <Loader2 className="mx-auto h-8 w-8 animate-spin text-blue-400" />
              <p className="text-slate-300 mt-2">
                Scanning stocks for swing trading opportunities...
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="bg-red-900/20 border-red-500/50">
          <CardContent className="p-6">
            <p className="text-red-300 text-center">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Scan Results */}
      {scanResults && (
        <div className="space-y-6">
          {/* Scan Summary */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Scan Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{scanResults.totalScanned}</div>
                  <div className="text-sm text-slate-300">Total Scanned</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{scanResults.successfulScans}</div>
                  <div className="text-sm text-slate-300">Successful</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-400">{scanResults.failedScans}</div>
                  <div className="text-sm text-slate-300">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">
                    {scanResults.scanDuration ? `${(scanResults.scanDuration / 1000).toFixed(1)}s` : 'N/A'}
                  </div>
                  <div className="text-sm text-slate-300">Duration</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top Opportunities */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <TrendingUp className="mr-2 h-5 w-5 text-green-400" />
                Top Swing Trading Opportunities
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {scanResults.topOpportunities.map((result, index) => (
                  <div key={result.symbol} className="p-4 bg-slate-700/50 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="text-lg font-bold text-white">#{result.rank}</div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-semibold text-white">{result.symbol}</span>
                            {getTrendIcon(result.analysis.trend)}
                            <Badge className={getRecommendationColor(result.analysis.recommendation)}>
                              {result.analysis.recommendation.replace('_', ' ')}
                            </Badge>
                          </div>
                          <div className="text-sm text-slate-300">{result.name}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xl font-bold text-white">
                          {result.score.toFixed(1)}/100
                        </div>
                        <div className="text-sm text-slate-300">Score</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <div className="text-slate-300">Price</div>
                        <div className="text-white font-semibold">
                          {formatCurrency(result.quote.price)}
                        </div>
                        <div className={result.quote.change >= 0 ? "text-green-400" : "text-red-400"}>
                          {formatPercentage(result.quote.changePercent)}
                        </div>
                      </div>
                      <div>
                        <div className="text-slate-300">Entry</div>
                        <div className="text-blue-400 font-semibold">
                          {formatCurrency(result.analysis.entryPrice)}
                        </div>
                      </div>
                      <div>
                        <div className="text-slate-300">R/R Ratio</div>
                        <div className="text-green-400 font-semibold">
                          {result.analysis.riskRewardRatio.toFixed(2)}:1
                        </div>
                      </div>
                      <div>
                        <div className="text-slate-300">Confidence</div>
                        <div className="text-white font-semibold">
                          {result.analysis.confidence.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

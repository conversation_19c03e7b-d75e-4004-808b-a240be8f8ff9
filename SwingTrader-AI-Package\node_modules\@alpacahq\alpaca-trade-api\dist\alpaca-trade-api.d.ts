export = Alpaca;
declare class Alpaca {
    constructor(config?: {});
    configuration: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    };
    data_ws: websockets.AlpacaStreamClient;
    trade_ws: websockets.AlpacaStreamClient;
    data_stream_v2: websockets_v2.AlpacaStocksClient;
    adjustment: typeof dataV2.Adjustment;
    timeframeUnit: typeof entityV2.TimeFrameUnit;
    crypto_stream_v1beta3: crypto_websocket.AlpacaCryptoClient;
    news_stream: news_stream.AlpacaNewsCLient;
    option_stream: option_stream.AlpacaOptionClient;
    httpRequest: typeof api.httpRequest;
    dataHttpRequest: typeof api.dataHttpRequest;
    sendRequest(endpoint: any, queryParams: any, body: any, method: any): any;
    getAccount: typeof account.get;
    updateAccountConfigurations: typeof account.updateConfigs;
    getAccountConfigurations: typeof account.getConfigs;
    getAccountActivities: typeof account.getActivities;
    getPortfolioHistory: typeof account.getPortfolioHistory;
    getPositions: typeof position.getAll;
    getPosition: typeof position.getOne;
    closeAllPositions: typeof position.closeAll;
    closePosition: typeof position.closeOne;
    getCalendar: typeof calendar.get;
    getClock: typeof clock.get;
    getAssets: typeof asset.getAll;
    getAsset: typeof asset.getOne;
    getOrders: typeof order.getAll;
    getOrder: typeof order.getOne;
    getOrderByClientId: typeof order.getByClientOrderId;
    createOrder: typeof order.post;
    replaceOrder: typeof order.patchOrder;
    cancelOrder: typeof order.cancel;
    cancelAllOrders: typeof order.cancelAll;
    getTradesV2(symbol: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): AsyncGenerator<entityV2.AlpacaTrade, void, unknown>;
    getMultiTradesV2(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.AlpacaTrade[]>>;
    getMultiTradesAsyncV2(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): AsyncGenerator<entityV2.AlpacaTrade, void, unknown>;
    getQuotesV2(symbol: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): AsyncGenerator<entityV2.AlpacaQuote, void, unknown>;
    getMultiQuotesV2(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.AlpacaQuote[]>>;
    getMultiQuotesAsyncV2(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): AsyncGenerator<entityV2.AlpacaQuote, void, unknown>;
    getBarsV2(symbol: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): AsyncGenerator<entityV2.AlpacaBar, void, unknown>;
    getMultiBarsV2(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.AlpacaBar[]>>;
    getMultiBarsAsyncV2(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): AsyncGenerator<entityV2.AlpacaBar, void, unknown>;
    getLatestTrade(symbol: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<entityV2.AlpacaTrade>;
    getLatestTrades(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.AlpacaTrade>>;
    getLatestQuote(symbol: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<entityV2.AlpacaQuote>;
    getLatestQuotes(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.AlpacaQuote>>;
    getLatestBar(symbol: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<entityV2.AlpacaBar>;
    getLatestBars(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.AlpacaBar>>;
    getSnapshot(symbol: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<entityV2.AlpacaSnapshot>;
    getSnapshots(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<entityV2.AlpacaSnapshot[]>;
    getCryptoTrades(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.CryptoTrade[]>>;
    getCryptoQuotes(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.CryptoQuote[]>>;
    getCryptoBars(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.CryptoBar[]>>;
    getLatestCryptoTrades(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.CryptoTrade>>;
    getLatestCryptoQuotes(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.CryptoQuote>>;
    getLatestCryptoBars(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.CryptoBar>>;
    getCryptoSnapshots(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.CryptoSnapshot>>;
    getCryptoOrderbooks(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.CryptoOrderbook>>;
    getOptionBars(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.AlpacaOptionBar[]>>;
    getOptionTrades(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.AlpacaOptionTrade[]>>;
    getOptionLatestTrades(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.AlpacaOptionTrade>>;
    getOptionLatestQuotes(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<Map<string, entityV2.AlpacaOptionQuote>>;
    getOptionSnapshots(symbols: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<entityV2.AlpacaOptionSnapshot[]>;
    getOptionChain(underlying_symbol: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<entityV2.AlpacaOptionSnapshot[]>;
    getCorporateActions(symbols: any, options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<entityV2.CorporateActions | undefined>;
    getNews(options: any, config?: {
        baseUrl: any;
        dataBaseUrl: any;
        dataStreamUrl: any;
        keyId: any;
        secretKey: any;
        apiVersion: any;
        oauth: any;
        feed: any;
        optionFeed: any;
        verbose: any;
    }): Promise<entityV2.AlpacaNews[]>;
    newTimeframe(amount: any, unit: any): string;
    getWatchlists: typeof watchlist.getAll;
    getWatchlist: typeof watchlist.getOne;
    addWatchlist: typeof watchlist.addWatchlist;
    addToWatchlist: typeof watchlist.addToWatchlist;
    updateWatchlist: typeof watchlist.updateWatchlist;
    deleteWatchlist: typeof watchlist.deleteWatchlist;
    deleteFromWatchlist: typeof watchlist.deleteFromWatchlist;
}
import websockets = require("./resources/websockets");
import websockets_v2 = require("./resources/datav2/stock_websocket_v2");
import dataV2 = require("./resources/datav2/rest_v2");
import entityV2 = require("./resources/datav2/entityv2");
import crypto_websocket = require("./resources/datav2/crypto_websocket_v1beta3");
import news_stream = require("./resources/datav2/news_websocket");
import option_stream = require("./resources/datav2/option_websocket_v1beta1");
import api = require("./api");
import account = require("./resources/account");
import position = require("./resources/position");
import calendar = require("./resources/calendar");
import clock = require("./resources/clock");
import asset = require("./resources/asset");
import order = require("./resources/order");
import watchlist = require("./resources/watchlist");

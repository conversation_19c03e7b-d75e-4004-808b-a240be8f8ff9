{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/SwingTrader-AI-Package/src/app/api/paper-trading/execute/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { EnhancedScanResult } from '@/lib/enhancedSwingScanner'\nimport { PaperTrade } from '@/types/paperTrading'\nimport { v4 as uuidv4 } from 'uuid'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { scanResult, accountSize }: { \n      scanResult: EnhancedScanResult\n      accountSize: number \n    } = await request.json()\n\n    if (!scanResult || !accountSize) {\n      return NextResponse.json({ error: 'Scan result and account size are required' }, { status: 400 })\n    }\n\n    // Determine which setup to use\n    const setup = scanResult.overnightSetup || scanResult.breakoutSetup\n    if (!setup) {\n      return NextResponse.json({ error: 'No trading setup found' }, { status: 400 })\n    }\n\n    const strategy = scanResult.overnightSetup ? 'overnight_momentum' : 'technical_breakout'\n\n    // Calculate position sizing with risk management\n    const entryPrice = setup.entryPrice\n    const stopLoss = setup.stopLoss\n    const riskPerShare = Math.abs(entryPrice - stopLoss)\n    \n    // Use 1-2% risk per trade (configurable)\n    const riskPercentage = 1.5 // 1.5% of account\n    const riskAmount = accountSize * (riskPercentage / 100)\n    \n    // Calculate position size based on risk\n    let positionSize = Math.floor(riskAmount / riskPerShare)\n    \n    // Ensure we don't exceed reasonable position limits\n    const maxPositionValue = accountSize * 0.1 // Max 10% of account per position\n    const maxShares = Math.floor(maxPositionValue / entryPrice)\n    positionSize = Math.min(positionSize, maxShares)\n    \n    // Minimum position size check\n    if (positionSize < 1) {\n      return NextResponse.json({ \n        error: 'Position size too small - increase account size or reduce risk per share' \n      }, { status: 400 })\n    }\n\n    // Create paper trade record\n    const paperTrade: PaperTrade = {\n      id: uuidv4(),\n      symbol: scanResult.symbol,\n      strategy,\n      entryPrice,\n      stopLoss,\n      targets: setup.targets,\n      positionSize,\n      accountSize,\n      riskAmount,\n      riskPercentage,\n      entryTime: new Date().toISOString(),\n      status: 'open',\n      currentPrice: scanResult.quote.price,\n      unrealizedPnL: 0,\n      notes: `${strategy} setup - Score: ${scanResult.overallScore}/100`\n    }\n\n    // Calculate potential profit/loss\n    const potentialProfit = (setup.targets[0] - entryPrice) * positionSize\n    const potentialLoss = (entryPrice - stopLoss) * positionSize\n    const riskRewardRatio = potentialProfit / Math.abs(potentialLoss)\n\n    const executionSummary = {\n      paperTrade,\n      executionDetails: {\n        symbol: scanResult.symbol,\n        strategy,\n        entryPrice: entryPrice,\n        stopLoss: stopLoss,\n        firstTarget: setup.targets[0],\n        positionSize,\n        positionValue: entryPrice * positionSize,\n        riskAmount,\n        riskPercentage,\n        potentialProfit,\n        potentialLoss,\n        riskRewardRatio: Math.round(riskRewardRatio * 100) / 100,\n        executionTime: new Date().toISOString()\n      }\n    }\n\n    // In a real application, you would save this to a database\n    // For now, we'll return the trade details for client-side storage\n    \n    return NextResponse.json(executionSummary)\n\n  } catch (error) {\n    console.error('Paper trading execution error:', error)\n    return NextResponse.json(\n      { error: 'Failed to execute paper trade' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAGA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAG7B,MAAM,QAAQ,IAAI;QAEtB,IAAI,CAAC,cAAc,CAAC,aAAa;YAC/B,OAAO,gLAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA4C,GAAG;gBAAE,QAAQ;YAAI;QACjG;QAEA,+BAA+B;QAC/B,MAAM,QAAQ,WAAW,cAAc,IAAI,WAAW,aAAa;QACnE,IAAI,CAAC,OAAO;YACV,OAAO,gLAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAyB,GAAG;gBAAE,QAAQ;YAAI;QAC9E;QAEA,MAAM,WAAW,WAAW,cAAc,GAAG,uBAAuB;QAEpE,iDAAiD;QACjD,MAAM,aAAa,MAAM,UAAU;QACnC,MAAM,WAAW,MAAM,QAAQ;QAC/B,MAAM,eAAe,KAAK,GAAG,CAAC,aAAa;QAE3C,yCAAyC;QACzC,MAAM,iBAAiB,IAAI,kBAAkB;;QAC7C,MAAM,aAAa,cAAc,CAAC,iBAAiB,GAAG;QAEtD,wCAAwC;QACxC,IAAI,eAAe,KAAK,KAAK,CAAC,aAAa;QAE3C,oDAAoD;QACpD,MAAM,mBAAmB,cAAc,IAAI,kCAAkC;;QAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,mBAAmB;QAChD,eAAe,KAAK,GAAG,CAAC,cAAc;QAEtC,8BAA8B;QAC9B,IAAI,eAAe,GAAG;YACpB,OAAO,gLAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,4BAA4B;QAC5B,MAAM,aAAyB;YAC7B,IAAI,IAAA,mLAAM;YACV,QAAQ,WAAW,MAAM;YACzB;YACA;YACA;YACA,SAAS,MAAM,OAAO;YACtB;YACA;YACA;YACA;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;YACR,cAAc,WAAW,KAAK,CAAC,KAAK;YACpC,eAAe;YACf,OAAO,GAAG,SAAS,gBAAgB,EAAE,WAAW,YAAY,CAAC,IAAI,CAAC;QACpE;QAEA,kCAAkC;QAClC,MAAM,kBAAkB,CAAC,MAAM,OAAO,CAAC,EAAE,GAAG,UAAU,IAAI;QAC1D,MAAM,gBAAgB,CAAC,aAAa,QAAQ,IAAI;QAChD,MAAM,kBAAkB,kBAAkB,KAAK,GAAG,CAAC;QAEnD,MAAM,mBAAmB;YACvB;YACA,kBAAkB;gBAChB,QAAQ,WAAW,MAAM;gBACzB;gBACA,YAAY;gBACZ,UAAU;gBACV,aAAa,MAAM,OAAO,CAAC,EAAE;gBAC7B;gBACA,eAAe,aAAa;gBAC5B;gBACA;gBACA;gBACA;gBACA,iBAAiB,KAAK,KAAK,CAAC,kBAAkB,OAAO;gBACrD,eAAe,IAAI,OAAO,WAAW;YACvC;QACF;QAEA,2DAA2D;QAC3D,kEAAkE;QAElE,OAAO,gLAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gLAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgC,GACzC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
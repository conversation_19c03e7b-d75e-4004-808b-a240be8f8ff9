module.exports = [
"[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/SwingTrader-AI-Package/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/7b731_fbbc9f3d._.js",
  "build/chunks/[root-of-the-server]__7e17ed2d._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/SwingTrader-AI-Package/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}),
];
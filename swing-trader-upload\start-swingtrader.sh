#!/bin/bash
echo "========================================"
echo "SwingTrader AI - Starting Application"
echo "========================================"
echo ""

echo "📦 Installing dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "🔨 Building application..."
npm run build
if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "🚀 Starting SwingTrader AI..."
echo ""
echo "✅ Application starting..."
echo "🌐 Open your browser to: http://localhost:3000"
echo "🛑 Press Ctrl+C to stop the server"
echo ""

npm start

[{"name":"hot-reloader","duration":102,"timestamp":5100318957,"id":3,"tags":{"version":"15.5.4"},"startTime":1759320319658,"traceId":"88558e09cd28ae7d"},{"name":"setup-dev-bundler","duration":403273,"timestamp":5100317498,"id":2,"parentId":1,"tags":{},"startTime":1759320319657,"traceId":"88558e09cd28ae7d"},{"name":"start-dev-server","duration":1179084,"timestamp":5099635268,"id":1,"tags":{"cpus":"16","platform":"win32","memory.freeMem":"7362322432","memory.totalMem":"***********","memory.heapSizeLimit":"**********","memory.rss":"185065472","memory.heapTotal":"99799040","memory.heapUsed":"78726120"},"startTime":1759320318975,"traceId":"88558e09cd28ae7d"},{"name":"compile-path","duration":3830603,"timestamp":5125241618,"id":6,"tags":{"trigger":"/"},"startTime":1759320344581,"traceId":"88558e09cd28ae7d"},{"name":"ensure-page","duration":3832687,"timestamp":5125240877,"id":5,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759320344580,"traceId":"88558e09cd28ae7d"}]
[{"name":"ensure-page","duration":181732,"timestamp":5129085621,"id":7,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759320348425,"traceId":"88558e09cd28ae7d"},{"name":"handle-request","duration":5034476,"timestamp":5125233276,"id":4,"tags":{"url":"/"},"startTime":1759320344572,"traceId":"88558e09cd28ae7d"},{"name":"memory-usage","duration":10,"timestamp":5130267883,"id":8,"parentId":4,"tags":{"url":"/","memory.rss":"719097856","memory.heapUsed":"103381672","memory.heapTotal":"141066240"},"startTime":1759320349607,"traceId":"88558e09cd28ae7d"},{"name":"compile-path","duration":1034424,"timestamp":**********,"id":11,"tags":{"trigger":"/api/scanner/strategies"},"startTime":*************,"traceId":"88558e09cd28ae7d"}]
[{"name":"ensure-page","duration":93591,"timestamp":**********,"id":12,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"88558e09cd28ae7d"},{"name":"handle-request","duration":4349817,"timestamp":**********,"id":9,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20"},"startTime":*************,"traceId":"88558e09cd28ae7d"},{"name":"memory-usage","duration":8,"timestamp":**********,"id":13,"parentId":9,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"88558e09cd28ae7d"},{"name":"ensure-page","duration":39605,"timestamp":**********,"id":15,"parentId":3,"tags":{"inputPage":"/page"},"startTime":*************,"traceId":"88558e09cd28ae7d"},{"name":"ensure-page","duration":31711,"timestamp":**********,"id":16,"parentId":3,"tags":{"inputPage":"/page"},"startTime":*************,"traceId":"88558e09cd28ae7d"},{"name":"handle-request","duration":140756,"timestamp":**********,"id":14,"tags":{"url":"/"},"startTime":*************,"traceId":"88558e09cd28ae7d"},{"name":"memory-usage","duration":6,"timestamp":**********,"id":17,"parentId":14,"tags":{"url":"/","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"88558e09cd28ae7d"},{"name":"ensure-page","duration":42005,"timestamp":**********,"id":19,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"88558e09cd28ae7d"},{"name":"ensure-page","duration":32058,"timestamp":**********,"id":20,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"88558e09cd28ae7d"},{"name":"handle-request","duration":7722443,"timestamp":**********,"id":18,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20"},"startTime":*************,"traceId":"88558e09cd28ae7d"},{"name":"memory-usage","duration":4,"timestamp":**********,"id":21,"parentId":18,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"88558e09cd28ae7d"},{"name":"compile-path","duration":178705,"timestamp":**********,"id":24,"tags":{"trigger":"/api/scanner/full"},"startTime":*************,"traceId":"88558e09cd28ae7d"}]
[{"name":"hot-reloader","duration":60,"timestamp":***********,"id":3,"tags":{"version":"15.5.4"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"setup-dev-bundler","duration":478958,"timestamp":***********,"id":2,"parentId":1,"tags":{},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"start-dev-server","duration":1538768,"timestamp":***********,"id":1,"tags":{"cpus":"16","platform":"win32","memory.freeMem":"**********","memory.totalMem":"***********","memory.heapSizeLimit":"**********","memory.rss":"*********","memory.heapTotal":"*********","memory.heapUsed":"********"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"compile-path","duration":2105735,"timestamp":11623159266,"id":6,"tags":{"trigger":"/"},"startTime":1759326842498,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":2106947,"timestamp":11623158455,"id":5,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759326842498,"traceId":"983fe506342c3523"}]
[{"name":"ensure-page","duration":16769,"timestamp":11625269056,"id":7,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759326844608,"traceId":"983fe506342c3523"},{"name":"handle-request","duration":2502912,"timestamp":11623151901,"id":4,"tags":{"url":"/"},"startTime":1759326842491,"traceId":"983fe506342c3523"},{"name":"memory-usage","duration":9,"timestamp":11625654917,"id":8,"parentId":4,"tags":{"url":"/","memory.rss":"709541888","memory.heapUsed":"103531000","memory.heapTotal":"140541952"},"startTime":1759326844994,"traceId":"983fe506342c3523"},{"name":"compile-path","duration":328012,"timestamp":***********,"id":11,"tags":{"trigger":"/api/scanner/strategies"},"startTime":*************,"traceId":"983fe506342c3523"}]
[{"name":"ensure-page","duration":22556,"timestamp":***********,"id":12,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"handle-request","duration":3246410,"timestamp":***********,"id":9,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"memory-usage","duration":4,"timestamp":***********,"id":13,"parentId":9,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":78653,"timestamp":***********,"id":15,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":58824,"timestamp":***********,"id":16,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"handle-request","duration":********,"timestamp":***********,"id":14,"tags":{"url":"/api/scanner/strategies?type=full&accountSize=100000&limit=20"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"memory-usage","duration":5,"timestamp":***********,"id":17,"parentId":14,"tags":{"url":"/api/scanner/strategies?type=full&accountSize=100000&limit=20","memory.rss":"**********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":1027,"timestamp":***********,"id":18,"parentId":3,"tags":{"inputPage":"/api/stocks/quote/AAPL"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":375,"timestamp":***********,"id":19,"parentId":3,"tags":{"inputPage":"/api/stocks/quote/AAPL"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":574,"timestamp":***********,"id":20,"parentId":3,"tags":{"inputPage":"/api/analysis/swing/AAPL"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":333,"timestamp":***********,"id":21,"parentId":3,"tags":{"inputPage":"/api/analysis/swing/AAPL"},"startTime":*************,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":354,"timestamp":***********,"id":22,"parentId":3,"tags":{"inputPage":"/api/stocks/quote/AAPL"},"startTime":1759326938337,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":471,"timestamp":11718999776,"id":23,"parentId":3,"tags":{"inputPage":"/api/stocks/quote/AAPL"},"startTime":1759326938337,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":357,"timestamp":11719001640,"id":24,"parentId":3,"tags":{"inputPage":"/api/analysis/swing/AAPL"},"startTime":1759326938339,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":519,"timestamp":11719002042,"id":25,"parentId":3,"tags":{"inputPage":"/api/analysis/swing/AAPL"},"startTime":1759326938340,"traceId":"983fe506342c3523"},{"name":"ensure-page","duration":301822,"timestamp":11719014316,"id":28,"parentId":3,"tags":{"inputPage":"/api/stocks/quote/[symbol]/route"},"startTime":1759326938352,"traceId":"983fe506342c3523"},{"name":"compile-path","duration":309691,"timestamp":11719015402,"id":29,"tags":{"trigger":"/api/stocks/quote/[symbol]"},"startTime":1759326938353,"traceId":"983fe506342c3523"}]
[{"name":"hot-reloader","duration":49,"timestamp":11881520809,"id":3,"tags":{"version":"15.5.4"},"startTime":1759327100860,"traceId":"59bed8287f420eee"},{"name":"setup-dev-bundler","duration":358378,"timestamp":11881519474,"id":2,"parentId":1,"tags":{},"startTime":1759327100859,"traceId":"59bed8287f420eee"},{"name":"start-dev-server","duration":999725,"timestamp":11880962949,"id":1,"tags":{"cpus":"16","platform":"win32","memory.freeMem":"6970523648","memory.totalMem":"***********","memory.heapSizeLimit":"**********","memory.rss":"185679872","memory.heapTotal":"100098048","memory.heapUsed":"78607392"},"startTime":1759327100302,"traceId":"59bed8287f420eee"},{"name":"compile-path","duration":2072030,"timestamp":12049286654,"id":6,"tags":{"trigger":"/"},"startTime":1759327268625,"traceId":"59bed8287f420eee"},{"name":"ensure-page","duration":2072942,"timestamp":12049286219,"id":5,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759327268625,"traceId":"59bed8287f420eee"}]
[{"name":"ensure-page","duration":19540,"timestamp":12051362804,"id":7,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759327270702,"traceId":"59bed8287f420eee"},{"name":"handle-request","duration":2442373,"timestamp":12049277108,"id":4,"tags":{"url":"/"},"startTime":1759327268616,"traceId":"59bed8287f420eee"},{"name":"memory-usage","duration":6,"timestamp":12051719546,"id":8,"parentId":4,"tags":{"url":"/","memory.rss":"714051584","memory.heapUsed":"105887872","memory.heapTotal":"141475840"},"startTime":1759327271058,"traceId":"59bed8287f420eee"},{"name":"compile-path","duration":277810,"timestamp":12052367544,"id":11,"tags":{"trigger":"/api/scanner/strategies"},"startTime":1759327271706,"traceId":"59bed8287f420eee"}]
[{"name":"hot-reloader","duration":62,"timestamp":12951473688,"id":3,"tags":{"version":"15.5.4"},"startTime":1759328170813,"traceId":"961131a94bdc7bae"},{"name":"setup-dev-bundler","duration":376666,"timestamp":12951471827,"id":2,"parentId":1,"tags":{},"startTime":1759328170811,"traceId":"961131a94bdc7bae"},{"name":"start-dev-server","duration":1143707,"timestamp":12950804692,"id":1,"tags":{"cpus":"16","platform":"win32","memory.freeMem":"4973072384","memory.totalMem":"***********","memory.heapSizeLimit":"**********","memory.rss":"185233408","memory.heapTotal":"103337984","memory.heapUsed":"66717336"},"startTime":1759328170144,"traceId":"961131a94bdc7bae"},{"name":"compile-path","duration":2188227,"timestamp":12975076311,"id":6,"tags":{"trigger":"/"},"startTime":1759328194415,"traceId":"961131a94bdc7bae"},{"name":"ensure-page","duration":2189239,"timestamp":12975075711,"id":5,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759328194415,"traceId":"961131a94bdc7bae"}]
[{"name":"ensure-page","duration":18177,"timestamp":12977268391,"id":7,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759328196607,"traceId":"961131a94bdc7bae"},{"name":"handle-request","duration":2584694,"timestamp":12975067298,"id":4,"tags":{"url":"/"},"startTime":1759328194406,"traceId":"961131a94bdc7bae"},{"name":"memory-usage","duration":6,"timestamp":12977652064,"id":8,"parentId":4,"tags":{"url":"/","memory.rss":"716300288","memory.heapUsed":"103780800","memory.heapTotal":"142225408"},"startTime":1759328196991,"traceId":"961131a94bdc7bae"},{"name":"compile-path","duration":471640,"timestamp":***********,"id":11,"tags":{"trigger":"/api/scanner/strategies"},"startTime":*************,"traceId":"961131a94bdc7bae"}]
[{"name":"ensure-page","duration":23095,"timestamp":***********,"id":12,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"961131a94bdc7bae"},{"name":"handle-request","duration":*********,"timestamp":***********,"id":9,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20"},"startTime":*************,"traceId":"961131a94bdc7bae"},{"name":"memory-usage","duration":6,"timestamp":***********,"id":13,"parentId":9,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"961131a94bdc7bae"},{"name":"compile-path","duration":196452,"timestamp":***********,"id":16,"tags":{"trigger":"/api/scanner/test"},"startTime":*************,"traceId":"961131a94bdc7bae"}]
[{"name":"ensure-page","duration":57659,"timestamp":***********,"id":17,"parentId":3,"tags":{"inputPage":"/api/scanner/test/route"},"startTime":*************,"traceId":"961131a94bdc7bae"},{"name":"handle-request","duration":520044,"timestamp":***********,"id":14,"tags":{"url":"/api/scanner/test"},"startTime":*************,"traceId":"961131a94bdc7bae"},{"name":"memory-usage","duration":4,"timestamp":***********,"id":18,"parentId":14,"tags":{"url":"/api/scanner/test","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"961131a94bdc7bae"},{"name":"client-hmr-latency","duration":105000,"timestamp":15062686724,"id":19,"parentId":3,"tags":{"updatedModules":["[project]/SwingTrader-AI-Package/src/components/StrategyScanner.tsx"],"page":"/","isPageHidden":true},"startTime":1759330282141,"traceId":"961131a94bdc7bae"},{"name":"ensure-page","duration":46000,"timestamp":15062824526,"id":21,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759330282164,"traceId":"961131a94bdc7bae"},{"name":"ensure-page","duration":29149,"timestamp":15062872896,"id":22,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759330282212,"traceId":"961131a94bdc7bae"},{"name":"handle-request","duration":341479,"timestamp":15062822806,"id":20,"tags":{"url":"/"},"startTime":1759330282162,"traceId":"961131a94bdc7bae"},{"name":"memory-usage","duration":3,"timestamp":15063164341,"id":23,"parentId":20,"tags":{"url":"/","memory.rss":"903954432","memory.heapUsed":"165568240","memory.heapTotal":"177725440"},"startTime":1759330282504,"traceId":"961131a94bdc7bae"},{"name":"ensure-page","duration":41714,"timestamp":15063471688,"id":28,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":1759330282811,"traceId":"961131a94bdc7bae"},{"name":"ensure-page","duration":24655,"timestamp":15063516133,"id":29,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":1759330282855,"traceId":"961131a94bdc7bae"},{"name":"compile-path","duration":104420,"timestamp":15063468188,"id":27,"tags":{"trigger":"/favicon.ico"},"startTime":1759330282808,"traceId":"961131a94bdc7bae"}]
[{"name":"hot-reloader","duration":50,"timestamp":68274958071,"id":3,"tags":{"version":"15.5.4"},"startTime":1759410687291,"traceId":"bcec95d477f64203"},{"name":"setup-dev-bundler","duration":565625,"timestamp":68274956523,"id":2,"parentId":1,"tags":{},"startTime":1759410687289,"traceId":"bcec95d477f64203"},{"name":"start-dev-server","duration":1395013,"timestamp":68274260871,"id":1,"tags":{"cpus":"16","platform":"win32","memory.freeMem":"5340778496","memory.totalMem":"***********","memory.heapSizeLimit":"**********","memory.rss":"183902208","memory.heapTotal":"100585472","memory.heapUsed":"74512568"},"startTime":1759410686594,"traceId":"bcec95d477f64203"},{"name":"compile-path","duration":2661000,"timestamp":75108452107,"id":6,"tags":{"trigger":"/"},"startTime":1759417520791,"traceId":"bcec95d477f64203"},{"name":"ensure-page","duration":2662335,"timestamp":75108451460,"id":5,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759417520791,"traceId":"bcec95d477f64203"}]
[{"name":"ensure-page","duration":19387,"timestamp":75111119068,"id":7,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759417523458,"traceId":"bcec95d477f64203"},{"name":"handle-request","duration":3151980,"timestamp":75108436728,"id":4,"tags":{"url":"/"},"startTime":1759417520776,"traceId":"bcec95d477f64203"},{"name":"memory-usage","duration":11,"timestamp":75111588825,"id":8,"parentId":4,"tags":{"url":"/","memory.rss":"700100608","memory.heapUsed":"104168704","memory.heapTotal":"140128256"},"startTime":1759417523928,"traceId":"bcec95d477f64203"},{"name":"ensure-page","duration":307030,"timestamp":75113209112,"id":10,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1759417525548,"traceId":"bcec95d477f64203"},{"name":"ensure-page","duration":55005,"timestamp":75113523359,"id":14,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1759417525862,"traceId":"bcec95d477f64203"},{"name":"handle-request","duration":679262,"timestamp":75113206838,"id":9,"tags":{"url":"/favicon.ico?favicon.0b3bf435.ico"},"startTime":1759417525546,"traceId":"bcec95d477f64203"},{"name":"memory-usage","duration":6,"timestamp":75113886150,"id":15,"parentId":9,"tags":{"url":"/favicon.ico?favicon.0b3bf435.ico","memory.rss":"864477184","memory.heapUsed":"124880152","memory.heapTotal":"148140032"},"startTime":1759417526225,"traceId":"bcec95d477f64203"},{"name":"compile-path","duration":723519,"timestamp":75113209551,"id":11,"tags":{"trigger":"/favicon.ico"},"startTime":1759417525549,"traceId":"bcec95d477f64203"}]
[{"name":"hot-reloader","duration":50,"timestamp":75254857370,"id":3,"tags":{"version":"15.5.4"},"startTime":1759417667190,"traceId":"e4900282df20f653"},{"name":"setup-dev-bundler","duration":386495,"timestamp":75254856013,"id":2,"parentId":1,"tags":{},"startTime":1759417667189,"traceId":"e4900282df20f653"},{"name":"start-dev-server","duration":1185969,"timestamp":75254183346,"id":1,"tags":{"cpus":"16","platform":"win32","memory.freeMem":"3103412224","memory.totalMem":"***********","memory.heapSizeLimit":"**********","memory.rss":"182231040","memory.heapTotal":"100585472","memory.heapUsed":"74951488"},"startTime":1759417666516,"traceId":"e4900282df20f653"},{"name":"compile-path","duration":2175470,"timestamp":75973495448,"id":6,"tags":{"trigger":"/"},"startTime":1759418385834,"traceId":"e4900282df20f653"},{"name":"ensure-page","duration":2176584,"timestamp":75973494732,"id":5,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759418385833,"traceId":"e4900282df20f653"}]
[{"name":"ensure-page","duration":17277,"timestamp":75975675349,"id":7,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759418388014,"traceId":"e4900282df20f653"},{"name":"handle-request","duration":2564258,"timestamp":75973485657,"id":4,"tags":{"url":"/"},"startTime":1759418385824,"traceId":"e4900282df20f653"},{"name":"memory-usage","duration":11,"timestamp":75976050009,"id":8,"parentId":4,"tags":{"url":"/","memory.rss":"715907072","memory.heapUsed":"104025704","memory.heapTotal":"140914688"},"startTime":1759418388389,"traceId":"e4900282df20f653"},{"name":"compile-path","duration":643413,"timestamp":75976523589,"id":11,"tags":{"trigger":"/api/scanner/strategies"},"startTime":1759418388862,"traceId":"e4900282df20f653"}]
[{"name":"hot-reloader","duration":52,"timestamp":76130465530,"id":3,"tags":{"version":"15.5.4"},"startTime":1759418542798,"traceId":"0e9cc8511db11c36"},{"name":"setup-dev-bundler","duration":380179,"timestamp":76130464036,"id":2,"parentId":1,"tags":{},"startTime":1759418542797,"traceId":"0e9cc8511db11c36"},{"name":"start-dev-server","duration":1075664,"timestamp":76129866747,"id":1,"tags":{"cpus":"16","platform":"win32","memory.freeMem":"2777464832","memory.totalMem":"***********","memory.heapSizeLimit":"**********","memory.rss":"181891072","memory.heapTotal":"100847616","memory.heapUsed":"74677264"},"startTime":1759418542199,"traceId":"0e9cc8511db11c36"},{"name":"compile-path","duration":4296299,"timestamp":76346506720,"id":6,"tags":{"trigger":"/api/scanner/strategies"},"startTime":1759418758838,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":4298481,"timestamp":***********,"id":5,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"}]
[{"name":"ensure-page","duration":50360,"timestamp":***********,"id":7,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":********,"timestamp":***********,"id":4,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=5"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":3,"timestamp":***********,"id":8,"parentId":4,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=5","memory.rss":"*********","memory.heapUsed":"********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":35439,"timestamp":***********,"id":10,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":24932,"timestamp":***********,"id":11,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":9473313,"timestamp":***********,"id":9,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=3"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":3,"timestamp":***********,"id":12,"parentId":9,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=3","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":31588,"timestamp":***********,"id":14,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":21716,"timestamp":***********,"id":15,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":********,"timestamp":***********,"id":13,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=10"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":4,"timestamp":***********,"id":16,"parentId":13,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=10","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":27974,"timestamp":***********,"id":18,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":26231,"timestamp":***********,"id":19,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":********,"timestamp":***********,"id":17,"tags":{"url":"/api/scanner/strategies?type=full&accountSize=100000&limit=20"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":5,"timestamp":***********,"id":20,"parentId":17,"tags":{"url":"/api/scanner/strategies?type=full&accountSize=100000&limit=20","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"compile-path","duration":1820982,"timestamp":***********,"id":23,"tags":{"trigger":"/"},"startTime":*************,"traceId":"0e9cc8511db11c36"}]
[{"name":"ensure-page","duration":63927,"timestamp":***********,"id":24,"parentId":3,"tags":{"inputPage":"/page"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":2181659,"timestamp":***********,"id":21,"tags":{"url":"/"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":3,"timestamp":***********,"id":25,"parentId":21,"tags":{"url":"/","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":40171,"timestamp":***********,"id":27,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":24468,"timestamp":***********,"id":28,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":********,"timestamp":***********,"id":26,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":3,"timestamp":***********,"id":29,"parentId":26,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":25525,"timestamp":***********,"id":31,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":22270,"timestamp":***********,"id":32,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":********,"timestamp":***********,"id":30,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=10"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":2,"timestamp":***********,"id":33,"parentId":30,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=10","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"client-hmr-latency","duration":99000,"timestamp":***********,"id":34,"parentId":3,"tags":{"updatedModules":["[project]/SwingTrader-AI-Package/src/components/StrategyScanner.tsx"],"page":"/","isPageHidden":true},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"client-hmr-latency","duration":81000,"timestamp":***********,"id":35,"parentId":3,"tags":{"updatedModules":["[project]/SwingTrader-AI-Package/src/components/StrategyScanner.tsx"],"page":"/","isPageHidden":true},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"client-hmr-latency","duration":88000,"timestamp":***********,"id":36,"parentId":3,"tags":{"updatedModules":["[project]/SwingTrader-AI-Package/src/components/StrategyScanner.tsx"],"page":"/","isPageHidden":true},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"client-hmr-latency","duration":38000,"timestamp":76955180255,"id":39,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1759419367591,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":41644,"timestamp":76955217804,"id":38,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759419367556,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":26410,"timestamp":76955261993,"id":40,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759419367600,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":181298,"timestamp":76955211610,"id":37,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1759419367550,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":5,"timestamp":76955392950,"id":41,"parentId":37,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"837324800","memory.heapUsed":"164988288","memory.heapTotal":"185061376"},"startTime":1759419367731,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":31365,"timestamp":76955406124,"id":43,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759419367744,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":45526,"timestamp":76955439980,"id":44,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759419367778,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":111632,"timestamp":76955403269,"id":42,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1759419367742,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":4,"timestamp":76955514943,"id":45,"parentId":42,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"833142784","memory.heapUsed":"143445544","memory.heapTotal":"163430400"},"startTime":1759419367853,"traceId":"0e9cc8511db11c36"},{"name":"client-hmr-latency","duration":24000,"timestamp":76978605269,"id":48,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1759419391008,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":37451,"timestamp":76978635242,"id":47,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759419390974,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":31773,"timestamp":76978676001,"id":49,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759419391014,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":216166,"timestamp":76978628987,"id":46,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1759419390967,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":7,"timestamp":76978845247,"id":50,"parentId":46,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"821202944","memory.heapUsed":"157791224","memory.heapTotal":"180903936"},"startTime":1759419391184,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":25130,"timestamp":76978858604,"id":52,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759419391197,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":24303,"timestamp":76978886297,"id":53,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759419391225,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":72602,"timestamp":76978856547,"id":51,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1759419391195,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":5,"timestamp":76978929211,"id":54,"parentId":51,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"824393728","memory.heapUsed":"160419800","memory.heapTotal":"180903936"},"startTime":1759419391268,"traceId":"0e9cc8511db11c36"},{"name":"client-hmr-latency","duration":91000,"timestamp":77061981706,"id":55,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1759419474434,"traceId":"0e9cc8511db11c36"},{"name":"client-hmr-latency","duration":88000,"timestamp":77073997429,"id":56,"parentId":3,"tags":{"updatedModules":["[project]/SwingTrader-AI-Package/src/components/StrategyScanner.tsx"],"page":"/","isPageHidden":true},"startTime":1759419486511,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":25357,"timestamp":77089009590,"id":58,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":1759419501349,"traceId":"0e9cc8511db11c36"},{"name":"ensure-page","duration":22719,"timestamp":77089037964,"id":59,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"client-hmr-latency","duration":78000,"timestamp":***********,"id":60,"parentId":3,"tags":{"updatedModules":["[project]/SwingTrader-AI-Package/src/components/StrategyScanner.tsx"],"page":"/","isPageHidden":true},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":********,"timestamp":***********,"id":57,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":3,"timestamp":***********,"id":61,"parentId":57,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"client-hmr-latency","duration":83000,"timestamp":***********,"id":62,"parentId":3,"tags":{"updatedModules":["[project]/SwingTrader-AI-Package/src/components/StrategyScanner.tsx"],"page":"/","isPageHidden":true},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"client-hmr-latency","duration":91000,"timestamp":***********,"id":63,"parentId":3,"tags":{"updatedModules":["[project]/SwingTrader-AI-Package/src/components/StrategyScanner.tsx","[project]/SwingTrader-AI-Package/node_modules/lucide-react/dist/esm/icons/briefcase.js"],"page":"/","isPageHidden":true},"startTime":*************,"traceId":"0e9cc8511db11c36"},{"name":"compile-path","duration":54821,"timestamp":***********,"id":66,"tags":{"trigger":"/"},"startTime":*************,"traceId":"0e9cc8511db11c36"}]
[{"name":"ensure-page","duration":28214,"timestamp":77241671252,"id":67,"parentId":3,"tags":{"inputPage":"/page"},"startTime":*********4005,"traceId":"0e9cc8511db11c36"},{"name":"handle-request","duration":185765,"timestamp":77241609421,"id":64,"tags":{"url":"/?_rsc=fsnpp"},"startTime":*********3943,"traceId":"0e9cc8511db11c36"},{"name":"memory-usage","duration":4,"timestamp":77241795229,"id":68,"parentId":64,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"903213056","memory.heapUsed":"221213936","memory.heapTotal":"232882176"},"startTime":*********4129,"traceId":"0e9cc8511db11c36"},{"name":"client-hmr-latency","duration":160000,"timestamp":77241497832,"id":69,"parentId":3,"tags":{"updatedModules":["[project]/SwingTrader-AI-Package/src/components/StrategyScanner.tsx","[project]/SwingTrader-AI-Package/src/components/TradingCard.tsx","[project]/SwingTrader-AI-Package/node_modules/lucide-react/dist/esm/icons/play.js","[project]/SwingTrader-AI-Package/node_modules/lucide-react/dist/esm/icons/circle-check-big.js","[project]/SwingTrader-AI-Package/node_modules/lucide-react/dist/esm/icons/chevron-down.js","[project]/SwingTrader-AI-Package/node_modules/lucide-react/dist/esm/icons/chevron-up.js"],"page":"/","isPageHidden":true},"startTime":*********4159,"traceId":"0e9cc8511db11c36"},{"name":"compile-path","duration":327650,"timestamp":82198901117,"id":72,"tags":{"trigger":"/api/analysis/ai-setup"},"startTime":1759424611240,"traceId":"0e9cc8511db11c36"}]
[{"name":"compile-path","duration":2600730,"timestamp":82199260285,"id":74,"tags":{"trigger":"/_error"},"startTime":1759424611599,"traceId":"0e9cc8511db11c36"}]
[{"name":"hot-reloader","duration":54,"timestamp":82298728720,"id":3,"tags":{"version":"15.5.4"},"startTime":1759424711056,"traceId":"ae00013a3c71c69c"},{"name":"setup-dev-bundler","duration":398041,"timestamp":82298727335,"id":2,"parentId":1,"tags":{},"startTime":1759424711054,"traceId":"ae00013a3c71c69c"},{"name":"start-dev-server","duration":1291693,"timestamp":82297983884,"id":1,"tags":{"cpus":"16","platform":"win32","memory.freeMem":"3514306560","memory.totalMem":"***********","memory.heapSizeLimit":"**********","memory.rss":"181833728","memory.heapTotal":"101412864","memory.heapUsed":"78559992"},"startTime":1759424710311,"traceId":"ae00013a3c71c69c"},{"name":"compile-path","duration":2116317,"timestamp":82615226653,"id":6,"tags":{"trigger":"/"},"startTime":1759425027565,"traceId":"ae00013a3c71c69c"},{"name":"ensure-page","duration":2117697,"timestamp":82615225761,"id":5,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759425027564,"traceId":"ae00013a3c71c69c"}]
[{"name":"ensure-page","duration":17076,"timestamp":82617347076,"id":7,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1759425029686,"traceId":"ae00013a3c71c69c"},{"name":"handle-request","duration":2527956,"timestamp":82615214113,"id":4,"tags":{"url":"/"},"startTime":1759425027553,"traceId":"ae00013a3c71c69c"},{"name":"memory-usage","duration":6,"timestamp":82617742143,"id":8,"parentId":4,"tags":{"url":"/","memory.rss":"702836736","memory.heapUsed":"104182112","memory.heapTotal":"140779520"},"startTime":1759425030081,"traceId":"ae00013a3c71c69c"},{"name":"compile-path","duration":495606,"timestamp":***********,"id":11,"tags":{"trigger":"/api/scanner/strategies"},"startTime":*************,"traceId":"ae00013a3c71c69c"}]
[{"name":"ensure-page","duration":27581,"timestamp":***********,"id":12,"parentId":3,"tags":{"inputPage":"/api/scanner/strategies/route"},"startTime":*************,"traceId":"ae00013a3c71c69c"},{"name":"handle-request","duration":********,"timestamp":***********,"id":9,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20"},"startTime":*************,"traceId":"ae00013a3c71c69c"},{"name":"memory-usage","duration":8,"timestamp":***********,"id":13,"parentId":9,"tags":{"url":"/api/scanner/strategies?type=quick&accountSize=100000&limit=20","memory.rss":"*********","memory.heapUsed":"*********","memory.heapTotal":"*********"},"startTime":*************,"traceId":"ae00013a3c71c69c"},{"name":"compile-path","duration":494561,"timestamp":***********,"id":16,"tags":{"trigger":"/api/analysis/ai-setup"},"startTime":*************,"traceId":"ae00013a3c71c69c"}]
[{"name":"hot-reloader","duration":53,"timestamp":***********,"id":3,"tags":{"version":"15.5.4"},"startTime":*************,"traceId":"dc929d20d3e8d773"},{"name":"setup-dev-bundler","duration":373595,"timestamp":***********,"id":2,"parentId":1,"tags":{},"startTime":*************,"traceId":"dc929d20d3e8d773"},{"name":"start-dev-server","duration":1055094,"timestamp":***********,"id":1,"tags":{"cpus":"16","platform":"win32","memory.freeMem":"**********","memory.totalMem":"***********","memory.heapSizeLimit":"**********","memory.rss":"*********","memory.heapTotal":"*********","memory.heapUsed":"********"},"startTime":*************,"traceId":"dc929d20d3e8d773"},{"name":"compile-path","duration":2631382,"timestamp":93468054293,"id":6,"tags":{"trigger":"/api/analysis/ai-setup"},"startTime":1759435880393,"traceId":"dc929d20d3e8d773"},{"name":"ensure-page","duration":2637576,"timestamp":93468053636,"id":5,"parentId":3,"tags":{"inputPage":"/api/analysis/ai-setup/route"},"startTime":1759435880392,"traceId":"dc929d20d3e8d773"}]
[{"name":"ensure-page","duration":222927,"timestamp":93470702222,"id":7,"parentId":3,"tags":{"inputPage":"/api/analysis/ai-setup/route"},"startTime":1759435883041,"traceId":"dc929d20d3e8d773"},{"name":"handle-request","duration":3827159,"timestamp":93468041250,"id":4,"tags":{"url":"/api/analysis/ai-setup"},"startTime":1759435880380,"traceId":"dc929d20d3e8d773"},{"name":"memory-usage","duration":6,"timestamp":93471868838,"id":8,"parentId":4,"tags":{"url":"/api/analysis/ai-setup","memory.rss":"560848896","memory.heapUsed":"113582792","memory.heapTotal":"143319040"},"startTime":1759435884207,"traceId":"dc929d20d3e8d773"},{"name":"compile-path","duration":272263,"timestamp":93490443096,"id":11,"tags":{"trigger":"/api/paper-trading/execute"},"startTime":1759435902782,"traceId":"dc929d20d3e8d773"}]

module.exports = [
"[project]/SwingTrader-AI-Package/.next-internal/server/app/api/analysis/ai-setup/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/SwingTrader-AI-Package/src/lib/marketDataEnrichment.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * Market Data Enrichment Service
 * Provides real-time market data to enhance AI analysis with specific, quantifiable information
 */ __turbopack_context__.s([
    "MarketDataEnrichmentService",
    ()=>MarketDataEnrichmentService,
    "marketDataService",
    ()=>marketDataService
]);
class MarketDataEnrichmentService {
    /**
   * Get enriched market data for a symbol
   */ async getEnrichedData(symbol, sector) {
        // In production, this would make real API calls to data providers
        // For now, we'll simulate realistic data based on the symbol
        const enrichedData = {
            symbol,
            sector,
            recentEarnings: await this.getEarningsData(symbol),
            analystActions: await this.getAnalystActions(symbol),
            sectorPerformance: await this.getSectorPerformance(sector),
            volatilityMetrics: await this.getVolatilityData(symbol),
            newsEvents: await this.getNewsEvents(symbol),
            economicEvents: await this.getEconomicEvents(sector),
            technicalLevels: await this.getTechnicalLevels(symbol)
        };
        return enrichedData;
    }
    async getEarningsData(symbol) {
        // Simulate recent earnings data
        // In production: connect to earnings calendar API
        const earningsMap = {
            'NVDA': {
                reportDate: '2024-11-21',
                actualEPS: 2.45,
                estimatedEPS: 2.30,
                beat: true,
                beatAmount: 0.15,
                revenue: 35.1e9,
                revenueEstimate: 33.2e9,
                nextEarningsDate: '2025-02-20'
            },
            'AMD': {
                reportDate: '2024-10-29',
                actualEPS: 0.92,
                estimatedEPS: 0.88,
                beat: true,
                beatAmount: 0.04,
                revenue: 6.8e9,
                revenueEstimate: 6.7e9,
                nextEarningsDate: '2025-01-28'
            }
        };
        return earningsMap[symbol];
    }
    async getAnalystActions(symbol) {
        // Simulate recent analyst actions
        // In production: connect to analyst research APIs
        const analystMap = {
            'NVDA': [
                {
                    firm: 'Goldman Sachs',
                    action: 'upgrade',
                    rating: 'Buy',
                    priceTarget: 250,
                    previousTarget: 220,
                    date: '2024-11-22',
                    analyst: 'Toshiya Hari'
                },
                {
                    firm: 'Morgan Stanley',
                    action: 'maintain',
                    rating: 'Overweight',
                    priceTarget: 240,
                    date: '2024-11-20',
                    analyst: 'Joseph Moore'
                }
            ],
            'AMD': [
                {
                    firm: 'Bank of America',
                    action: 'upgrade',
                    rating: 'Buy',
                    priceTarget: 180,
                    previousTarget: 165,
                    date: '2024-10-30',
                    analyst: 'Vivek Arya'
                }
            ]
        };
        return analystMap[symbol] || [];
    }
    async getSectorPerformance(sector) {
        // Simulate sector performance data
        // In production: connect to sector ETF data
        const sectorMap = {
            'Technology': {
                sectorETF: 'XLK',
                sectorReturn1W: 2.8,
                sectorReturn1M: 8.5,
                spyReturn1W: 1.2,
                spyReturn1M: 4.3,
                relativeStrength: 1.23,
                ranking: 2,
                totalSectors: 11
            },
            'Materials': {
                sectorETF: 'XLB',
                sectorReturn1W: 3.2,
                sectorReturn1M: 6.8,
                spyReturn1W: 1.2,
                spyReturn1M: 4.3,
                relativeStrength: 1.58,
                ranking: 1,
                totalSectors: 11
            }
        };
        return sectorMap[sector] || {
            sectorETF: 'SPY',
            sectorReturn1W: 1.2,
            sectorReturn1M: 4.3,
            spyReturn1W: 1.2,
            spyReturn1M: 4.3,
            relativeStrength: 1.0,
            ranking: 6,
            totalSectors: 11
        };
    }
    async getVolatilityData(symbol) {
        // Simulate volatility metrics
        // In production: connect to options data providers
        return {
            impliedVolatility30d: 45,
            impliedVolatility6m: 38,
            historicalVolatility30d: 42,
            historicalVolatility6m: 35,
            gapFrequency: 15,
            averageGapSize: 3.2
        };
    }
    async getNewsEvents(symbol) {
        // Simulate recent news events
        // In production: connect to news APIs like Bloomberg, Reuters
        return [
            {
                headline: `${symbol} announces strategic partnership with major cloud provider`,
                source: 'Reuters',
                date: '2024-11-25',
                sentiment: 'positive',
                relevanceScore: 0.85
            }
        ];
    }
    async getEconomicEvents(sector) {
        // Simulate relevant economic events
        // In production: connect to economic calendar APIs
        return [
            {
                event: 'Federal Reserve Interest Rate Decision',
                date: '2024-12-18',
                impact: 'high',
                actual: undefined,
                forecast: -0.25,
                previous: 0,
                unit: 'percentage points'
            }
        ];
    }
    async getTechnicalLevels(symbol) {
        // Simulate technical analysis levels
        // In production: calculate from historical price data
        return {
            support: [
                {
                    price: 180,
                    strength: 0.85,
                    lastTest: '2024-11-15'
                },
                {
                    price: 175,
                    strength: 0.72,
                    lastTest: '2024-10-28'
                }
            ],
            resistance: [
                {
                    price: 195,
                    strength: 0.78,
                    lastTest: '2024-11-20'
                },
                {
                    price: 200,
                    strength: 0.65,
                    lastTest: '2024-11-10'
                }
            ],
            vwap: 187.5,
            sma20: 185.2,
            sma50: 178.8,
            sma200: 165.4,
            volumeProfile: [
                {
                    price: 185,
                    volume: 2500000
                },
                {
                    price: 190,
                    volume: 1800000
                }
            ]
        };
    }
}
const marketDataService = new MarketDataEnrichmentService();
}),
"[project]/SwingTrader-AI-Package/src/app/api/analysis/ai-setup/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$marketDataEnrichment$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/src/lib/marketDataEnrichment.ts [app-route] (ecmascript)");
;
;
;
// REAL OpenAI API key - hardcoded for seamless transfer between computers
const apiKey = '********************************************************************************************************************************************************************';
console.log('🔑 Using hardcoded API Key prefix:', apiKey.substring(0, 15));
console.log('🔑 Source: Hardcoded for seamless computer transfer');
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: apiKey
});
async function POST(request) {
    try {
        console.log('🚀 AI Analysis endpoint called');
        console.log('🔑 Using hardcoded API key:', apiKey.substring(0, 15));
        console.log('🔑 API key length:', apiKey.length);
        const { scanResult } = await request.json();
        if (!scanResult) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Scan result is required'
            }, {
                status: 400
            });
        }
        // Prepare context for AI analysis
        const setup = scanResult.overnightSetup || scanResult.breakoutSetup;
        if (!setup) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No trading setup found'
            }, {
                status: 400
            });
        }
        const strategyType = scanResult.overnightSetup ? 'overnight momentum' : 'technical breakout';
        const currentPrice = scanResult.quote?.price || setup?.currentPrice || 0;
        const changePercent = scanResult.quote?.changePercent || setup?.momentum || 0;
        // Get enriched market data for more specific analysis
        const enrichedData = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$marketDataEnrichment$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["marketDataService"].getEnrichedData(scanResult.symbol, scanResult.sector);
        const prompt = `You are a professional swing trader providing DATA-DRIVEN analysis for ${scanResult.symbol} (${scanResult.name}).

CRITICAL REQUIREMENT: Provide SPECIFIC, QUANTIFIABLE data points. NO generic statements allowed.

CURRENT SETUP DATA:
- Symbol: ${scanResult.symbol} (${scanResult.name})
- Sector: ${scanResult.sector}
- Strategy: ${strategyType}
- Current Price: $${currentPrice}
- Daily Change: ${changePercent.toFixed(2)}%
- Entry: $${setup.entryPrice}
- Stop Loss: $${setup.stopLoss}
- Target: $${setup.targets[0]}
- Setup Confidence: ${setup.confidence}%
- Overall Score: ${scanResult.overallScore}/100
- Alerts: ${scanResult.alerts?.join(', ') || 'None'}
- Risk Warnings: ${scanResult.riskWarnings?.join(', ') || 'None'}

ENRICHED MARKET DATA:
${enrichedData.recentEarnings ? `
RECENT EARNINGS:
- Report Date: ${enrichedData.recentEarnings.reportDate}
- Actual EPS: $${enrichedData.recentEarnings.actualEPS} vs Est: $${enrichedData.recentEarnings.estimatedEPS}
- Beat by: $${enrichedData.recentEarnings.beatAmount} (${enrichedData.recentEarnings.beat ? 'BEAT' : 'MISS'})
- Revenue: $${(enrichedData.recentEarnings.revenue / 1e9).toFixed(1)}B vs Est: $${(enrichedData.recentEarnings.revenueEstimate / 1e9).toFixed(1)}B
- Next Earnings: ${enrichedData.recentEarnings.nextEarningsDate}
` : ''}
${enrichedData.analystActions && enrichedData.analystActions.length > 0 ? `
RECENT ANALYST ACTIONS:
${enrichedData.analystActions.map((action)=>`- ${action.firm}: ${action.action.toUpperCase()} to ${action.rating}, $${action.priceTarget} target (${action.date})`).join('\n')}
` : ''}
SECTOR PERFORMANCE:
- Sector ETF (${enrichedData.sectorPerformance?.sectorETF}): +${enrichedData.sectorPerformance?.sectorReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.sectorReturn1M.toFixed(1)}% (1M)
- S&P 500: +${enrichedData.sectorPerformance?.spyReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.spyReturn1M.toFixed(1)}% (1M)
- Relative Strength: ${enrichedData.sectorPerformance?.relativeStrength.toFixed(2)}x
- Sector Ranking: #${enrichedData.sectorPerformance?.ranking} of ${enrichedData.sectorPerformance?.totalSectors}

VOLATILITY METRICS:
- 30-day IV: ${enrichedData.volatilityMetrics?.impliedVolatility30d}% vs 6M avg: ${enrichedData.volatilityMetrics?.impliedVolatility6m}%
- Gap Frequency: ${enrichedData.volatilityMetrics?.gapFrequency}% of days have >2% gaps
- Average Gap Size: ${enrichedData.volatilityMetrics?.averageGapSize}%

TECHNICAL LEVELS:
- Support: ${enrichedData.technicalLevels?.support.map((s)=>`$${s.price} (strength: ${(s.strength * 100).toFixed(0)}%, last test: ${s.lastTest})`).join(', ')}
- Resistance: ${enrichedData.technicalLevels?.resistance.map((r)=>`$${r.price} (strength: ${(r.strength * 100).toFixed(0)}%, last test: ${r.lastTest})`).join(', ')}
- VWAP: $${enrichedData.technicalLevels?.vwap}
- SMA20: $${enrichedData.technicalLevels?.sma20}, SMA50: $${enrichedData.technicalLevels?.sma50}

MANDATORY ANALYSIS REQUIREMENTS:

**MARKET CATALYSTS** - Must include SPECIFIC data:
- Recent earnings with actual numbers and dates (e.g., "Q3 EPS $2.45 vs $2.30 est, reported Nov 21")
- Analyst actions with firm names and targets (e.g., "Goldman upgraded to Buy, $250 target on Nov 22")
- Sector performance vs benchmarks with exact % (e.g., "Materials (XLB) +3.2% vs S&P +1.1% this week")
- Recent news with dates and sources (e.g., "Reuters Nov 20: Company announced $2B contract")
- Economic data affecting stock (e.g., "Fed cut rates 0.25% yesterday, benefits growth stocks")

**RISK ASSESSMENT** - Must include QUANTIFIABLE risks:
- Volatility metrics (e.g., "30-day IV at 45% vs 6-month avg 32%")
- Gap risk stats (e.g., "Stock gaps >2% on 15% of trading days past 3 months")
- Support/resistance with context (e.g., "$180 support held 3x in Oct, volume spike at $185")
- Market conditions with probabilities (e.g., "Fed meeting Dec 18 has 70% chance of rate cut")

**KEY LEVELS** - Must be EXACT price points:
- Support levels with historical significance
- Resistance that matters for this trade
- VWAP and moving averages currently relevant
- Volume profile levels

EXAMPLES:
✅ GOOD: "NVDA beat Q3 EPS by $0.15 ($2.45 vs $2.30), reported Nov 21. Goldman upgraded to $250 target Nov 22."
❌ BAD: "Market sentiment is bullish" or "sector performing well"

Provide a comprehensive professional trading analysis in markdown format with:

## 📊 TECHNICAL SETUP ANALYSIS
Detailed explanation of the setup with specific price levels and indicators

## 🚀 MARKET CATALYSTS
List specific catalysts with dates, numbers, and sources

## ⚠️ RISK ASSESSMENT
Quantified risks with historical data and probabilities

## 🎯 KEY PRICE LEVELS
Exact price levels with historical significance

## ⏰ TRADE TIMEFRAME
Specific timeframe with reasoning

## 🎯 CONFIDENCE LEVEL
Overall confidence (1-100) with justification

Use REAL DATA POINTS and specific numbers throughout.`;
        console.log('🤖 Making REAL OpenAI API call...');
        console.log('🔑 API Key being used:', apiKey.substring(0, 20) + '...');
        console.log('📝 Prompt length:', prompt.length);
        const completion = await openai.chat.completions.create({
            model: "gpt-4o-mini",
            messages: [
                {
                    role: "system",
                    content: "You are a professional swing trading analyst providing DATA-DRIVEN analysis. You must include specific numbers, dates, sources, and quantifiable metrics. Never use generic statements. Focus on actionable intelligence with real market data."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.2,
            max_tokens: 1500
        });
        console.log('✅ REAL OpenAI API call successful');
        console.log('📊 Response length:', completion.choices[0]?.message?.content?.length || 0);
        const aiResponse = completion.choices[0]?.message?.content;
        console.log('🎯 AI Response received:', aiResponse ? 'YES' : 'NO');
        console.log('📄 AI Response preview:', aiResponse?.substring(0, 200) + '...');
        if (!aiResponse) {
            throw new Error('No response from AI');
        }
        // Use the AI response directly as professional analysis
        const analysis = {
            setupExplanation: aiResponse,
            catalysts: [
                `Professional AI analysis for ${scanResult.symbol}`
            ],
            riskAssessment: 'See detailed analysis above',
            keyLevels: [
                `Entry: $${setup.entryPrice}`,
                `Stop: $${setup.stopLoss}`,
                `Target: $${setup.targets[0]}`
            ],
            timeframe: '3-10 trading days',
            confidence: scanResult.overallScore
        };
        console.log('✅ Analysis object created successfully');
        const fullAnalysis = {
            symbol: scanResult.symbol,
            setupExplanation: analysis.setupExplanation || 'Setup analysis not available',
            catalysts: analysis.catalysts || [],
            riskAssessment: analysis.riskAssessment || 'Standard risks apply',
            keyLevels: analysis.keyLevels || [],
            timeframe: analysis.timeframe || '3-10 days',
            confidence: analysis.confidence || Math.round(setup.confidence),
            lastUpdated: new Date().toISOString()
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(fullAnalysis);
    } catch (error) {
        console.error('❌ AI Analysis error:', error);
        console.error('❌ Error details:', {
            message: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
            apiKeyPrefix: apiKey.substring(0, 15)
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate AI analysis',
            details: error instanceof Error ? error.message : 'Unknown error',
            apiKeyStatus: ("TURBOPACK compile-time truthy", 1) ? 'Present' : "TURBOPACK unreachable"
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__ec4c7028._.js.map
module.exports = [
"[project]/SwingTrader-AI-Package/.next-internal/server/app/api/analysis/ai-setup/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/SwingTrader-AI-Package/src/lib/marketDataEnrichment.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * Market Data Enrichment Service
 * Provides real-time market data to enhance AI analysis with specific, quantifiable information
 */ __turbopack_context__.s([
    "MarketDataEnrichmentService",
    ()=>MarketDataEnrichmentService,
    "marketDataService",
    ()=>marketDataService
]);
class MarketDataEnrichmentService {
    /**
   * Get enriched market data for a symbol
   */ async getEnrichedData(symbol, sector) {
        // In production, this would make real API calls to data providers
        // For now, we'll simulate realistic data based on the symbol
        const enrichedData = {
            symbol,
            sector,
            recentEarnings: await this.getEarningsData(symbol),
            analystActions: await this.getAnalystActions(symbol),
            sectorPerformance: await this.getSectorPerformance(sector),
            volatilityMetrics: await this.getVolatilityData(symbol),
            newsEvents: await this.getNewsEvents(symbol),
            economicEvents: await this.getEconomicEvents(sector),
            technicalLevels: await this.getTechnicalLevels(symbol)
        };
        return enrichedData;
    }
    async getEarningsData(symbol) {
        // Simulate recent earnings data
        // In production: connect to earnings calendar API
        const earningsMap = {
            'NVDA': {
                reportDate: '2024-11-21',
                actualEPS: 2.45,
                estimatedEPS: 2.30,
                beat: true,
                beatAmount: 0.15,
                revenue: 35.1e9,
                revenueEstimate: 33.2e9,
                nextEarningsDate: '2025-02-20'
            },
            'AMD': {
                reportDate: '2024-10-29',
                actualEPS: 0.92,
                estimatedEPS: 0.88,
                beat: true,
                beatAmount: 0.04,
                revenue: 6.8e9,
                revenueEstimate: 6.7e9,
                nextEarningsDate: '2025-01-28'
            }
        };
        return earningsMap[symbol];
    }
    async getAnalystActions(symbol) {
        // Simulate recent analyst actions
        // In production: connect to analyst research APIs
        const analystMap = {
            'NVDA': [
                {
                    firm: 'Goldman Sachs',
                    action: 'upgrade',
                    rating: 'Buy',
                    priceTarget: 250,
                    previousTarget: 220,
                    date: '2024-11-22',
                    analyst: 'Toshiya Hari'
                },
                {
                    firm: 'Morgan Stanley',
                    action: 'maintain',
                    rating: 'Overweight',
                    priceTarget: 240,
                    date: '2024-11-20',
                    analyst: 'Joseph Moore'
                }
            ],
            'AMD': [
                {
                    firm: 'Bank of America',
                    action: 'upgrade',
                    rating: 'Buy',
                    priceTarget: 180,
                    previousTarget: 165,
                    date: '2024-10-30',
                    analyst: 'Vivek Arya'
                }
            ]
        };
        return analystMap[symbol] || [];
    }
    async getSectorPerformance(sector) {
        // Simulate sector performance data
        // In production: connect to sector ETF data
        const sectorMap = {
            'Technology': {
                sectorETF: 'XLK',
                sectorReturn1W: 2.8,
                sectorReturn1M: 8.5,
                spyReturn1W: 1.2,
                spyReturn1M: 4.3,
                relativeStrength: 1.23,
                ranking: 2,
                totalSectors: 11
            },
            'Materials': {
                sectorETF: 'XLB',
                sectorReturn1W: 3.2,
                sectorReturn1M: 6.8,
                spyReturn1W: 1.2,
                spyReturn1M: 4.3,
                relativeStrength: 1.58,
                ranking: 1,
                totalSectors: 11
            }
        };
        return sectorMap[sector] || {
            sectorETF: 'SPY',
            sectorReturn1W: 1.2,
            sectorReturn1M: 4.3,
            spyReturn1W: 1.2,
            spyReturn1M: 4.3,
            relativeStrength: 1.0,
            ranking: 6,
            totalSectors: 11
        };
    }
    async getVolatilityData(symbol) {
        // Simulate volatility metrics
        // In production: connect to options data providers
        return {
            impliedVolatility30d: 45,
            impliedVolatility6m: 38,
            historicalVolatility30d: 42,
            historicalVolatility6m: 35,
            gapFrequency: 15,
            averageGapSize: 3.2
        };
    }
    async getNewsEvents(symbol) {
        // Simulate recent news events
        // In production: connect to news APIs like Bloomberg, Reuters
        return [
            {
                headline: `${symbol} announces strategic partnership with major cloud provider`,
                source: 'Reuters',
                date: '2024-11-25',
                sentiment: 'positive',
                relevanceScore: 0.85
            }
        ];
    }
    async getEconomicEvents(sector) {
        // Simulate relevant economic events
        // In production: connect to economic calendar APIs
        return [
            {
                event: 'Federal Reserve Interest Rate Decision',
                date: '2024-12-18',
                impact: 'high',
                actual: undefined,
                forecast: -0.25,
                previous: 0,
                unit: 'percentage points'
            }
        ];
    }
    async getTechnicalLevels(symbol) {
        // Simulate technical analysis levels
        // In production: calculate from historical price data
        return {
            support: [
                {
                    price: 180,
                    strength: 0.85,
                    lastTest: '2024-11-15'
                },
                {
                    price: 175,
                    strength: 0.72,
                    lastTest: '2024-10-28'
                }
            ],
            resistance: [
                {
                    price: 195,
                    strength: 0.78,
                    lastTest: '2024-11-20'
                },
                {
                    price: 200,
                    strength: 0.65,
                    lastTest: '2024-11-10'
                }
            ],
            vwap: 187.5,
            sma20: 185.2,
            sma50: 178.8,
            sma200: 165.4,
            volumeProfile: [
                {
                    price: 185,
                    volume: 2500000
                },
                {
                    price: 190,
                    volume: 1800000
                }
            ]
        };
    }
}
const marketDataService = new MarketDataEnrichmentService();
}),
"[project]/SwingTrader-AI-Package/src/app/api/analysis/ai-setup/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$marketDataEnrichment$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/src/lib/marketDataEnrichment.ts [app-route] (ecmascript)");
;
;
;
// REAL OpenAI API key - hardcoded for seamless transfer between computers
const apiKey = '********************************************************************************************************************************************************************';
console.log('🔑 Using hardcoded API Key prefix:', apiKey.substring(0, 15));
console.log('🔑 Source: Hardcoded for seamless computer transfer');
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: apiKey
});
async function POST(request) {
    try {
        console.log('🚀 AI Analysis endpoint called');
        console.log('🔑 Using hardcoded API key:', apiKey.substring(0, 15));
        console.log('🔑 API key length:', apiKey.length);
        const { scanResult } = await request.json();
        if (!scanResult) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Scan result is required'
            }, {
                status: 400
            });
        }
        // Prepare context for AI analysis
        const setup = scanResult.overnightSetup || scanResult.breakoutSetup;
        if (!setup) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No trading setup found'
            }, {
                status: 400
            });
        }
        const strategyType = scanResult.overnightSetup ? 'overnight momentum' : 'technical breakout';
        const currentPrice = scanResult.quote?.price || setup?.currentPrice || 0;
        const changePercent = scanResult.quote?.changePercent || setup?.momentum || 0;
        // Get enriched market data for more specific analysis
        const enrichedData = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$marketDataEnrichment$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["marketDataService"].getEnrichedData(scanResult.symbol, scanResult.sector);
        const prompt = `You are a senior equity research analyst providing institutional-grade swing trading analysis for ${scanResult.symbol}.

MANDATORY REQUIREMENTS - Every statement must include:
1. EXACT DATES, NUMBERS, and PERCENTAGES
2. SPECIFIC SOURCE ATTRIBUTION
3. VERIFIABLE DATA POINTS
4. PROBABILITY-BASED ASSESSMENTS

CURRENT SETUP DATA:
- Symbol: ${scanResult.symbol} (${scanResult.name || 'N/A'})
- Sector: ${scanResult.sector || 'Technology'}
- Strategy: ${strategyType}
- Current Price: $${currentPrice}
- Daily Change: ${changePercent.toFixed(2)}%
- Entry: $${setup.entryPrice}
- Stop Loss: $${setup.stopLoss} (Risk: ${((setup.entryPrice - setup.stopLoss) / setup.entryPrice * 100).toFixed(1)}%)
- Target: $${setup.targets[0]} (Reward: ${((setup.targets[0] - setup.entryPrice) / setup.entryPrice * 100).toFixed(1)}%)
- Risk/Reward Ratio: ${((setup.targets[0] - setup.entryPrice) / (setup.entryPrice - setup.stopLoss)).toFixed(2)}:1
- Setup Confidence: ${setup.confidence}%
- Overall Score: ${scanResult.overallScore}/100

ENRICHED MARKET DATA:
${enrichedData.recentEarnings ? `
RECENT EARNINGS:
- Report Date: ${enrichedData.recentEarnings.reportDate}
- Actual EPS: $${enrichedData.recentEarnings.actualEPS} vs Est: $${enrichedData.recentEarnings.estimatedEPS}
- Beat by: $${enrichedData.recentEarnings.beatAmount} (${enrichedData.recentEarnings.beat ? 'BEAT' : 'MISS'})
- Revenue: $${(enrichedData.recentEarnings.revenue / 1e9).toFixed(1)}B vs Est: $${(enrichedData.recentEarnings.revenueEstimate / 1e9).toFixed(1)}B
- Next Earnings: ${enrichedData.recentEarnings.nextEarningsDate}
` : ''}
${enrichedData.analystActions && enrichedData.analystActions.length > 0 ? `
RECENT ANALYST ACTIONS:
${enrichedData.analystActions.map((action)=>`- ${action.firm}: ${action.action.toUpperCase()} to ${action.rating}, $${action.priceTarget} target (${action.date})`).join('\n')}
` : ''}
SECTOR PERFORMANCE:
- Sector ETF (${enrichedData.sectorPerformance?.sectorETF}): +${enrichedData.sectorPerformance?.sectorReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.sectorReturn1M.toFixed(1)}% (1M)
- S&P 500: +${enrichedData.sectorPerformance?.spyReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.spyReturn1M.toFixed(1)}% (1M)
- Relative Strength: ${enrichedData.sectorPerformance?.relativeStrength.toFixed(2)}x
- Sector Ranking: #${enrichedData.sectorPerformance?.ranking} of ${enrichedData.sectorPerformance?.totalSectors}

VOLATILITY METRICS:
- 30-day IV: ${enrichedData.volatilityMetrics?.impliedVolatility30d}% vs 6M avg: ${enrichedData.volatilityMetrics?.impliedVolatility6m}%
- Gap Frequency: ${enrichedData.volatilityMetrics?.gapFrequency}% of days have >2% gaps
- Average Gap Size: ${enrichedData.volatilityMetrics?.averageGapSize}%

TECHNICAL LEVELS:
- Support: ${enrichedData.technicalLevels?.support.map((s)=>`$${s.price} (strength: ${(s.strength * 100).toFixed(0)}%, last test: ${s.lastTest})`).join(', ')}
- Resistance: ${enrichedData.technicalLevels?.resistance.map((r)=>`$${r.price} (strength: ${(r.strength * 100).toFixed(0)}%, last test: ${r.lastTest})`).join(', ')}
- VWAP: $${enrichedData.technicalLevels?.vwap}
- SMA20: $${enrichedData.technicalLevels?.sma20}, SMA50: $${enrichedData.technicalLevels?.sma50}

INSTITUTIONAL RESEARCH STANDARDS - MANDATORY SPECIFICITY:

**EARNINGS & FUNDAMENTALS** (Must include exact data):
- Latest quarterly results: "Q3 2024 EPS $X.XX vs $X.XX est (+/-X.X% beat/miss), reported [DATE]"
- Revenue figures: "$XX.XB vs $XX.XB est (+/-X.X%)"
- Forward guidance: "Management raised/lowered FY2024 EPS to $X.XX-$X.XX from $X.XX-$X.XX"
- Source attribution: "Source: Company 10-Q filing, earnings call transcript"

**ANALYST COVERAGE** (Must include firm names, dates, targets):
- Recent upgrades/downgrades: "[FIRM] upgraded to [RATING] from [PRIOR], $XXX target on [DATE]"
- Price target changes: "[FIRM] raised PT to $XXX from $XXX (+/-X.X%) on [DATE]"
- Consensus data: "Street consensus: XX Buy, XX Hold, XX Sell ratings, $XXX avg PT"
- Source: "Source: Bloomberg, FactSet, company filings"

**SECTOR & MARKET DYNAMICS** (Must include exact percentages):
- Sector performance: "[SECTOR ETF] +/-X.X% vs S&P 500 +/-X.X% over [TIMEFRAME]"
- Relative strength: "Stock outperformed sector by +/-X.X% over past [X] days"
- Economic catalysts: "[ECONOMIC EVENT] on [DATE]: [SPECIFIC IMPACT] affects [STOCK] because [REASON]"
- Source: "Source: Federal Reserve, Bureau of Labor Statistics, sector ETF data"

**TECHNICAL ANALYSIS** (Must include exact levels with historical context):
- Support levels: "$XXX support tested [X] times since [DATE], held with avg volume of [X]M shares"
- Resistance levels: "$XXX resistance from [DATE] high, [X]% above current price"
- Volume analysis: "Average daily volume [X]M vs [X]M 30-day avg (+/-X.X%)"
- Volatility: "30-day IV at X.X% vs 6-month avg X.X% (+/-X.X% vs historical)"

**RISK QUANTIFICATION** (Must include probabilities and historical data):
- Gap risk: "Stock gaps >2% on X% of trading days over past 90 days"
- Event risk: "[UPCOMING EVENT] on [DATE] has X% probability of [OUTCOME] based on [SOURCE]"
- Market correlation: "Beta of X.X vs S&P 500, correlation coefficient X.X over past year"
- Downside scenarios: "If [SPECIFIC CATALYST] occurs, target downside to $XXX (-X.X%)"

EXAMPLES OF REQUIRED SPECIFICITY:
✅ REQUIRED: "MSFT reported Q1 2024 EPS $3.30 vs $3.10 est (+6.5% beat) on Oct 24, 2024. Azure revenue +29% YoY to $25.7B. Morgan Stanley raised PT to $550 from $520 on Oct 25, citing cloud acceleration. Source: Microsoft 10-Q, MS Research."

❌ FORBIDDEN: "Recent earnings were strong" or "analysts are bullish" or "sector performing well"

Provide institutional-grade research analysis in markdown format. Every statement must include specific data, dates, sources, and quantified metrics:

## 📊 TECHNICAL SETUP ANALYSIS
- Risk/Reward calculation with exact percentages
- Historical price level significance with test dates and volumes
- Technical indicator readings with specific values and timeframes
- Volume analysis vs historical averages with exact numbers

## 📈 FUNDAMENTAL CATALYSTS
- Latest earnings results with exact EPS, revenue, beat/miss percentages, and report date
- Recent analyst actions with firm names, rating changes, price targets, and dates
- Upcoming events with specific dates and probability-based impact assessments
- Sector performance vs benchmarks with exact percentages and timeframes

## ⚠️ QUANTIFIED RISK ASSESSMENT
- Volatility metrics with specific IV percentages vs historical averages
- Gap risk statistics with historical frequency data
- Event risk probabilities with specific dates and potential outcomes
- Market correlation data with beta and correlation coefficients

## 🎯 PRECISE PRICE LEVELS
- Support levels with exact prices, test dates, and volume confirmation
- Resistance levels with historical significance and percentage distances
- Moving averages with current values and trend analysis
- Volume profile levels with specific price points and significance

## ⏰ TRADE EXECUTION FRAMEWORK
- Specific entry/exit timing based on upcoming catalysts
- Position sizing recommendations based on volatility and risk metrics
- Stop-loss and profit-taking levels with mathematical justification
- Expected timeframe with market-based reasoning

## 📊 PROBABILITY-BASED OUTLOOK
- Success probability with historical precedent analysis
- Scenario analysis with specific price targets and probabilities
- Risk-adjusted return expectations with quantified metrics
- Source attribution for all data points and analysis

CRITICAL: Replace ALL generic statements with specific, verifiable data points and source citations.`;
        console.log('🤖 Making REAL OpenAI API call...');
        console.log('🔑 API Key being used:', apiKey.substring(0, 20) + '...');
        console.log('📝 Prompt length:', prompt.length);
        const completion = await openai.chat.completions.create({
            model: "gpt-4o-mini",
            messages: [
                {
                    role: "system",
                    content: "You are a senior equity research analyst at a top-tier investment bank. Provide institutional-grade analysis with MANDATORY specificity: exact dates, precise numbers, verifiable sources, probability assessments, and quantified risk metrics. Every statement must be backed by concrete data. NO generic language allowed - treat this as a research report for institutional clients who demand verifiable intelligence."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.1,
            max_tokens: 2500
        });
        console.log('✅ REAL OpenAI API call successful');
        console.log('📊 Response length:', completion.choices[0]?.message?.content?.length || 0);
        const aiResponse = completion.choices[0]?.message?.content;
        console.log('🎯 AI Response received:', aiResponse ? 'YES' : 'NO');
        console.log('📄 AI Response preview:', aiResponse?.substring(0, 200) + '...');
        if (!aiResponse) {
            throw new Error('No response from AI');
        }
        // Use the AI response directly as professional analysis
        const analysis = {
            setupExplanation: aiResponse,
            catalysts: [
                `Risk/Reward: ${((setup.targets[0] - setup.entryPrice) / (setup.entryPrice - setup.stopLoss)).toFixed(2)}:1 ratio`,
                `Entry: $${setup.entryPrice} | Stop: $${setup.stopLoss} | Target: $${setup.targets[0]}`,
                `Setup Score: ${scanResult.overallScore}/100 | Confidence: ${setup.confidence}%`
            ],
            riskAssessment: `Downside risk: ${((setup.entryPrice - setup.stopLoss) / setup.entryPrice * 100).toFixed(1)}% | Upside potential: ${((setup.targets[0] - setup.entryPrice) / setup.entryPrice * 100).toFixed(1)}%`,
            keyLevels: [
                `Entry Zone: $${setup.entryPrice}`,
                `Stop Loss: $${setup.stopLoss} (${((setup.entryPrice - setup.stopLoss) / setup.entryPrice * 100).toFixed(1)}% risk)`,
                `Target: $${setup.targets[0]} (${((setup.targets[0] - setup.entryPrice) / setup.entryPrice * 100).toFixed(1)}% reward)`
            ],
            timeframe: 'See detailed analysis for specific timeframe and catalysts',
            confidence: scanResult.overallScore
        };
        console.log('✅ Analysis object created successfully');
        const fullAnalysis = {
            symbol: scanResult.symbol,
            setupExplanation: analysis.setupExplanation || 'Setup analysis not available',
            catalysts: analysis.catalysts || [],
            riskAssessment: analysis.riskAssessment || 'Standard risks apply',
            keyLevels: analysis.keyLevels || [],
            timeframe: analysis.timeframe || '3-10 days',
            confidence: analysis.confidence || Math.round(setup.confidence),
            lastUpdated: new Date().toISOString()
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(fullAnalysis);
    } catch (error) {
        console.error('❌ AI Analysis error:', error);
        console.error('❌ Error details:', {
            message: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
            apiKeyPrefix: apiKey.substring(0, 15)
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate AI analysis',
            details: error instanceof Error ? error.message : 'Unknown error',
            apiKeyStatus: ("TURBOPACK compile-time truthy", 1) ? 'Present' : "TURBOPACK unreachable"
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__ec4c7028._.js.map
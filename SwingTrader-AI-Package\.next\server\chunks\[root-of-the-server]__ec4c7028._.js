module.exports = [
"[project]/SwingTrader-AI-Package/.next-internal/server/app/api/analysis/ai-setup/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/SwingTrader-AI-Package/src/lib/marketDataEnrichment.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * Market Data Enrichment Service
 * Provides real-time market data to enhance AI analysis with specific, quantifiable information
 */ __turbopack_context__.s([
    "MarketDataEnrichmentService",
    ()=>MarketDataEnrichmentService,
    "marketDataService",
    ()=>marketDataService
]);
class MarketDataEnrichmentService {
    /**
   * Get enriched market data for a symbol
   */ async getEnrichedData(symbol, sector) {
        // In production, this would make real API calls to data providers
        // For now, we'll simulate realistic data based on the symbol
        const enrichedData = {
            symbol,
            sector,
            recentEarnings: await this.getEarningsData(symbol),
            analystActions: await this.getAnalystActions(symbol),
            sectorPerformance: await this.getSectorPerformance(sector),
            volatilityMetrics: await this.getVolatilityData(symbol),
            newsEvents: await this.getNewsEvents(symbol),
            economicEvents: await this.getEconomicEvents(sector),
            technicalLevels: await this.getTechnicalLevels(symbol)
        };
        return enrichedData;
    }
    async getEarningsData(symbol) {
        // Simulate recent earnings data
        // In production: connect to earnings calendar API
        const earningsMap = {
            'NVDA': {
                reportDate: '2024-11-21',
                actualEPS: 2.45,
                estimatedEPS: 2.30,
                beat: true,
                beatAmount: 0.15,
                revenue: 35.1e9,
                revenueEstimate: 33.2e9,
                nextEarningsDate: '2025-02-20'
            },
            'AMD': {
                reportDate: '2024-10-29',
                actualEPS: 0.92,
                estimatedEPS: 0.88,
                beat: true,
                beatAmount: 0.04,
                revenue: 6.8e9,
                revenueEstimate: 6.7e9,
                nextEarningsDate: '2025-01-28'
            }
        };
        return earningsMap[symbol];
    }
    async getAnalystActions(symbol) {
        // Simulate recent analyst actions
        // In production: connect to analyst research APIs
        const analystMap = {
            'NVDA': [
                {
                    firm: 'Goldman Sachs',
                    action: 'upgrade',
                    rating: 'Buy',
                    priceTarget: 250,
                    previousTarget: 220,
                    date: '2024-11-22',
                    analyst: 'Toshiya Hari'
                },
                {
                    firm: 'Morgan Stanley',
                    action: 'maintain',
                    rating: 'Overweight',
                    priceTarget: 240,
                    date: '2024-11-20',
                    analyst: 'Joseph Moore'
                }
            ],
            'AMD': [
                {
                    firm: 'Bank of America',
                    action: 'upgrade',
                    rating: 'Buy',
                    priceTarget: 180,
                    previousTarget: 165,
                    date: '2024-10-30',
                    analyst: 'Vivek Arya'
                }
            ]
        };
        return analystMap[symbol] || [];
    }
    async getSectorPerformance(sector) {
        // Simulate sector performance data
        // In production: connect to sector ETF data
        const sectorMap = {
            'Technology': {
                sectorETF: 'XLK',
                sectorReturn1W: 2.8,
                sectorReturn1M: 8.5,
                spyReturn1W: 1.2,
                spyReturn1M: 4.3,
                relativeStrength: 1.23,
                ranking: 2,
                totalSectors: 11
            },
            'Materials': {
                sectorETF: 'XLB',
                sectorReturn1W: 3.2,
                sectorReturn1M: 6.8,
                spyReturn1W: 1.2,
                spyReturn1M: 4.3,
                relativeStrength: 1.58,
                ranking: 1,
                totalSectors: 11
            }
        };
        return sectorMap[sector] || {
            sectorETF: 'SPY',
            sectorReturn1W: 1.2,
            sectorReturn1M: 4.3,
            spyReturn1W: 1.2,
            spyReturn1M: 4.3,
            relativeStrength: 1.0,
            ranking: 6,
            totalSectors: 11
        };
    }
    async getVolatilityData(symbol) {
        // Simulate volatility metrics
        // In production: connect to options data providers
        return {
            impliedVolatility30d: 45,
            impliedVolatility6m: 38,
            historicalVolatility30d: 42,
            historicalVolatility6m: 35,
            gapFrequency: 15,
            averageGapSize: 3.2
        };
    }
    async getNewsEvents(symbol) {
        // Simulate recent news events
        // In production: connect to news APIs like Bloomberg, Reuters
        return [
            {
                headline: `${symbol} announces strategic partnership with major cloud provider`,
                source: 'Reuters',
                date: '2024-11-25',
                sentiment: 'positive',
                relevanceScore: 0.85
            }
        ];
    }
    async getEconomicEvents(sector) {
        // Simulate relevant economic events
        // In production: connect to economic calendar APIs
        return [
            {
                event: 'Federal Reserve Interest Rate Decision',
                date: '2024-12-18',
                impact: 'high',
                actual: undefined,
                forecast: -0.25,
                previous: 0,
                unit: 'percentage points'
            }
        ];
    }
    async getTechnicalLevels(symbol) {
        // Simulate technical analysis levels
        // In production: calculate from historical price data
        return {
            support: [
                {
                    price: 180,
                    strength: 0.85,
                    lastTest: '2024-11-15'
                },
                {
                    price: 175,
                    strength: 0.72,
                    lastTest: '2024-10-28'
                }
            ],
            resistance: [
                {
                    price: 195,
                    strength: 0.78,
                    lastTest: '2024-11-20'
                },
                {
                    price: 200,
                    strength: 0.65,
                    lastTest: '2024-11-10'
                }
            ],
            vwap: 187.5,
            sma20: 185.2,
            sma50: 178.8,
            sma200: 165.4,
            volumeProfile: [
                {
                    price: 185,
                    volume: 2500000
                },
                {
                    price: 190,
                    volume: 1800000
                }
            ]
        };
    }
}
const marketDataService = new MarketDataEnrichmentService();
}),
"[project]/SwingTrader-AI-Package/src/app/api/analysis/ai-setup/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
var __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$marketDataEnrichment$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/SwingTrader-AI-Package/src/lib/marketDataEnrichment.ts [app-route] (ecmascript)");
;
;
;
// REAL OpenAI API key - hardcoded for seamless transfer between computers
const apiKey = '********************************************************************************************************************************************************************';
console.log('🔑 Using hardcoded API Key prefix:', apiKey.substring(0, 15));
console.log('🔑 Source: Hardcoded for seamless computer transfer');
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: apiKey
});
async function POST(request) {
    try {
        console.log('🚀 AI Analysis endpoint called');
        console.log('🔑 Using hardcoded API key:', apiKey.substring(0, 15));
        console.log('🔑 API key length:', apiKey.length);
        // Parse mode from query parameters - default to FULL for comprehensive analysis
        const { searchParams } = new URL(request.url);
        const mode = (searchParams.get('mode') || 'FULL').toUpperCase();
        console.log('📊 Analysis mode:', mode);
        const { scanResult } = await request.json();
        if (!scanResult) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Scan result is required'
            }, {
                status: 400
            });
        }
        // Prepare context for AI analysis
        const setup = scanResult.overnightSetup || scanResult.breakoutSetup;
        if (!setup) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No trading setup found'
            }, {
                status: 400
            });
        }
        const strategyType = scanResult.overnightSetup ? 'overnight momentum' : 'technical breakout';
        const currentPrice = scanResult.quote?.price || setup?.currentPrice || 0;
        const changePercent = scanResult.quote?.changePercent || setup?.momentum || 0;
        // Get enriched market data for more specific analysis
        const enrichedData = await __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$src$2f$lib$2f$marketDataEnrichment$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["marketDataService"].getEnrichedData(scanResult.symbol, scanResult.sector);
        const prompt = `MODE=${mode}

You are a senior equity research analyst providing institutional-grade swing trading analysis for ${scanResult.symbol}.

MANDATORY REQUIREMENTS - Every statement must include:
1. EXACT DATES, NUMBERS, and PERCENTAGES
2. SPECIFIC SOURCE ATTRIBUTION
3. VERIFIABLE DATA POINTS
4. PROBABILITY-BASED ASSESSMENTS

MODE SWITCHING RULES:
- If MODE=SUMMARY: Return ONLY minimal fields with short text (max 25 words in setupExplanation), and OMIT long sections.
- If MODE=FULL: Return comprehensive, detailed analysis with ALL sections including technical analysis, fundamental catalysts, risk assessment, key levels, and trade execution plan. Use the complete detailed format established previously.

CURRENT SETUP DATA:
- Symbol: ${scanResult.symbol} (${scanResult.name || 'N/A'})
- Sector: ${scanResult.sector || 'Technology'}
- Strategy: ${strategyType}
- Current Price: $${currentPrice}
- Daily Change: ${changePercent.toFixed(2)}%
- Entry: $${setup.entryPrice}
- Stop Loss: $${setup.stopLoss} (Risk: ${((setup.entryPrice - setup.stopLoss) / setup.entryPrice * 100).toFixed(1)}%)
- Target: $${setup.targets[0]} (Reward: ${((setup.targets[0] - setup.entryPrice) / setup.entryPrice * 100).toFixed(1)}%)
- Risk/Reward Ratio: ${((setup.targets[0] - setup.entryPrice) / (setup.entryPrice - setup.stopLoss)).toFixed(2)}:1
- Setup Confidence: ${setup.confidence}%
- Overall Score: ${scanResult.overallScore}/100

ENRICHED MARKET DATA:
${enrichedData.recentEarnings ? `
RECENT EARNINGS:
- Report Date: ${enrichedData.recentEarnings.reportDate}
- Actual EPS: $${enrichedData.recentEarnings.actualEPS} vs Est: $${enrichedData.recentEarnings.estimatedEPS}
- Beat by: $${enrichedData.recentEarnings.beatAmount} (${enrichedData.recentEarnings.beat ? 'BEAT' : 'MISS'})
- Revenue: $${(enrichedData.recentEarnings.revenue / 1e9).toFixed(1)}B vs Est: $${(enrichedData.recentEarnings.revenueEstimate / 1e9).toFixed(1)}B
- Next Earnings: ${enrichedData.recentEarnings.nextEarningsDate}
` : ''}
${enrichedData.analystActions && enrichedData.analystActions.length > 0 ? `
RECENT ANALYST ACTIONS:
${enrichedData.analystActions.map((action)=>`- ${action.firm}: ${action.action.toUpperCase()} to ${action.rating}, $${action.priceTarget} target (${action.date})`).join('\n')}
` : ''}
SECTOR PERFORMANCE:
- Sector ETF (${enrichedData.sectorPerformance?.sectorETF}): +${enrichedData.sectorPerformance?.sectorReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.sectorReturn1M.toFixed(1)}% (1M)
- S&P 500: +${enrichedData.sectorPerformance?.spyReturn1W.toFixed(1)}% (1W), +${enrichedData.sectorPerformance?.spyReturn1M.toFixed(1)}% (1M)
- Relative Strength: ${enrichedData.sectorPerformance?.relativeStrength.toFixed(2)}x
- Sector Ranking: #${enrichedData.sectorPerformance?.ranking} of ${enrichedData.sectorPerformance?.totalSectors}

VOLATILITY METRICS:
- 30-day IV: ${enrichedData.volatilityMetrics?.impliedVolatility30d}% vs 6M avg: ${enrichedData.volatilityMetrics?.impliedVolatility6m}%
- Gap Frequency: ${enrichedData.volatilityMetrics?.gapFrequency}% of days have >2% gaps
- Average Gap Size: ${enrichedData.volatilityMetrics?.averageGapSize}%

TECHNICAL LEVELS:
- Support: ${enrichedData.technicalLevels?.support.map((s)=>`$${s.price} (strength: ${(s.strength * 100).toFixed(0)}%, last test: ${s.lastTest})`).join(', ')}
- Resistance: ${enrichedData.technicalLevels?.resistance.map((r)=>`$${r.price} (strength: ${(r.strength * 100).toFixed(0)}%, last test: ${r.lastTest})`).join(', ')}
- VWAP: $${enrichedData.technicalLevels?.vwap}
- SMA20: $${enrichedData.technicalLevels?.sma20}, SMA50: $${enrichedData.technicalLevels?.sma50}

INSTITUTIONAL RESEARCH STANDARDS - MANDATORY SPECIFICITY:

**EARNINGS & FUNDAMENTALS** (Must include exact data):
- Latest quarterly results: "Q3 2024 EPS $X.XX vs $X.XX est (+/-X.X% beat/miss), reported [DATE]"
- Revenue figures: "$XX.XB vs $XX.XB est (+/-X.X%)"
- Forward guidance: "Management raised/lowered FY2024 EPS to $X.XX-$X.XX from $X.XX-$X.XX"
- Source attribution: "Source: Company 10-Q filing, earnings call transcript"

**ANALYST COVERAGE** (Must include firm names, dates, targets):
- Recent upgrades/downgrades: "[FIRM] upgraded to [RATING] from [PRIOR], $XXX target on [DATE]"
- Price target changes: "[FIRM] raised PT to $XXX from $XXX (+/-X.X%) on [DATE]"
- Consensus data: "Street consensus: XX Buy, XX Hold, XX Sell ratings, $XXX avg PT"
- Source: "Source: Bloomberg, FactSet, company filings"

**SECTOR & MARKET DYNAMICS** (Must include exact percentages):
- Sector performance: "[SECTOR ETF] +/-X.X% vs S&P 500 +/-X.X% over [TIMEFRAME]"
- Relative strength: "Stock outperformed sector by +/-X.X% over past [X] days"
- Economic catalysts: "[ECONOMIC EVENT] on [DATE]: [SPECIFIC IMPACT] affects [STOCK] because [REASON]"
- Source: "Source: Federal Reserve, Bureau of Labor Statistics, sector ETF data"

**TECHNICAL ANALYSIS** (Must include exact levels with historical context):
- Support levels: "$XXX support tested [X] times since [DATE], held with avg volume of [X]M shares"
- Resistance levels: "$XXX resistance from [DATE] high, [X]% above current price"
- Volume analysis: "Average daily volume [X]M vs [X]M 30-day avg (+/-X.X%)"
- Volatility: "30-day IV at X.X% vs 6-month avg X.X% (+/-X.X% vs historical)"

**RISK QUANTIFICATION** (Must include probabilities and historical data):
- Gap risk: "Stock gaps >2% on X% of trading days over past 90 days"
- Event risk: "[UPCOMING EVENT] on [DATE] has X% probability of [OUTCOME] based on [SOURCE]"
- Market correlation: "Beta of X.X vs S&P 500, correlation coefficient X.X over past year"
- Downside scenarios: "If [SPECIFIC CATALYST] occurs, target downside to $XXX (-X.X%)"

EXAMPLES OF REQUIRED SPECIFICITY:
✅ REQUIRED: "MSFT reported Q1 2024 EPS $3.30 vs $3.10 est (+6.5% beat) on Oct 24, 2024. Azure revenue +29% YoY to $25.7B. Morgan Stanley raised PT to $550 from $520 on Oct 25, citing cloud acceleration. Source: Microsoft 10-Q, MS Research."

❌ FORBIDDEN: "Recent earnings were strong" or "analysts are bullish" or "sector performing well"

Provide a clean, professional research analysis with simple formatting:

[SYMBOL] Swing Trading Analysis

Executive Summary
Risk/Reward: X.XX:1 | Confidence: XX% | Target: $XXX.XX | Timeframe: X-X weeks

Current Setup Data
Metric              Value
Current Price       $XXX.XX
Stop Loss          $XXX.XX (-X.X% risk)
Target Price       $XXX.XX (+X.X% reward)
Risk/Reward Ratio  X.XX:1

Technical Analysis

Price Level Assessment
- Support: $XXX.XX tested X times since MM/DD/YYYY, average volume XXM shares
- Resistance: $XXX.XX (X.X% above current), last tested MM/DD/YYYY
- Moving Averages: SMA20 $XXX.XX, SMA50 $XXX.XX - trend direction bullish/bearish
- Volume: XXM shares vs XXM 30-day average (+/-X.X%)

Fundamental Catalysts

Recent Earnings Performance
- QX 2024 Results: EPS $X.XX vs $X.XX estimate (+/-X.X% beat/miss) on MM/DD/YYYY
- Revenue: $XXB vs $XXB estimate (+/-X.X%)
- Source: Company 10-Q filing, earnings call transcript

Analyst Coverage
- [FIRM NAME]: Upgraded to [RATING] with $XXX target on MM/DD/YYYY
- [FIRM NAME]: Raised price target to $XXX from $XXX (+X.X%) on MM/DD/YYYY
- Street Consensus: XX Buy, XX Hold, XX Sell | Average PT: $XXX

Upcoming Events
- [EVENT] on MM/DD/YYYY: XX% probability of positive impact
- [CATALYST] expected [TIMEFRAME]: potential XX% revenue impact

Risk Assessment

Volatility Analysis
- 30-day IV: XX% vs 6-month average XX% (+/-X.X%)
- Gap frequency: XX% of days show gaps >2% (past 90 days)
- Market correlation: Beta X.XX vs S&P 500

Risk Scenarios
- Downside target: $XXX (-XX%) if [specific catalyst occurs]
- Event risk: [Upcoming event] on MM/DD/YYYY has XX% probability of negative impact

Trade Execution Plan

Entry Strategy
- Entry trigger: Enter at $XXX.XX upon [specific technical/fundamental trigger]
- Position size: X% of portfolio (based on X.X% maximum risk)

Exit Strategy
- Stop loss: $XXX.XX (X.X% risk) - hard stop, no exceptions
- Target 1: $XXX.XX (X.X% reward) - take 50% profits
- Target 2: $XXX.XX (X.X% reward) - remaining position

Expected timeframe: X-X weeks based on [specific catalyst/technical pattern]

Probability Assessment

Success Metrics
- Target probability: XX% chance of reaching target within X weeks
- Risk-adjusted return: X.XX (reward-to-risk ratio)
- Historical precedent: Similar setups succeeded XX% of time over past 2 years

Key Recommendation: [Clear action statement with specific entry/exit levels]

Source Attribution: All data from [specific sources like Bloomberg, FactSet, company filings]

FORMATTING REQUIREMENTS:
- NO markdown headers (#), blockquotes (>), or backticks
- Clean section titles with plain text
- Simple table formatting without excessive markdown
- Use plain text for all prices and percentages
- Clean bullet points with simple dashes (-)
- Professional spacing for readability`;
        console.log('🤖 Making REAL OpenAI API call...');
        console.log('🔑 API Key being used:', apiKey.substring(0, 20) + '...');
        console.log('📝 Prompt length:', prompt.length);
        const completion = await openai.chat.completions.create({
            model: "gpt-4o-mini",
            messages: [
                {
                    role: "system",
                    content: "You are a senior equity research analyst creating comprehensive, detailed trading reports. Provide COMPLETE analysis with ALL sections: Executive Summary, Technical Analysis, Fundamental Catalysts, Risk Assessment, Trade Execution Plan, and Probability Assessment. Use SIMPLE TEXT FORMATTING: NO markdown headers (#), NO blockquotes (>), NO backticks around prices. Use plain text for all prices ($75.02), percentages (3.5%), and data. Every statement must include exact numbers, dates, sources, and probabilities. Write detailed, professional analysis that traders can act upon immediately."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.1,
            max_tokens: 2500
        });
        console.log('✅ REAL OpenAI API call successful');
        console.log('📊 Response length:', completion.choices[0]?.message?.content?.length || 0);
        const aiResponse = completion.choices[0]?.message?.content;
        console.log('🎯 AI Response received:', aiResponse ? 'YES' : 'NO');
        console.log('📄 AI Response preview:', aiResponse?.substring(0, 200) + '...');
        if (!aiResponse) {
            throw new Error('No response from AI');
        }
        // Parse the comprehensive AI response to extract structured data
        const analysis = {
            setupExplanation: aiResponse,
            catalysts: [
                `Risk/Reward: ${((setup.targets[0] - setup.entryPrice) / (setup.entryPrice - setup.stopLoss)).toFixed(2)}:1 ratio`,
                `Entry: $${setup.entryPrice} | Stop: $${setup.stopLoss} | Target: $${setup.targets[0]}`,
                `Setup Score: ${scanResult.overallScore}/100 | Confidence: ${setup.confidence}%`
            ],
            riskAssessment: aiResponse,
            keyLevels: [
                `Entry Zone: $${setup.entryPrice}`,
                `Stop Loss: $${setup.stopLoss} (${((setup.entryPrice - setup.stopLoss) / setup.entryPrice * 100).toFixed(1)}% risk)`,
                `Target: $${setup.targets[0]} (${((setup.targets[0] - setup.entryPrice) / setup.entryPrice * 100).toFixed(1)}% reward)`
            ],
            timeframe: 'See detailed analysis for specific timeframe and catalysts',
            confidence: scanResult.overallScore
        };
        console.log('✅ Analysis object created successfully');
        const fullAnalysis = {
            symbol: scanResult.symbol,
            setupExplanation: analysis.setupExplanation || 'Setup analysis not available',
            catalysts: analysis.catalysts || [],
            riskAssessment: analysis.riskAssessment || 'Standard risks apply',
            keyLevels: analysis.keyLevels || [],
            timeframe: analysis.timeframe || '3-10 days',
            confidence: analysis.confidence || Math.round(setup.confidence),
            lastUpdated: new Date().toISOString()
        };
        // Return short response for SUMMARY mode
        if (mode === 'SUMMARY') {
            const shortAnalysis = {
                symbol: scanResult.symbol.toUpperCase(),
                setupExplanation: (analysis.setupExplanation || '').slice(0, 200),
                entry: setup.entryPrice,
                stop: setup.stopLoss,
                target: setup.targets?.[0],
                confidence: Math.round(setup.confidence),
                timeframe: analysis.timeframe || 'overnight'
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(shortAnalysis);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(fullAnalysis);
    } catch (error) {
        console.error('❌ AI Analysis error:', error);
        console.error('❌ Error details:', {
            message: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
            apiKeyPrefix: apiKey.substring(0, 15)
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$SwingTrader$2d$AI$2d$Package$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate AI analysis',
            details: error instanceof Error ? error.message : 'Unknown error',
            apiKeyStatus: ("TURBOPACK compile-time truthy", 1) ? 'Present' : "TURBOPACK unreachable"
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__ec4c7028._.js.map
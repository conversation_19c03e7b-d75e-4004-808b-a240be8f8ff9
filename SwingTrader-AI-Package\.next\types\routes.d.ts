// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/"
type AppRouteHandlerRoutes = "/api/analysis/strategy/[symbol]" | "/api/analysis/swing/[symbol]" | "/api/scanner/full" | "/api/scanner/quick" | "/api/scanner/sector/[sector]" | "/api/scanner/strategies" | "/api/scanner/test" | "/api/stocks/historical/[symbol]" | "/api/stocks/quote/[symbol]" | "/api/stocks/search"
type PageRoutes = never
type LayoutRoutes = "/"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/": {}
  "/api/analysis/strategy/[symbol]": { "symbol": string; }
  "/api/analysis/swing/[symbol]": { "symbol": string; }
  "/api/scanner/full": {}
  "/api/scanner/quick": {}
  "/api/scanner/sector/[sector]": { "sector": string; }
  "/api/scanner/strategies": {}
  "/api/scanner/test": {}
  "/api/stocks/historical/[symbol]": { "symbol": string; }
  "/api/stocks/quote/[symbol]": { "symbol": string; }
  "/api/stocks/search": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}

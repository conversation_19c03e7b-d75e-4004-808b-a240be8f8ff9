{"name": "@alpacahq/alpaca-trade-api", "version": "3.1.3", "description": "Javascript library for the Alpaca Trade API", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=16.9", "npm": ">=6"}, "scripts": {"prepublishOnly": "tsc", "pretest": "npx tsc", "test": "mocha --timeout 4s test/init.js 'test/**/*.test.js'", "tsc:w": "tsc -w", "lint": "eslint **/*.ts"}, "repository": {"type": "git", "url": "git+https://github.com/alpacahq/alpaca-trade-api-js.git"}, "keywords": ["Alpaca", "Trading"], "contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "Apache-2.0", "bugs": {"url": "https://github.com/alpacahq/alpaca-trade-api-js/issues"}, "homepage": "https://alpaca.markets/", "dependencies": {"axios": "^0.21.1", "dotenv": "^6.2.0", "eslint": "^8.57.0", "events": "^3.2.0", "just-extend": "^4.1.0", "lodash": "^4.17.19", "minimist": "^1.2.6", "msgpack5": "^5.3.2", "nats": "^1.4.9", "urljoin": "^0.1.5", "ws": "^7.4.3"}, "devDependencies": {"@eslint/js": "^8.56.0", "@types/lodash": "^4.14.173", "@types/msgpack5": "^3.4.2", "@types/node": "^16.18.25", "@types/ws": "^8.2.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "body-parser": "^1.19.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "express": "^4.17.1", "globals": "^15.4.0", "joi": "^14.3.1", "mocha": "^9.2.2", "sinon": "^6.3.5", "typescript": "^5.4.5", "typescript-eslint": "^7.12.0"}}